<?php

namespace App\Services;

use App\Models\Assessment;
use App\Models\Answer;
use App\Models\Result;
use App\Models\User;

class AssessmentProcessingService
{
    /**
     * Process the results of an abilities assessment
     *
     * @param User $user
     * @param Assessment $assessment
     * @param array $answers
     * @return array
     */
    public function processAbilitiesAssessment(User $user, Assessment $assessment, array $answers)
    {
        // Group answers by category
        $categoryScores = [];

        // Get categories from assessment
        $categories = $assessment->categories()->with('questions')->get();

        // Calculate scores for each category
        foreach ($categories as $category) {
            $categoryName = $category->name;
            $categoryScores[$categoryName] = 0;

            // Get questions for this category
            $questionIds = $category->questions->pluck('id')->toArray();

            // Sum up scores for questions in this category
            foreach ($answers as $answer) {
                if (in_array($answer['question_id'], $questionIds) && isset($answer['answer_value'])) {
                    $categoryScores[$categoryName] += $answer['answer_value'];
                }
            }
        }

        // Sort scores from highest to lowest
        arsort($categoryScores);

        // Get top abilities (highest score)
        $topAbilities = [array_key_first($categoryScores)];

        // Get complementary abilities (score > 35)
        $complementaryAbilities = [];
        foreach ($categoryScores as $category => $score) {
            if ($score > 35 && !in_array($category, $topAbilities)) {
                $complementaryAbilities[] = $category;
            }
        }

        // Get development areas (score < 26)
        $developmentAreas = [];
        foreach ($categoryScores as $category => $score) {
            if ($score < 26) {
                $developmentAreas[] = $category;
            }
        }

        // Determine strength profile
        $strengthProfile = "Generalist";
        $strengthProfiles = [
            "Cognitive+Technical" => "Problem Solver",
            "Social+Management" => "Team Leader",
            "Creative+Physical" => "Performer",
            "Technical+Management" => "Project Expert",
            "Social+Creative" => "Creative Communicator"
        ];

        if (count($topAbilities) > 0 && count($complementaryAbilities) > 0) {
            $topCategory = explode(' ', $topAbilities[0])[0];
            $compCategory = explode(' ', $complementaryAbilities[0])[0];
            $profileKey = $topCategory . '+' . $compCategory;

            if (isset($strengthProfiles[$profileKey])) {
                $strengthProfile = $strengthProfiles[$profileKey];
            } else {
                $strengthProfile = "Specialized Generalist";
            }
        }

        // Return processed results
        return [
            'top_abilities' => $topAbilities,
            'complementary_abilities' => $complementaryAbilities,
            'development_areas' => $developmentAreas,
            'strength_profile' => $strengthProfile,
            'category_scores' => $categoryScores
        ];
    }

    /**
     * Process the results of a heart assessment
     *
     * @param User $user
     * @param Assessment $assessment
     * @param array $answers
     * @return array
     */
    public function processHeartAssessment(User $user, Assessment $assessment, array $answers)
    {
        // Group answers by category
        $categoryScores = [];

        // Get categories from assessment
        $categories = $assessment->categories()->with('questions')->get();

        // Calculate scores for each category
        foreach ($categories as $category) {
            $categoryName = $category->name;
            $categoryScores[$categoryName] = 0;

            // Get questions for this category
            $questionIds = $category->questions->pluck('id')->toArray();

            // Sum up scores for questions in this category
            foreach ($answers as $answer) {
                if (in_array($answer['question_id'], $questionIds) && isset($answer['answer_value'])) {
                    $categoryScores[$categoryName] += $answer['answer_value'];
                }
            }
        }

        // Sort scores from highest to lowest
        arsort($categoryScores);

        // Get keys as array
        $categoryKeys = array_keys($categoryScores);

        // Get top 2 interests
        $topInterests = array_slice($categoryKeys, 0, 2);

        // Identify synergy (scores within 3 points)
        $synergyPairs = [];
        if (
            isset($categoryScores[$categoryKeys[0]]) &&
            isset($categoryScores[$categoryKeys[1]]) &&
            ($categoryScores[$categoryKeys[0]] - $categoryScores[$categoryKeys[1]] <= 3)
        ) {
            $synergyPairs[] = $categoryKeys[0] . ' & ' . $categoryKeys[1];
        }

        // Engagement recommendations
        $engagementMap = [
            "Art & Entertainment" => ["Komunitas Seni", "Manajemen Acara Budaya", "Produksi Kreatif"],
            "Business" => ["Startup Weekend", "Konsultasi Bisnis UMKM", "Pengembangan Produk"],
            "Communication / Media" => ["Podcast", "Penulisan Opini", "Manajemen Media Sosial"],
            "Family" => ["Seminar Parenting", "Konseling Keluarga", "Acara Ikatan Keluarga"],
            "Religion" => ["Retret Spiritual", "Diskusi Lintas Iman", "Pelayanan Komunitas"],
            "Education" => ["Pengajaran Sukarela", "Pengembangan Kurikulum", "Program Literasi"],
            "Government" => ["Forum Kebijakan Publik", "Advokasi Masyarakat", "Pengawasan Pemilu"]
        ];

        $recommendedEngagements = [];
        foreach ($topInterests as $interest) {
            if (isset($engagementMap[$interest])) {
                foreach ($engagementMap[$interest] as $activity) {
                    if (!in_array($activity, $recommendedEngagements)) {
                        $recommendedEngagements[] = $activity;
                    }
                }
            }
        }

        // Generate passion profile
        $profileLabels = [
            "Art & Entertainment" => "Kreator",
            "Business" => "Pengusaha",
            "Communication / Media" => "Komunikator",
            "Family" => "Perajut Keluarga",
            "Religion" => "Pencari Makna",
            "Education" => "Pembangun Pengetahuan",
            "Government" => "Pelayan Publik"
        ];

        $passionProfile = "";
        if (count($synergyPairs) > 0) {
            $passionProfile = $profileLabels[$topInterests[0]] . '-' . $profileLabels[$topInterests[1]];
        } else {
            $passionProfile = $profileLabels[$topInterests[0]];
        }

        // Return processed results
        return [
            'top_interests' => $topInterests,
            'synergy_pairs' => $synergyPairs,
            'recommended_engagements' => $recommendedEngagements,
            'passion_profile' => $passionProfile,
            'category_scores' => $categoryScores
        ];
    }

    /**
     * Process the results of an experience assessment
     *
     * @param User $user
     * @param Assessment $assessment
     * @param array $answers
     * @return array
     */
    public function processExperienceAssessment(User $user, Assessment $assessment, array $answers)
    {
        // Define experience patterns based on guidance profiles
        $experiencePatterns = [
            'Leadership Journey' => [
                'keywords' => ['memimpin', 'tim', 'proyek', 'keputusan', 'tanggung jawab', 'koordinasi', 'delegasi', 'motivasi'],
                'indicators' => ['leadership', 'management', 'team', 'project', 'decision']
            ],
            'Learning Odyssey' => [
                'keywords' => ['belajar', 'kursus', 'pelatihan', 'sertifikat', 'skill', 'pengetahuan', 'pengembangan'],
                'indicators' => ['learning', 'course', 'training', 'education', 'skill', 'knowledge']
            ],
            'Life Catalyst' => [
                'keywords' => ['perubahan', 'transformasi', 'tantangan', 'krisis', 'breakthrough', 'turning point'],
                'indicators' => ['change', 'transformation', 'challenge', 'crisis', 'breakthrough']
            ],
            'Professional Evolution' => [
                'keywords' => ['karir', 'promosi', 'industri', 'profesional', 'expertise', 'spesialisasi'],
                'indicators' => ['career', 'promotion', 'industry', 'professional', 'expertise', 'specialization']
            ]
        ];

        // Analyze answers to determine experience pattern
        $patternScores = [];
        foreach ($experiencePatterns as $pattern => $config) {
            $patternScores[$pattern] = 0;
        }

        foreach ($answers as $answer) {
            if (isset($answer['answer_text'])) {
                $text = strtolower($answer['answer_text']);

                foreach ($experiencePatterns as $pattern => $config) {
                    $score = 0;

                    // Check Indonesian keywords
                    foreach ($config['keywords'] as $keyword) {
                        if (strpos($text, $keyword) !== false) {
                            $score += 2;
                        }
                    }

                    // Check English indicators
                    foreach ($config['indicators'] as $indicator) {
                        if (strpos($text, $indicator) !== false) {
                            $score += 1;
                        }
                    }

                    $patternScores[$pattern] += $score;
                }
            }
        }

        // Determine primary experience pattern
        arsort($patternScores);
        $primaryPattern = array_key_first($patternScores);
        $primaryScore = $patternScores[$primaryPattern];

        // If no clear pattern emerges, default to Learning Odyssey
        if ($primaryScore < 3) {
            $primaryPattern = 'Learning Odyssey';
        }

        // Extract competencies from narrative answers
        $competencyPatterns = [];
        foreach ($answers as $answer) {
            if (isset($answer['answer_text'])) {
                $keywords = $this->extractKeywords($answer['answer_text']);
                foreach ($keywords as $skill) {
                    $competencyPatterns[$skill] = ($competencyPatterns[$skill] ?? 0) + 1;
                }
            }
        }

        // Identify core competencies (mentioned multiple times)
        $coreCompetencies = [];
        foreach ($competencyPatterns as $skill => $count) {
            if ($count > 1) { // Lowered threshold
                $coreCompetencies[] = $skill;
            }
        }

        // Analyze values in narratives
        $valueKeywords = ['integritas', 'kreativitas', 'kolaborasi', 'keadilan', 'pertumbuhan'];
        $valueFrequency = [];

        foreach ($answers as $answer) {
            if (isset($answer['answer_text'])) {
                $narrative = strtolower($answer['answer_text']);
                foreach ($valueKeywords as $value) {
                    if (strpos($narrative, $value) !== false) {
                        $valueFrequency[$value] = ($valueFrequency[$value] ?? 0) + 1;
                    }
                }
            }
        }

        // Get the most frequent value
        $signatureValue = '';
        if (!empty($valueFrequency)) {
            arsort($valueFrequency);
            $signatureValue = ucfirst(array_key_first($valueFrequency));
        } else {
            $signatureValue = 'Pertumbuhan';
        }

        // Map transformative events
        $transformationMap = [];
        foreach ($answers as $answer) {
            if (isset($answer['subprompt_answers']) && isset($answer['answer_text'])) {
                $impactLevel = $this->assessNarrativeImpact($answer['answer_text']);
                if ($impactLevel >= 3) { // Lowered threshold
                    $subprompts = $answer['subprompt_answers'];
                    $transformationMap[] = [
                        'event' => $subprompts['title'] ?? 'Transformative Event',
                        'year' => $subprompts['year'] ?? date('Y'),
                        'beforeAfter' => $subprompts['transformation'] ?? $answer['answer_text']
                    ];
                }
            }
        }

        // Create future blueprint
        $futureBlueprint = [
            'leverage' => array_slice($coreCompetencies, 0, 3),
            'explore' => []
        ];

        // Return processed results with primary pattern
        return [
            'primary_pattern' => $primaryPattern,
            'experience_pattern' => $primaryPattern, // For backward compatibility
            'pattern_scores' => $patternScores,
            'core_competencies' => $coreCompetencies,
            'values_signature' => $signatureValue,
            'transformation_map' => $transformationMap,
            'future_blueprint' => $futureBlueprint
        ];
    }

    /**
     * Process the results of a strength assessment
     *
     * @param User $user
     * @param Assessment $assessment
     * @param array $answers
     * @return array
     */
    public function processStrengthAssessment(User $user, Assessment $assessment, array $answers)
    {
        // Group answers by category
        $categoryScores = [];

        // Get categories from assessment
        $categories = $assessment->categories()->with('questions')->get();

        // Calculate scores for each category
        foreach ($categories as $category) {
            $categoryName = $category->name;
            $categoryScores[$categoryName] = 0;

            // Get questions for this category
            $questionIds = $category->questions->pluck('id')->toArray();

            // Sum up scores for questions in this category
            foreach ($answers as $answer) {
                if (in_array($answer['question_id'], $questionIds) && isset($answer['answer_value'])) {
                    $categoryScores[$categoryName] += $answer['answer_value'];
                }
            }
        }

        // Sort scores from highest to lowest
        arsort($categoryScores);

        // Get top two strengths
        $topStrengths = array_slice(array_keys($categoryScores), 0, 2);

        // Assign strength level based on score ranges
        $strengthLevels = [];
        foreach ($categoryScores as $category => $score) {
            if ($score >= 36) {
                $strengthLevels[$category] = 'Tinggi';
            } elseif ($score >= 21) {
                $strengthLevels[$category] = 'Moderat';
            } else {
                $strengthLevels[$category] = 'Rendah';
            }
        }

        // Determine strength pair profile
        $strengthPair = "";
        if (count($topStrengths) >= 2) {
            $strengthPairs = [
                "Visionary Pioneers+Insightful Truth-Seekers" => "Inovator Sistem",
                "Inspiring Connectors+Supportive Nurturers" => "Pengembang Komunitas",
                "Clarifying Mentors+Insightful Truth-Seekers" => "Ahli Standar Profesional",
                "Visionary Pioneers+Inspiring Connectors" => "Pembuka Peluang",
                "Supportive Nurturers+Clarifying Mentors" => "Pelatih Pengembangan"
            ];

            $pairKey = $topStrengths[0] . "+" . $topStrengths[1];

            if (isset($strengthPairs[$pairKey])) {
                $strengthPair = $strengthPairs[$pairKey];
            } else {
                $reversePairKey = $topStrengths[1] . "+" . $topStrengths[0];
                if (isset($strengthPairs[$reversePairKey])) {
                    $strengthPair = $strengthPairs[$reversePairKey];
                } else {
                    $strengthPair = "Kombinasi Unik";
                }
            }
        }

        // Return processed results
        return [
            'top_strengths' => $topStrengths,
            'strength_levels' => $strengthLevels,
            'strength_pair' => $strengthPair,
            'category_scores' => $categoryScores
        ];
    }

    /**
     * Process the results of a personality assessment
     *
     * @param User $user
     * @param Assessment $assessment
     * @param array $answers
     * @return array
     */
    public function processPersonalitiesAssessment(User $user, Assessment $assessment, array $answers)
    {
        // Group answers by category/dimension
        $dimensionScores = [];

        // Get categories from assessment
        $categories = $assessment->categories()->with('questions')->get();

        // Calculate scores for each dimension
        foreach ($categories as $category) {
            $dimensionName = $category->name;
            $dimensionScores[$dimensionName] = 0;

            // Get questions for this dimension
            $questionIds = $category->questions->pluck('id')->toArray();

            // Sum up scores for questions in this dimension
            foreach ($answers as $answer) {
                if (in_array($answer['question_id'], $questionIds) && isset($answer['answer_value'])) {
                    $dimensionScores[$dimensionName] += $answer['answer_value'];
                }
            }
        }

        // Sort scores from highest to lowest
        arsort($dimensionScores);

        // Get primary and secondary personality types
        $dimensionNames = array_keys($dimensionScores);
        $primaryType = $dimensionNames[0] ?? '';
        $secondaryType = $dimensionNames[1] ?? '';

        // Create personality code (e.g., DL-IC)
        $dimensionCodes = [
            'Decisive Leader' => 'DL',
            'Influential Communicator' => 'IC',
            'Precision Analyst' => 'PA',
            'Steady Supporter' => 'SS'
        ];

        $personalityCode = "";
        if (!empty($primaryType) && isset($dimensionCodes[$primaryType])) {
            $personalityCode = $dimensionCodes[$primaryType];

            if (!empty($secondaryType) && isset($dimensionCodes[$secondaryType])) {
                $personalityCode .= '-' . $dimensionCodes[$secondaryType];
            }
        }

        // Determine personality profile
        $personalityProfiles = [
            'VD-DC' => 'Inspirational Leader',
            'VD-AP' => 'Strategic Innovator',
            'VD-SS' => 'Decisive Implementer',
            'DC-VD' => 'Charismatic Influencer',
            'DC-AP' => 'Engaging Educator',
            'DC-SS' => 'Team Harmonizer',
            'AP-VD' => 'Visionary Analyst',
            'AP-DC' => 'Articulate Specialist',
            'AP-SS' => 'Methodical Consultant',
            'SS-VD' => 'Reliable Executor',
            'SS-DC' => 'Supportive Coordinator',
            'SS-AP' => 'Detail-Oriented Guardian'
        ];

        $personalityProfile = $personalityProfiles[$personalityCode] ?? 'Balanced Profile';

        // Generate development recommendations based on profile
        $developmentRecommendations = $this->getPersonalityDevelopmentRecommendations($primaryType, $secondaryType);

        // Return processed results
        return [
            'primary_type' => $primaryType,
            'secondary_type' => $secondaryType,
            'personality_code' => $personalityCode,
            'personality_profile' => $personalityProfile,
            'development_recommendations' => $developmentRecommendations,
            'dimension_scores' => $dimensionScores
        ];
    }

    /**
     * Get personalized development recommendations based on personality types
     *
     * @param string $primaryType
     * @param string $secondaryType
     * @return array
     */
    private function getPersonalityDevelopmentRecommendations($primaryType, $secondaryType)
    {
        $recommendations = [];

        $developmentAreas = [
            'Visionary Driver' => [
                'Patient Listening' => 'Praktek mendengar aktif 10 menit sehari',
                'Detail Management' => 'Gunakan sistem cek ulang untuk detail penting',
                'Empathetic Response' => 'Latihan refleksi perasaan dalam komunikasi'
            ],
            'Dynamic Connector' => [
                'Focus Maintenance' => 'Gunakan teknik Pomodoro (25 menit fokus)',
                'Data-Based Decisions' => 'Buat template analisis data sederhana',
                'Task Completion' => 'Implementasikan sistem "finish-to-start"'
            ],
            'Analytical Processor' => [
                'Quick Decision-Making' => 'Latihan keputusan dengan batas waktu 5 menit',
                'Social Engagement' => 'Jadwalkan networking mingguan dengan 2 orang baru',
                'Emotional Expression' => 'Jurnal perasaan dan refleksi harian'
            ],
            'Supportive Stabilizer' => [
                'Change Management' => 'Ikuti workshop "Adapting to Change"',
                'Quick Decision-Making' => 'Gunakan timer 5 menit untuk keputusan rutin',
                'Assertiveness' => 'Latihan menyampaikan pendapat di awal rapat'
            ]
        ];

        // Add primary type recommendations
        if (!empty($primaryType) && isset($developmentAreas[$primaryType])) {
            foreach ($developmentAreas[$primaryType] as $area => $recommendation) {
                $recommendations[] = [
                    'area' => $area,
                    'activity' => $recommendation
                ];
            }
        }

        return $recommendations;
    }

    /**
     * Extract keywords from text (simplified version)
     *
     * @param string $text
     * @return array
     */
    private function extractKeywords($text)
    {
        // This is a simplified implementation
        // In a real app, you would use NLP or a more sophisticated algorithm
        $commonSkills = [
            'leadership',
            'management',
            'communication',
            'programming',
            'analysis',
            'design',
            'creativity',
            'problem solving',
            'teamwork',
            'organization',
            'kepemimpinan',
            'manajemen',
            'komunikasi',
            'pemrograman',
            'analisis',
            'desain',
            'kreativitas',
            'pemecahan masalah',
            'kerja tim',
            'organisasi'
        ];

        $text = strtolower($text);
        $foundSkills = [];

        foreach ($commonSkills as $skill) {
            if (strpos($text, $skill) !== false) {
                $foundSkills[] = $skill;
            }
        }

        return $foundSkills;
    }

    /**
     * Assess the impact level of a narrative (simplified)
     *
     * @param string $text
     * @return int
     */
    private function assessNarrativeImpact($text)
    {
        // This is a simplified implementation
        $impactWords = [
            'transformative' => 5,
            'life-changing' => 5,
            'revolutionary' => 5,
            'significant' => 4,
            'profound' => 4,
            'substantial' => 4,
            'moderate' => 3,
            'notable' => 3,
            'meaningful' => 3,
            'minor' => 2,
            'small' => 2,
            'slight' => 2,
            'minimal' => 1,
            'negligible' => 1,
            'trivial' => 1,
            'mengubah hidup' => 5,
            'revolusioner' => 5,
            'transformatif' => 5,
            'signifikan' => 4,
            'mendalam' => 4,
            'substansial' => 4,
            'moderat' => 3,
            'bermakna' => 3,
            'berarti' => 3,
            'kecil' => 2,
            'ringan' => 2,
            'sedikit' => 2,
            'minimal' => 1,
            'tidak berarti' => 1,
            'sepele' => 1
        ];

        $text = strtolower($text);
        $maxImpact = 0;

        foreach ($impactWords as $word => $impact) {
            if (strpos($text, $word) !== false && $impact > $maxImpact) {
                $maxImpact = $impact;
            }
        }

        // Set a minimum impact of 1 if no impact words found
        return max($maxImpact, 1);
    }
}
