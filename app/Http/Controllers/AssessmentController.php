<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Assessment;
use App\Models\Answer;
use App\Models\Result;
use App\Services\AssessmentProcessingService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AssessmentController extends Controller
{
    protected $assessmentService;

    public function __construct(AssessmentProcessingService $assessmentService)
    {
        $this->assessmentService = $assessmentService;
    }

    /**
     * Get all available assessments
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $assessments = Assessment::where('active', true)->get();
        return response()->json(['assessments' => $assessments]);
    }

    /**
     * Get a specific assessment with its categories and questions
     *
     * @param string $slug
     * @return \Illuminate\Http\Response
     */
    public function show($slug)
    {
        $assessment = Assessment::where('slug', $slug)
            ->with(['categories.questions' => function ($query) {
                $query->orderBy('order');
            }])
            ->firstOrFail();

        return response()->json(['assessment' => $assessment]);
    }

    /**
     * Submit answers for an assessment and process results
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function submitAssessment(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'answers' => 'required|array',
            'answers.*.question_id' => 'required|exists:questions,id',
            'answers.*.answer_value' => 'nullable|integer|min:1|max:5',
            'answers.*.answer_text' => 'nullable|string',
            'answers.*.subprompt_answers' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();
        $assessment = Assessment::findOrFail($id);

        // Save answers
        foreach ($request->answers as $answerData) {
            Answer::create([
                'user_id' => $user->id,
                'assessment_id' => $assessment->id,
                'question_id' => $answerData['question_id'],
                'answer_value' => $answerData['answer_value'] ?? null,
                'answer_text' => $answerData['answer_text'] ?? null,
                'subprompt_answers' => $answerData['subprompt_answers'] ?? null,
            ]);
        }

        // Process answers based on assessment type
        $processedResults = $this->processAssessmentResults($user, $assessment, $request->answers);

        // Save results
        $result = Result::create([
            'user_id' => $user->id,
            'assessment_id' => $assessment->id,
            'raw_scores' => json_encode($request->answers),
            'processed_results' => json_encode($processedResults),
            'category_scores' => json_encode($processedResults['category_scores'] ?? null),
            'completed_at' => now(),
        ]);

        return response()->json([
            'message' => 'Assessment submitted successfully',
            'result' => $result,
            'processed_results' => $processedResults,
        ]);
    }

    /**
     * Process assessment results based on assessment type
     *
     * @param \App\Models\User $user
     * @param \App\Models\Assessment $assessment
     * @param array $answers
     * @return array
     */
    private function processAssessmentResults($user, $assessment, $answers)
    {
        switch ($assessment->type) {
            case 'abilities':
                return $this->assessmentService->processAbilitiesAssessment($user, $assessment, $answers);
            case 'heart':
                return $this->assessmentService->processHeartAssessment($user, $assessment, $answers);
            case 'strength':
                return $this->assessmentService->processStrengthAssessment($user, $assessment, $answers);
            case 'personalities':
                return $this->assessmentService->processPersonalitiesAssessment($user, $assessment, $answers);
            case 'experience':
                return $this->assessmentService->processExperienceAssessment($user, $assessment, $answers);
            default:
                throw new \Exception("Unsupported assessment type: {$assessment->type}");
        }
    }

    /**
     * Get assessment results for the current user
     *
     * @param Request $request
     * @param string $slug
     * @return \Illuminate\Http\Response
     */
    public function getResults(Request $request, $slug)
    {
        $user = Auth::user();
        $assessment = Assessment::where('slug', $slug)->firstOrFail();

        $result = Result::where('user_id', $user->id)
            ->where('assessment_id', $assessment->id)
            ->orderBy('completed_at', 'desc')
            ->first();

        if (!$result) {
            return response()->json(['message' => 'No results found for this assessment'], 404);
        }

        return response()->json([
            'result' => $result,
            'processed_results' => json_decode($result->processed_results),
        ]);
    }

    /**
     * Get all assessment results for the current user
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function getAllResults(Request $request)
    {
        $user = Auth::user();

        $results = Result::where('user_id', $user->id)
            ->with('assessment')
            ->orderBy('completed_at', 'desc')
            ->get();

        if ($results->isEmpty()) {
            return response()->json(['message' => 'No assessment results found'], 404);
        }

        $formattedResults = $results->map(function ($result) {
            return [
                'id' => $result->id,
                'assessment_id' => $result->assessment_id,
                'assessment_name' => $result->assessment->name,
                'assessment_slug' => $result->assessment->slug,
                'completed_at' => $result->completed_at,
                'processed_results' => json_decode($result->processed_results),
            ];
        });

        return response()->json([
            'results' => $formattedResults
        ]);
    }
}
