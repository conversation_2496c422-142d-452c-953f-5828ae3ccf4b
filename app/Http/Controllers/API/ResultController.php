<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Assessment;
use App\Models\Answer;
use App\Models\Result;
use App\Services\AssessmentProcessingService;
use Carbon\Carbon;

class ResultController extends Controller
{
    protected $assessmentProcessingService;

    public function __construct(AssessmentProcessingService $assessmentProcessingService)
    {
        $this->assessmentProcessingService = $assessmentProcessingService;
    }

    /**
     * Process assessment results
     * 
     * @param Request $request
     * @param int $assessmentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function processResults(Request $request, $assessmentId)
    {
        $assessment = Assessment::findOrFail($assessmentId);
        $user = $request->user();

        // Get user's answers for this assessment
        $answers = Answer::where('user_id', $user->id)
            ->where('assessment_id', $assessmentId)
            ->get()
            ->toArray();

        if (count($answers) === 0) {
            return response()->json([
                'message' => 'No answers found for this assessment'
            ], 404);
        }

        $processedResults = [];
        $rawScores = [];
        $categoryScores = [];

        // Process results based on assessment type
        switch ($assessment->type) {
            case 'abilities':
                $processedResults = $this->assessmentProcessingService->processAbilitiesAssessment($user, $assessment, $answers);
                $categoryScores = $processedResults['category_scores'] ?? [];
                unset($processedResults['category_scores']);
                break;

            case 'heart':
                $processedResults = $this->assessmentProcessingService->processHeartAssessment($user, $assessment, $answers);
                $categoryScores = $processedResults['category_scores'] ?? [];
                unset($processedResults['category_scores']);
                break;

            case 'strength':
                $processedResults = $this->assessmentProcessingService->processStrengthAssessment($user, $assessment, $answers);
                $categoryScores = $processedResults['category_scores'] ?? [];
                unset($processedResults['category_scores']);
                break;

            case 'personalities':
                $processedResults = $this->assessmentProcessingService->processPersonalitiesAssessment($user, $assessment, $answers);
                $categoryScores = $processedResults['dimension_scores'] ?? [];
                unset($processedResults['dimension_scores']);
                break;

            case 'experience':
                $processedResults = $this->assessmentProcessingService->processExperienceAssessment($user, $assessment, $answers);
                break;

            default:
                return response()->json([
                    'message' => 'Unsupported assessment type: ' . $assessment->type
                ], 400);
        }

        // Check if result already exists
        $existingResult = Result::where('user_id', $user->id)
            ->where('assessment_id', $assessmentId)
            ->first();

        if ($existingResult) {
            $existingResult->update([
                'raw_scores' => $answers,
                'processed_results' => $processedResults,
                'category_scores' => $categoryScores,
                'completed_at' => Carbon::now()
            ]);

            $result = $existingResult;
        } else {
            $result = Result::create([
                'user_id' => $user->id,
                'assessment_id' => $assessmentId,
                'raw_scores' => $answers,
                'processed_results' => $processedResults,
                'category_scores' => $categoryScores,
                'completed_at' => Carbon::now()
            ]);
        }

        return response()->json([
            'message' => 'Results processed successfully',
            'data' => [
                'result_id' => $result->id,
                'assessment_type' => $assessment->type,
                'assessment_name' => $assessment->name,
                'results' => $processedResults,
                'category_scores' => $categoryScores
            ]
        ]);
    }

    /**
     * Get user's results for an assessment
     * 
     * @param Request $request
     * @param int $assessmentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserResults(Request $request, $assessmentId)
    {
        $result = Result::where('user_id', $request->user()->id)
            ->where('assessment_id', $assessmentId)
            ->first();

        if (!$result) {
            return response()->json([
                'message' => 'No results found for this assessment'
            ], 404);
        }

        $assessment = Assessment::findOrFail($assessmentId);

        return response()->json([
            'data' => [
                'result_id' => $result->id,
                'assessment_type' => $assessment->type,
                'assessment_name' => $assessment->name,
                'results' => $result->processed_results,
                'category_scores' => $result->category_scores,
                'completed_at' => $result->completed_at
            ]
        ]);
    }

    /**
     * Get all results for a user
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllUserResults(Request $request)
    {
        $results = Result::where('user_id', $request->user()->id)
            ->with('assessment:id,name,type,icon')
            ->get();

        return response()->json([
            'data' => $results
        ]);
    }
}
