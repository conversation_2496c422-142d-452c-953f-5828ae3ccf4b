<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Answer;
use App\Models\Assessment;
use App\Models\Question;
use App\Services\AssessmentProcessingService;

class AnswerController extends Controller
{
    protected $assessmentProcessingService;

    public function __construct(AssessmentProcessingService $assessmentProcessingService)
    {
        $this->assessmentProcessingService = $assessmentProcessingService;
    }

    /**
     * Submit an answer for a question
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitAnswer(Request $request)
    {
        $request->validate([
            'assessment_id' => 'required|exists:assessments,id',
            'question_id' => 'required|exists:questions,id',
            'answer_text' => 'nullable|string',
            'answer_value' => 'nullable|integer|min:1|max:5',
            'subprompt_answers' => 'nullable|json',
        ]);

        $question = Question::findOrFail($request->question_id);

        // Check if answer type matches question type
        if ($question->question_type === 'rating' && !$request->has('answer_value')) {
            return response()->json([
                'message' => 'Rating questions require an answer_value'
            ], 422);
        }

        if ($question->question_type === 'narrative' && !$request->has('answer_text')) {
            return response()->json([
                'message' => 'Narrative questions require answer_text'
            ], 422);
        }

        // Check if user already answered this question
        $existingAnswer = Answer::where('user_id', $request->user()->id)
            ->where('question_id', $request->question_id)
            ->first();

        if ($existingAnswer) {
            $existingAnswer->update([
                'answer_text' => $request->answer_text,
                'answer_value' => $request->answer_value,
                'subprompt_answers' => $request->subprompt_answers,
            ]);

            $answer = $existingAnswer;
        } else {
            $answer = Answer::create([
                'user_id' => $request->user()->id,
                'assessment_id' => $request->assessment_id,
                'question_id' => $request->question_id,
                'answer_text' => $request->answer_text,
                'answer_value' => $request->answer_value,
                'subprompt_answers' => $request->subprompt_answers,
            ]);
        }

        return response()->json([
            'message' => 'Answer submitted successfully',
            'data' => $answer
        ]);
    }

    /**
     * Submit multiple answers in one request
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitMultipleAnswers(Request $request)
    {
        $request->validate([
            'assessment_id' => 'required|exists:assessments,id',
            'answers' => 'required|array',
            'answers.*.question_id' => 'required|exists:questions,id',
            'answers.*.answer_text' => 'nullable|string',
            'answers.*.answer_value' => 'nullable|integer|min:1|max:5',
            'answers.*.subprompt_answers' => 'nullable|json',
        ]);

        $savedAnswers = [];

        foreach ($request->answers as $answerData) {
            $question = Question::findOrFail($answerData['question_id']);

            // Check if answer type matches question type
            if ($question->question_type === 'rating' && !isset($answerData['answer_value'])) {
                continue; // Skip invalid answer
            }

            if ($question->question_type === 'narrative' && !isset($answerData['answer_text'])) {
                continue; // Skip invalid answer
            }

            // Check if user already answered this question
            $existingAnswer = Answer::where('user_id', $request->user()->id)
                ->where('question_id', $answerData['question_id'])
                ->first();

            if ($existingAnswer) {
                $existingAnswer->update([
                    'answer_text' => $answerData['answer_text'] ?? null,
                    'answer_value' => $answerData['answer_value'] ?? null,
                    'subprompt_answers' => $answerData['subprompt_answers'] ?? null,
                ]);

                $savedAnswers[] = $existingAnswer;
            } else {
                $answer = Answer::create([
                    'user_id' => $request->user()->id,
                    'assessment_id' => $request->assessment_id,
                    'question_id' => $answerData['question_id'],
                    'answer_text' => $answerData['answer_text'] ?? null,
                    'answer_value' => $answerData['answer_value'] ?? null,
                    'subprompt_answers' => $answerData['subprompt_answers'] ?? null,
                ]);

                $savedAnswers[] = $answer;
            }
        }

        return response()->json([
            'message' => count($savedAnswers) . ' answers submitted successfully',
            'data' => $savedAnswers
        ]);
    }

    /**
     * Get answers by user and assessment
     * 
     * @param Request $request
     * @param int $assessmentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserAnswers(Request $request, $assessmentId)
    {
        $answers = Answer::where('user_id', $request->user()->id)
            ->where('assessment_id', $assessmentId)
            ->get();

        return response()->json([
            'data' => $answers
        ]);
    }
}
