<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\GuidanceContent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class GuidanceController extends Controller
{
    /**
     * Get guidance content by area
     */
    public function getByArea(string $area): JsonResponse
    {
        try {
            $profiles = GuidanceContent::getProfilesByArea($area);
            
            return response()->json([
                'success' => true,
                'data' => $profiles,
                'message' => "Guidance content for {$area} retrieved successfully"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve guidance content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific guidance content by area and profile
     */
    public function getByAreaAndProfile(string $area, string $profileName): JsonResponse
    {
        try {
            $content = GuidanceContent::getByAreaAndProfile($area, $profileName);
            
            if (!$content) {
                return response()->json([
                    'success' => false,
                    'message' => 'Guidance content not found'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => $content,
                'message' => 'Guidance content retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve guidance content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get guidance content by score
     */
    public function getByScore(string $area, int $score): JsonResponse
    {
        try {
            $content = GuidanceContent::getProfileByScore($area, $score);
            
            if (!$content) {
                return response()->json([
                    'success' => false,
                    'message' => 'No guidance content found for the given score'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => $content,
                'message' => 'Guidance content retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve guidance content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get comprehensive guidance for all areas based on assessment results
     */
    public function getComprehensiveGuidance(Request $request): JsonResponse
    {
        try {
            $results = $request->input('results', []);
            $guidance = [];
            
            foreach ($results as $area => $result) {
                if (isset($result['primary']) && isset($result['score'])) {
                    $content = GuidanceContent::getByAreaAndProfile($area, $result['primary']);
                    if ($content) {
                        $guidance[$area] = $content;
                    }
                }
            }
            
            return response()->json([
                'success' => true,
                'data' => $guidance,
                'message' => 'Comprehensive guidance retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve comprehensive guidance',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all areas and their profiles for overview
     */
    public function getAllAreas(): JsonResponse
    {
        try {
            $areas = ['strength', 'heart', 'abilities', 'personality', 'experience'];
            $allContent = [];
            
            foreach ($areas as $area) {
                $profiles = GuidanceContent::getProfilesByArea($area);
                $allContent[$area] = $profiles;
            }
            
            return response()->json([
                'success' => true,
                'data' => $allContent,
                'message' => 'All guidance content retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve all guidance content',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
