<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Assessment;
use App\Models\Question;
use App\Models\AssessmentCategory;

class QuestionController extends Controller
{
    /**
     * Get questions by assessment ID
     * 
     * @param int $assessmentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQuestionsByAssessment($assessmentId)
    {
        $questions = Question::where('assessment_id', $assessmentId)
            ->orderBy('assessment_category_id')
            ->orderBy('order')
            ->get();

        return response()->json([
            'data' => $questions
        ]);
    }

    /**
     * Get questions by category ID
     * 
     * @param int $categoryId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQuestionsByCategory($categoryId)
    {
        $questions = Question::where('assessment_category_id', $categoryId)
            ->orderBy('order')
            ->get();

        return response()->json([
            'data' => $questions
        ]);
    }

    /**
     * Create a new question (admin only)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'assessment_id' => 'required|exists:assessments,id',
            'assessment_category_id' => 'required|exists:assessment_categories,id',
            'question_id' => 'required|string|max:50|unique:questions,question_id',
            'question_text' => 'required|string',
            'question_type' => 'required|string|in:rating,narrative',
            'subprompts' => 'nullable|json',
            'icon' => 'nullable|string',
            'order' => 'nullable|integer',
        ]);

        $question = Question::create($request->all());

        return response()->json([
            'message' => 'Question created successfully',
            'data' => $question
        ], 201);
    }

    /**
     * Update a question (admin only)
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $question = Question::findOrFail($id);

        $request->validate([
            'assessment_id' => 'sometimes|exists:assessments,id',
            'assessment_category_id' => 'sometimes|exists:assessment_categories,id',
            'question_id' => 'sometimes|string|max:50|unique:questions,question_id,' . $id,
            'question_text' => 'sometimes|string',
            'question_type' => 'sometimes|string|in:rating,narrative',
            'subprompts' => 'nullable|json',
            'icon' => 'nullable|string',
            'order' => 'nullable|integer',
        ]);

        $question->update($request->all());

        return response()->json([
            'message' => 'Question updated successfully',
            'data' => $question
        ]);
    }

    /**
     * Delete a question (admin only)
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $question = Question::findOrFail($id);
        $question->delete();

        return response()->json([
            'message' => 'Question deleted successfully'
        ]);
    }

    /**
     * Bulk create questions from JSON (admin only)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkCreate(Request $request)
    {
        $request->validate([
            'assessment_id' => 'required|exists:assessments,id',
            'questions' => 'required|array',
            'questions.*.category_id' => 'required|exists:assessment_categories,id',
            'questions.*.question_id' => 'required|string|max:50|unique:questions,question_id',
            'questions.*.question_text' => 'required|string',
            'questions.*.question_type' => 'required|string|in:rating,narrative',
            'questions.*.subprompts' => 'nullable|array',
        ]);

        $questions = [];

        foreach ($request->questions as $index => $questionData) {
            $questions[] = Question::create([
                'assessment_id' => $request->assessment_id,
                'assessment_category_id' => $questionData['category_id'],
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => $questionData['question_type'],
                'subprompts' => $questionData['subprompts'] ?? null,
                'icon' => $questionData['icon'] ?? null,
                'order' => $questionData['order'] ?? $index
            ]);
        }

        return response()->json([
            'message' => count($questions) . ' questions created successfully',
            'data' => $questions
        ], 201);
    }
}
