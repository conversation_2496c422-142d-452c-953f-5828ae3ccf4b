<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Assessment;
use App\Models\AssessmentCategory;

class AssessmentController extends Controller
{
    /**
     * Get all active assessments
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $assessments = Assessment::where('active', true)
            ->select('id', 'name', 'slug', 'description', 'icon', 'type')
            ->get();

        return response()->json([
            'data' => $assessments
        ]);
    }

    /**
     * Get assessment by ID or slug
     * 
     * @param string $idOrSlug
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($idOrSlug)
    {
        $assessment = is_numeric($idOrSlug)
            ? Assessment::findOrFail($idOrSlug)
            : Assessment::where('slug', $idOrSlug)->firstOrFail();

        return response()->json([
            'data' => $assessment
        ]);
    }

    /**
     * Get categories for an assessment
     * 
     * @param int $assessmentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategories($assessmentId)
    {
        $categories = AssessmentCategory::where('assessment_id', $assessmentId)
            ->orderBy('order')
            ->get();

        return response()->json([
            'data' => $categories
        ]);
    }

    /**
     * Create a new assessment (admin only)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:assessments',
            'description' => 'required|string',
            'type' => 'required|string|in:abilities,heart,experience,personalities,strength',
            'icon' => 'nullable|string',
            'scoring_scale' => 'nullable|json',
            'scoring_logic' => 'nullable|json',
        ]);

        $assessment = Assessment::create($request->all());

        return response()->json([
            'message' => 'Assessment created successfully',
            'data' => $assessment
        ], 201);
    }

    /**
     * Update an assessment (admin only)
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $assessment = Assessment::findOrFail($id);

        $request->validate([
            'name' => 'sometimes|string|max:255',
            'slug' => 'sometimes|string|max:255|unique:assessments,slug,' . $id,
            'description' => 'sometimes|string',
            'type' => 'sometimes|string|in:abilities,heart,experience,personalities,strength',
            'icon' => 'nullable|string',
            'scoring_scale' => 'nullable|json',
            'scoring_logic' => 'nullable|json',
            'active' => 'sometimes|boolean',
        ]);

        $assessment->update($request->all());

        return response()->json([
            'message' => 'Assessment updated successfully',
            'data' => $assessment
        ]);
    }

    /**
     * Delete an assessment (admin only)
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $assessment = Assessment::findOrFail($id);
        $assessment->delete();

        return response()->json([
            'message' => 'Assessment deleted successfully'
        ]);
    }
    /**
     * Get all assessments for the current user with progress information
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserAssessments(Request $request)
    {
        $user = $request->user();

        // Get all active assessments
        $assessments = Assessment::where('active', true)
            ->select('id', 'name', 'slug', 'description', 'icon', 'type')
            ->get();

        // Add progress information
        $assessments = $assessments->map(function ($assessment) use ($user) {
            // Get all questions for this assessment
            $totalQuestions = \App\Models\Question::where('assessment_id', $assessment->id)->count();

            if ($totalQuestions === 0) {
                $assessment->progress = 0;
                $assessment->current_question_index = 0;
                return $assessment;
            }

            // Get answered questions count
            $answeredQuestions = \App\Models\Answer::where('user_id', $user->id)
                ->where('assessment_id', $assessment->id)
                ->distinct('question_id')
                ->count('question_id');

            // Check if results exist (assessment completed)
            $hasResults = \App\Models\Result::where('user_id', $user->id)
                ->where('assessment_id', $assessment->id)
                ->exists();

            // Get saved progress position if any
            $savedProgress = \App\Models\AssessmentProgress::where('user_id', $user->id)
                ->where('assessment_id', $assessment->id)
                ->first();

            // Calculate progress based on saved position and answers
            if ($hasResults) {
                // If results exist, assessment is completed
                $assessment->progress = 100;
                $assessment->current_question_index = $totalQuestions - 1;
            } else if ($savedProgress) {
                // Use saved progress position as primary source
                $currentIndex = $savedProgress->current_question_index;

                // Progress is based on current position + 1 (since index starts at 0)
                // But ensure it doesn't exceed the number of answered questions
                $progressBasedOnPosition = (($currentIndex + 1) / $totalQuestions) * 100;
                $progressBasedOnAnswers = ($answeredQuestions / $totalQuestions) * 100;

                // Use the higher of the two, but cap at actual answered questions
                $assessment->progress = round(min($progressBasedOnPosition, $progressBasedOnAnswers));
                $assessment->current_question_index = $currentIndex;

                // If user has answered more questions than the saved position suggests,
                // move the position forward to match the answers
                if ($answeredQuestions > ($currentIndex + 1)) {
                    $assessment->current_question_index = min($answeredQuestions - 1, $totalQuestions - 1);
                    $assessment->progress = round(($answeredQuestions / $totalQuestions) * 100);
                }
            } else if ($answeredQuestions > 0) {
                // Calculate based on answered questions
                $assessment->progress = round(($answeredQuestions / $totalQuestions) * 100);
                $assessment->current_question_index = min($answeredQuestions - 1, $totalQuestions - 1);
            } else {
                // No progress yet
                $assessment->progress = 0;
                $assessment->current_question_index = 0;
            }

            return $assessment;
        });

        return response()->json([
            'data' => $assessments
        ]);
    }

    /**
     * Save user progress in an assessment
     * 
     * @param Request $request
     * @param int $assessmentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveProgress(Request $request, $assessmentId)
    {
        $user = $request->user();
        $assessment = Assessment::findOrFail($assessmentId);

        $request->validate([
            'current_question_index' => 'required|integer|min:0'
        ]);

        // Check if progress entry exists
        $progress = \App\Models\AssessmentProgress::updateOrCreate(
            [
                'user_id' => $user->id,
                'assessment_id' => $assessmentId
            ],
            [
                'current_question_index' => $request->current_question_index
            ]
        );

        return response()->json([
            'message' => 'Progress saved successfully',
            'data' => $progress
        ]);
    }

    /**
     * Get user progress for an assessment
     * 
     * @param Request $request
     * @param int $assessmentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProgress(Request $request, $assessmentId)
    {
        $user = $request->user();

        $progress = \App\Models\AssessmentProgress::where('user_id', $user->id)
            ->where('assessment_id', $assessmentId)
            ->first();

        if (!$progress) {
            return response()->json([
                'data' => [
                    'current_question_index' => 0
                ]
            ]);
        }

        return response()->json([
            'data' => $progress
        ]);
    }
}
