<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssessmentProgress extends Model
{
    use HasFactory;

    protected $table = 'assessment_progress';

    protected $fillable = [
        'user_id',
        'assessment_id',
        'current_question_index'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function assessment()
    {
        return $this->belongsTo(Assessment::class);
    }
}
