<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use HasFactory;

    protected $fillable = [
        'assessment_id',
        'assessment_category_id',
        'question_id',
        'question_text',
        'question_type',
        'subprompts',
        'icon',
        'order'
    ];

    protected $casts = [
        'subprompts' => 'array'
    ];

    public function assessment()
    {
        return $this->belongsTo(Assessment::class);
    }

    public function category()
    {
        return $this->belongsTo(AssessmentCategory::class, 'assessment_category_id');
    }

    public function answers()
    {
        return $this->hasMany(Answer::class);
    }
}
