<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Result extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'assessment_id',
        'raw_scores',
        'processed_results',
        'category_scores',
        'completed_at'
    ];

    protected $casts = [
        'raw_scores' => 'array',
        'processed_results' => 'array',
        'category_scores' => 'array',
        'completed_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function assessment()
    {
        return $this->belongsTo(Assessment::class);
    }
}
