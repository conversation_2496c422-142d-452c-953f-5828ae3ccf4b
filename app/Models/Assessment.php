<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Assessment extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'type',
        'scoring_scale',
        'scoring_logic',
        'active'
    ];

    protected $casts = [
        'scoring_scale' => 'array',
        'scoring_logic' => 'array',
        'active' => 'boolean'
    ];

    public function categories()
    {
        return $this->hasMany(AssessmentCategory::class);
    }

    public function questions()
    {
        return $this->hasMany(Question::class);
    }

    public function answers()
    {
        return $this->hasMany(Answer::class);
    }

    public function results()
    {
        return $this->hasMany(Result::class);
    }
}
