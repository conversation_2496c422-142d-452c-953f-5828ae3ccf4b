<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GuidanceContent extends Model
{
    use HasFactory;

    protected $table = 'guidance_content';

    protected $fillable = [
        'area',
        'profile_name',
        'profile_code',
        'description',
        'characteristics',
        'communication_style',
        'strengths',
        'weaknesses',
        'team_roles',
        'development_tips',
        'inspirational_figures',
        'creative_ideas',
        'love_language',
        'hidden_strengths',
        'warnings',
        'combinations',
        'superpower',
        'min_score',
        'max_score',
        'active'
    ];

    protected $casts = [
        'characteristics' => 'array',
        'communication_style' => 'array',
        'strengths' => 'array',
        'weaknesses' => 'array',
        'team_roles' => 'array',
        'development_tips' => 'array',
        'inspirational_figures' => 'array',
        'creative_ideas' => 'array',
        'love_language' => 'array',
        'hidden_strengths' => 'array',
        'warnings' => 'array',
        'combinations' => 'array',
        'active' => 'boolean'
    ];

    /**
     * Scope to get content by area
     */
    public function scopeByArea($query, $area)
    {
        return $query->where('area', $area)->where('active', true);
    }

    /**
     * Scope to get content by profile name
     */
    public function scopeByProfile($query, $profileName)
    {
        return $query->where('profile_name', $profileName)->where('active', true);
    }

    /**
     * Get content by area and profile
     */
    public static function getByAreaAndProfile($area, $profileName)
    {
        return self::where('area', $area)
                   ->where('profile_name', $profileName)
                   ->where('active', true)
                   ->first();
    }

    /**
     * Get all profiles for an area
     */
    public static function getProfilesByArea($area)
    {
        return self::where('area', $area)
                   ->where('active', true)
                   ->orderBy('profile_name')
                   ->get();
    }

    /**
     * Get profile by score range
     */
    public static function getProfileByScore($area, $score)
    {
        return self::where('area', $area)
                   ->where('min_score', '<=', $score)
                   ->where('max_score', '>=', $score)
                   ->where('active', true)
                   ->first();
    }
}
