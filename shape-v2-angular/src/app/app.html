<div class="min-h-screen bg-gray-50">
  <header class="bg-white shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a routerLink="/" class="text-2xl font-bold text-indigo-600"
              >Shape</a
            >
          </div>
          <nav class="ml-6 flex space-x-4">
            @if (isLoggedIn) {
            <a
              routerLink="/assessments"
              class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-indigo-600"
              >Assessments</a
            >
            <a
              routerLink="/dashboard"
              class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-indigo-600"
              >Dashboard</a
            >
            }
            <a
              routerLink="/about"
              class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-indigo-600"
              >About</a
            >
          </nav>
        </div>
        <div class="flex items-center">
          @if (!isLoggedIn) {
          <div class="flex space-x-2">
            <a
              routerLink="/signup"
              class="px-4 py-2 text-sm font-medium text-indigo-600 border border-indigo-600 rounded-md hover:bg-indigo-50"
            >
              Sign up
            </a>
            <a
              routerLink="/login"
              class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
            >
              Login
            </a>
          </div>
          } @else {
          <div class="ml-3 relative">
            <div class="flex items-center space-x-4">
              <a
                routerLink="/dashboard"
                class="text-sm font-medium text-gray-700 hover:text-indigo-600"
              >
                Dashboard
              </a>
              <div class="relative group">
                <button
                  type="button"
                  class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-indigo-600"
                >
                  <span>{{ currentUser?.name || 'My Account' }}</span>
                  <span
                    class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center"
                  >
                    <span class="text-sm font-medium text-gray-600"
                      >{{ currentUser?.name?.charAt(0) || 'U' }}</span
                    >
                  </span>
                </button>
                <div
                  class="hidden group-hover:block absolute right-0 w-48 py-1 mt-1 bg-white rounded-md shadow-lg z-50"
                >
                  <a
                    routerLink="/profile"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >Profile</a
                  >
                  <a
                    routerLink="/assessments"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >My Assessments</a
                  >
                  <button
                    (click)="logout()"
                    class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                  >
                    Logout
                  </button>
                </div>
              </div>
            </div>
          </div>
          }
        </div>
      </div>
    </div>
  </header>

  <main>
    <router-outlet></router-outlet>
  </main>
</div>
