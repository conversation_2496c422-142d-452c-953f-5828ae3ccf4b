import {
  HttpRequest,
  HttpHandlerFn,
  HttpEvent,
  HttpInterceptorFn,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const AuthInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Only add token to requests to our API
  if (request.url.startsWith(environment.apiUrl)) {
    const token = authService.getToken();
    if (token) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`,
        },
      });
    }
  }

  return next(request).pipe(
    catchError((error) => {
      // Handle auth-related errors (401 Unauthorized or 403 Forbidden)
      if (
        error instanceof HttpErrorResponse &&
        (error.status === 401 || error.status === 403)
      ) {
        console.warn(
          `Authentication error detected (${error.status}): ${error.message}`
        );

        // Add debug information
        console.log('Request URL:', request.url);
        console.log('Request method:', request.method);
        console.log('Error details:', error);

        // Clear invalid authentication data
        authService.clearAuth();

        // Get current URL for redirect back after login
        const currentUrl = router.url;
        console.log('Current URL to redirect back to after login:', currentUrl);

        // Redirect to login page
        router.navigate(['/auth/login'], {
          queryParams: {
            redirectUrl: currentUrl,
            authError: error.status === 401 ? 'expired' : 'forbidden',
          },
        });
      }
      return throwError(() => error);
    })
  );
};
