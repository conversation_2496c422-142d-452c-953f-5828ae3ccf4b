import { inject } from '@angular/core';
import { Router, CanActivateFn } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { Observable, map, catchError, of } from 'rxjs';

export const authGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const authService = inject(AuthService);

  // First check basic token presence
  if (!authService.isLoggedIn()) {
    console.log('Auth guard: No token found, redirecting to login');
    // Redirect to login page with return url
    router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }

  // If token exists, validate it with the backend
  return validateToken(authService, router, state.url);
};

/**
 * Helper function to validate token with the backend
 */
function validateToken(
  authService: AuthService,
  router: Router,
  returnUrl: string
): Observable<boolean> {
  return authService.validateToken().pipe(
    map(() => {
      console.log('Token validated successfully');
      return true;
    }),
    catchError((error) => {
      console.error('Token validation failed:', error);

      // If token is invalid, redirect to login
      router.navigate(['/login'], {
        queryParams: {
          returnUrl: returnUrl,
          authError: error.status === 401 ? 'expired' : 'invalid',
        },
      });
      return of(false);
    })
  );
}
