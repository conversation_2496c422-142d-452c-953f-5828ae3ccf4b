import {
  Component,
  Input,
  OnInit,
  OnDestroy,
  OnChanges,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';

Chart.register(...registerables);

export interface AreaDetailData {
  categories: {
    name: string;
    score: number;
    level: 'Tinggi' | 'Moderat' | 'Rendah';
  }[];
  primaryCategory: string;
  area: string;
}

@Component({
  selector: 'app-area-detail-chart',
  template: `
    <div class="chart-container">
      <div class="mb-4">
        <h4 class="text-lg font-semibold text-gray-900">
          {{ getAreaTitle() }}
        </h4>
        <p class="text-sm text-gray-600">
          Primary: {{ data?.primaryCategory || 'Loading...' }}
        </p>
      </div>
      <canvas #chartCanvas></canvas>
      <div class="mt-4 text-xs text-gray-500">
        <div class="flex justify-between">
          <span>Rendah (0-20)</span>
          <span>Moderat (21-35)</span>
          <span>Tinggi (36-50)</span>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .chart-container {
        position: relative;
        width: 100%;
      }
      canvas {
        max-height: 300px;
      }
    `,
  ],
})
export class AreaDetailChartComponent implements OnInit, OnDestroy, OnChanges {
  @ViewChild('chartCanvas', { static: true })
  chartCanvas!: ElementRef<HTMLCanvasElement>;
  @Input() data: AreaDetailData | null = null;
  @Input() colorScheme: string = 'blue';

  private chart: Chart | null = null;

  ngOnInit(): void {
    this.createChart();
  }

  ngOnDestroy(): void {
    if (this.chart) {
      this.chart.destroy();
    }
  }

  ngOnChanges(): void {
    if (this.chart && this.data) {
      this.updateChart();
    }
  }

  private createChart(): void {
    if (!this.data) {
      this.data = this.getDefaultData();
    }

    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    const colors = this.getColorScheme();

    const config: ChartConfiguration = {
      type: 'bar' as ChartType,
      data: {
        labels: this.data.categories.map((cat) => cat.name),
        datasets: [
          {
            label: 'Score',
            data: this.data.categories.map((cat) => cat.score),
            backgroundColor: this.data.categories.map((cat, index) => {
              // Highlight primary category
              if (cat.name === this.data!.primaryCategory) {
                return colors.primary;
              }
              return colors.secondary;
            }),
            borderColor: this.data.categories.map((cat, index) => {
              if (cat.name === this.data!.primaryCategory) {
                return colors.primaryBorder;
              }
              return colors.secondaryBorder;
            }),
            borderWidth: 2,
            borderRadius: 4,
            borderSkipped: false,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        indexAxis: 'y' as const, // Horizontal bar chart
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            callbacks: {
              title: (context) => {
                const index = context[0].dataIndex;
                return this.data!.categories[index].name;
              },
              label: (context) => {
                const index = context.dataIndex;
                const category = this.data!.categories[index];
                return [`Score: ${category.score}`, `Level: ${category.level}`];
              },
            },
          },
        },
        scales: {
          x: {
            beginAtZero: true,
            max: 50,
            ticks: {
              stepSize: 10,
              font: {
                size: 12,
              },
            },
            grid: {
              color: 'rgba(0, 0, 0, 0.1)',
            },
            title: {
              display: true,
              text: 'Score',
              font: {
                size: 12,
                weight: 'bold',
              },
            },
          },
          y: {
            ticks: {
              font: {
                size: 11,
              },
              maxRotation: 0,
              callback: function (value, index) {
                // Truncate long labels
                const label = this.getLabelForValue(value as number);
                return label.length > 15
                  ? label.substring(0, 15) + '...'
                  : label;
              },
            },
            grid: {
              display: false,
            },
          },
        },
        interaction: {
          intersect: false,
        },
      },
    };

    this.chart = new Chart(ctx, config);
  }

  private updateChart(): void {
    if (!this.chart || !this.data) return;

    const colors = this.getColorScheme();

    this.chart.data.labels = this.data.categories.map((cat) => cat.name);
    this.chart.data.datasets[0].data = this.data.categories.map(
      (cat) => cat.score
    );
    this.chart.data.datasets[0].backgroundColor = this.data.categories.map(
      (cat) => {
        return cat.name === this.data!.primaryCategory
          ? colors.primary
          : colors.secondary;
      }
    );
    this.chart.data.datasets[0].borderColor = this.data.categories.map(
      (cat) => {
        return cat.name === this.data!.primaryCategory
          ? colors.primaryBorder
          : colors.secondaryBorder;
      }
    );

    this.chart.update();
  }

  private getColorScheme() {
    const schemes: { [key: string]: any } = {
      red: {
        primary: 'rgba(239, 68, 68, 0.8)',
        primaryBorder: 'rgba(239, 68, 68, 1)',
        secondary: 'rgba(239, 68, 68, 0.3)',
        secondaryBorder: 'rgba(239, 68, 68, 0.5)',
      },
      pink: {
        primary: 'rgba(236, 72, 153, 0.8)',
        primaryBorder: 'rgba(236, 72, 153, 1)',
        secondary: 'rgba(236, 72, 153, 0.3)',
        secondaryBorder: 'rgba(236, 72, 153, 0.5)',
      },
      yellow: {
        primary: 'rgba(245, 158, 11, 0.8)',
        primaryBorder: 'rgba(245, 158, 11, 1)',
        secondary: 'rgba(245, 158, 11, 0.3)',
        secondaryBorder: 'rgba(245, 158, 11, 0.5)',
      },
      blue: {
        primary: 'rgba(59, 130, 246, 0.8)',
        primaryBorder: 'rgba(59, 130, 246, 1)',
        secondary: 'rgba(59, 130, 246, 0.3)',
        secondaryBorder: 'rgba(59, 130, 246, 0.5)',
      },
      green: {
        primary: 'rgba(34, 197, 94, 0.8)',
        primaryBorder: 'rgba(34, 197, 94, 1)',
        secondary: 'rgba(34, 197, 94, 0.3)',
        secondaryBorder: 'rgba(34, 197, 94, 0.5)',
      },
    };

    return schemes[this.colorScheme] || schemes['blue'];
  }

  private getDefaultData(): AreaDetailData {
    return {
      categories: [{ name: 'Loading...', score: 0, level: 'Rendah' }],
      primaryCategory: 'Loading...',
      area: 'unknown',
    };
  }

  public updateData(newData: AreaDetailData): void {
    this.data = newData;
    this.updateChart();
  }

  public getAreaTitle(): string {
    if (!this.data) return 'Loading...';

    const areaTitles: { [key: string]: string } = {
      strength: '💪 Strength Analysis',
      heart: '❤️ Heart Analysis',
      abilities: '🧠 Abilities Analysis',
      personality: '👤 Personality Analysis',
      experience: '📈 Experience Analysis',
    };

    return areaTitles[this.data.area] || `${this.data.area} Analysis`;
  }
}
