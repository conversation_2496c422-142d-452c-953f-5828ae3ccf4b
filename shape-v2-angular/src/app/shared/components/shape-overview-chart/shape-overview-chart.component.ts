import { Component, Input, OnInit, On<PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';

Chart.register(...registerables);

export interface ShapeOverviewData {
  strength: { name: string; score: number };
  heart: { name: string; score: number };
  abilities: { name: string; score: number };
  personality: { name: string; score: number };
  experience: { name: string; score: number };
}

@Component({
  selector: 'app-shape-overview-chart',
  template: `
    <div class="chart-container">
      <canvas #chartCanvas></canvas>
      <div class="chart-legend mt-4">
        <div class="grid grid-cols-2 md:grid-cols-5 gap-2 text-sm">
          <div class="flex items-center">
            <div class="w-4 h-4 bg-red-500 rounded mr-2"></div>
            <span>Strength</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-pink-500 rounded mr-2"></div>
            <span>Heart</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
            <span>Abilities</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-blue-500 rounded mr-2"></div>
            <span>Personality</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
            <span>Experience</span>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .chart-container {
      position: relative;
      height: 400px;
      width: 100%;
    }
    canvas {
      max-height: 400px;
    }
  `]
})
export class ShapeOverviewChartComponent implements OnInit, OnDestroy {
  @ViewChild('chartCanvas', { static: true }) chartCanvas!: ElementRef<HTMLCanvasElement>;
  @Input() data: ShapeOverviewData | null = null;
  @Input() title: string = 'SHAPE Profile Overview';

  private chart: Chart | null = null;

  ngOnInit(): void {
    this.createChart();
  }

  ngOnDestroy(): void {
    if (this.chart) {
      this.chart.destroy();
    }
  }

  ngOnChanges(): void {
    if (this.chart && this.data) {
      this.updateChart();
    }
  }

  private createChart(): void {
    if (!this.data) {
      this.data = this.getDefaultData();
    }

    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    const config: ChartConfiguration = {
      type: 'radar' as ChartType,
      data: {
        labels: ['Strength', 'Heart', 'Abilities', 'Personality', 'Experience'],
        datasets: [{
          label: 'SHAPE Profile',
          data: [
            this.data.strength.score,
            this.data.heart.score,
            this.data.abilities.score,
            this.data.personality.score,
            this.data.experience.score
          ],
          backgroundColor: 'rgba(99, 102, 241, 0.2)',
          borderColor: 'rgba(99, 102, 241, 1)',
          borderWidth: 2,
          pointBackgroundColor: [
            'rgba(239, 68, 68, 1)',   // Red for Strength
            'rgba(236, 72, 153, 1)',  // Pink for Heart
            'rgba(245, 158, 11, 1)',  // Yellow for Abilities
            'rgba(59, 130, 246, 1)',  // Blue for Personality
            'rgba(34, 197, 94, 1)'    // Green for Experience
          ],
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: this.title,
            font: {
              size: 16,
              weight: 'bold'
            }
          },
          legend: {
            display: false // We'll use custom legend
          },
          tooltip: {
            callbacks: {
              title: (context) => {
                const index = context[0].dataIndex;
                const areas = ['strength', 'heart', 'abilities', 'personality', 'experience'];
                const area = areas[index] as keyof ShapeOverviewData;
                return `${context[0].label}: ${this.data![area].name}`;
              },
              label: (context) => {
                return `Score: ${context.parsed.r}`;
              }
            }
          }
        },
        scales: {
          r: {
            beginAtZero: true,
            max: 100,
            min: 0,
            ticks: {
              stepSize: 20,
              font: {
                size: 12
              }
            },
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            },
            angleLines: {
              color: 'rgba(0, 0, 0, 0.1)'
            },
            pointLabels: {
              font: {
                size: 14,
                weight: 'bold'
              }
            }
          }
        },
        interaction: {
          intersect: false
        }
      }
    };

    this.chart = new Chart(ctx, config);
  }

  private updateChart(): void {
    if (!this.chart || !this.data) return;

    this.chart.data.datasets[0].data = [
      this.data.strength.score,
      this.data.heart.score,
      this.data.abilities.score,
      this.data.personality.score,
      this.data.experience.score
    ];

    this.chart.update();
  }

  private getDefaultData(): ShapeOverviewData {
    return {
      strength: { name: 'Loading...', score: 0 },
      heart: { name: 'Loading...', score: 0 },
      abilities: { name: 'Loading...', score: 0 },
      personality: { name: 'Loading...', score: 0 },
      experience: { name: 'Loading...', score: 0 }
    };
  }

  public updateData(newData: ShapeOverviewData): void {
    this.data = newData;
    this.updateChart();
  }
}
