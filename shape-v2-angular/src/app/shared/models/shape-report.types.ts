/**
 * TypeScript interfaces for SHAPE assessment data and processing
 */

/**
 * User information
 */
export interface ShapeUser {
  name: string;
  email: string;
  assessment_date: string;
}

/**
 * Strength component data
 */
export interface StrengthData {
  top_abilities: string[];
  scores: { [key: string]: number };
}

/**
 * Heart component data
 */
export interface HeartData {
  top_interests: string[];
  scores: { [key: string]: number };
}

/**
 * Abilities component data
 */
export interface AbilitiesData {
  cognitive: number;
  social: number;
  technical: number;
  management: number;
  creative: number;
  physical: number;
}

/**
 * Personality component data
 */
export interface PersonalityData {
  primary: string;
  secondary: string;
  scores: { [key: string]: number };
}

/**
 * Experience component data
 */
export interface ExperienceData {
  core_competencies: string[];
  transformative_moments: { event: string; impact: string }[];
}

/**
 * Raw SHAPE assessment result data
 */
export interface ShapeRawData {
  user: ShapeUser;
  strength: StrengthData;
  heart: HeartData;
  abilities: AbilitiesData;
  personality: PersonalityData;
  experience: ExperienceData;
}

/**
 * Strength description data
 */
export interface StrengthDescription {
  primary: string;
  secondary: string;
  primaryDescription: string;
  secondaryDescription: string;
  synergy: string;
}

/**
 * Heart description data
 */
export interface HeartDescription {
  primary: string;
  secondary: string;
  primaryDescription: string;
  secondaryDescription: string;
  synergy: string;
}

/**
 * Abilities description data
 */
export interface AbilitiesDescription {
  top: string[];
  descriptions: string[];
}

/**
 * Personality characteristics data
 */
export interface PersonalityCharacteristics {
  profile: string;
  archetype: string;
  strengths: string[];
  challenges: string[];
}

/**
 * Experience description data
 */
export interface ExperienceDescription {
  core_patterns: string[];
  transformative_experiences: any[];
  skill_development: string;
}

/**
 * Integration pattern data
 */
export interface IntegrationData {
  genius_zone: string;
  pattern_description: string;
  peta_interaksi?: {
    narrative: string;
    patterns: {
      [key: string]: string;
    };
  };
  executive_summary?: {
    archetype_name: string;
    core_identity: string;
    metaphor: string;
    convergence_point: string;
  };
  cetak_biru_pengembangan?: {
    milestones: {
      phase: string;
      project: string;
      timeline: string;
    }[];
    callToAction: {
      sevenDays: string;
      thirtyDays: string;
      ninetyDays: string;
    };
  };
  wisdom_quote?: {
    quote: string;
    author: string;
  };
  development_recommendations: {
    strengthen: {
      title: string;
      recommendations: string[];
    };
    explore: {
      title: string;
      recommendations: string[];
    };
    adapt: {
      title: string;
      recommendations: string[];
    };
    roadmap: {
      phases: {
        name: string;
        activity: string;
        duration: string;
      }[];
    };
  };
}

/**
 * Processed SHAPE report data ready for PDF generation
 */
export interface ShapePdfReportData {
  user: ShapeUser;
  profileMetaphor: string;
  strength: StrengthData & {
    description: StrengthDescription;
  };
  heart: HeartData & {
    description: HeartDescription;
  };
  abilities: AbilitiesData & {
    description: AbilitiesDescription;
  };
  personality: PersonalityData & {
    characteristics: PersonalityCharacteristics;
  };
  experience: ExperienceData & {
    description: ExperienceDescription;
  };
  integration: IntegrationData;
  detailed_reports?: {
    strength_report?: any;
    heart_report?: any;
    abilities_report?: any;
    personality_report?: any;
    experience_report?: any;
  };
  colors: {
    strength: string;
    heart: string;
    abilities: string;
    personality: string;
    experience: string;
  };
  chartImages?: {
    [key: string]: string;
  };
}
