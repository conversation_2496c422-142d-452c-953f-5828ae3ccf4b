import { Injectable, inject } from '@angular/core';
import { ReportProcessorService } from './report-processor.service';
import { DomSanitizer } from '@angular/platform-browser';
import {
  ShapeRawData,
  ShapePdfReportData,
  ShapeUser,
  StrengthDescription,
  HeartDescription,
  AbilitiesDescription,
  PersonalityCharacteristics,
  ExperienceDescription,
  IntegrationData,
} from '../models/shape-report.types';

@Injectable({
  providedIn: 'root',
})
export class ShapePdfReportService {
  private reportProcessor = inject(ReportProcessorService);
  private sanitizer = inject(DomSanitizer);

  /**
   * Prepare data for PDF report generation
   * @param assessmentData Raw assessment data
   * @returns Processed data ready for PDF generation
   */
  preparePdfReportData(
    assessmentData: ShapeRawData
  ): ShapePdfReportData | null {
    if (!assessmentData) {
      return null;
    }

    // Process the raw assessment data
    const processedReport =
      this.reportProcessor.processShapeReport(assessmentData);

    if (!processedReport) {
      return null;
    }

    // Format data specifically for PDF structure
    return {
      user: {
        name: assessmentData.user?.name || 'User',
        email: assessmentData.user?.email || '',
        assessment_date:
          assessmentData.user?.assessment_date ||
          new Date().toISOString().split('T')[0],
      },

      // Profile Metaphor
      profileMetaphor: this.generateProfileMetaphor(processedReport),

      // Strength data
      strength: {
        top_abilities: this.extractTopStrengths(assessmentData.strength),
        scores: assessmentData.strength?.scores || {},
        description: this.getStrengthDescription(processedReport),
      },

      // Heart data
      heart: {
        top_interests: this.extractTopInterests(assessmentData.heart),
        scores: assessmentData.heart?.scores || {},
        description: this.getHeartDescription(processedReport),
      },

      // Abilities data
      abilities: {
        cognitive: assessmentData.abilities?.cognitive || 0,
        social: assessmentData.abilities?.social || 0,
        technical: assessmentData.abilities?.technical || 0,
        management: assessmentData.abilities?.management || 0,
        creative: assessmentData.abilities?.creative || 0,
        physical: assessmentData.abilities?.physical || 0,
        description: this.getAbilitiesDescription(processedReport),
      },

      // Personality data
      personality: {
        primary: assessmentData.personality?.primary || '',
        secondary: assessmentData.personality?.secondary || '',
        scores: assessmentData.personality?.scores || {},
        characteristics: this.getPersonalityCharacteristics(processedReport),
      },

      // Experience data
      experience: {
        core_competencies: assessmentData.experience?.core_competencies || [],
        transformative_moments:
          assessmentData.experience?.transformative_moments || [],
        description: this.getExperienceDescription(processedReport),
      },

      // Integration components
      integration: {
        genius_zone: processedReport.genius_zone || 'Specialized Professional',
        pattern_description:
          this.getIntegrationPatternDescription(processedReport),
        development_recommendations:
          this.getDevelopmentRecommendations(processedReport),
      },

      // Color scheme for consistent styling
      colors: {
        strength: '#FF6B6B',
        heart: '#4ECDC4',
        abilities: '#FFE66D',
        personality: '#1A535C',
        experience: '#FF9F1C',
      },
    };
  }

  /**
   * Generate a profile metaphor based on the user's dominant traits
   */
  private generateProfileMetaphor(report: any): string {
    const strength = report.strength_profile?.primary || '';
    const heart = report.heart_profile?.primary_interest || '';

    const strengthArchetypes: { [key: string]: string } = {
      'Visionary Pioneers': 'Pemikir Visioner',
      'Insightful Truth-Seekers': 'Pencari Kebenaran',
      'Inspiring Connectors': 'Inspirator',
      'Supportive Nurturers': 'Pendukung',
      'Clarifying Mentors': 'Penjelasan Sistematis',
    };

    const heartDomains: { [key: string]: string } = {
      'Art & Entertainment': 'dunia Seni & Hiburan',
      Business: 'dunia Bisnis',
      'Communication/Media': 'dunia Media & Komunikasi',
      Family: 'konteks Keluarga',
      Religion: 'ruang Spiritual',
      Education: 'dunia Pendidikan',
      Government: 'ruang Kebijakan Publik',
    };

    const archetype = strengthArchetypes[strength] || 'Pemikir Strategis';
    const domain = heartDomains[heart] || 'bidang profesional';

    return `Anda adalah ${archetype} di ${domain}`;
  }

  /**
   * Extract top strength abilities
   */
  private extractTopStrengths(strengthData: any): string[] {
    if (!strengthData || !strengthData.scores) {
      return ['Visionary Pioneers', 'Clarifying Mentors'];
    }

    // Sort scores and get top 2
    return Object.entries(strengthData.scores)
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 2)
      .map(([key]: any) => key);
  }

  /**
   * Extract top heart interests
   */
  private extractTopInterests(heartData: any): string[] {
    if (!heartData || !heartData.scores) {
      return ['Education', 'Art & Entertainment'];
    }

    // Sort scores and get top 2
    return Object.entries(heartData.scores)
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 2)
      .map(([key]: any) => key);
  }

  /**
   * Get strength description
   */
  private getStrengthDescription(report: any): any {
    const primary = report.strength_profile?.primary || '';
    const secondary = report.strength_profile?.secondary || '';

    return {
      primary,
      secondary,
      primaryDescription: `${primary}: ${this.getStrengthDefinition(primary)}`,
      secondaryDescription: `${secondary}: ${this.getStrengthDefinition(
        secondary
      )}`,
      synergy: this.getStrengthSynergy(primary, secondary),
    };
  }

  /**
   * Get heart description
   */
  private getHeartDescription(report: any): any {
    const primary = report.heart_profile?.primary_interest || '';
    const secondary = report.heart_profile?.secondary_interest || '';

    return {
      primary,
      secondary,
      primaryDescription: this.getHeartDefinition(primary),
      secondaryDescription: this.getHeartDefinition(secondary),
      synergy: `${primary} + ${secondary}: ${this.getHeartSynergy(
        primary,
        secondary
      )}`,
    };
  }

  /**
   * Get abilities description
   */
  private getAbilitiesDescription(report: any): any {
    // Get top 2 abilities
    const abilitiesMap = report.abilities_profile?.scores || {};
    const topAbilities = Object.entries(abilitiesMap)
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 2)
      .map(([key]: any) => key);

    return {
      top: topAbilities,
      descriptions: topAbilities.map(
        (ability) => `${ability}: ${this.getAbilityDefinition(ability)}`
      ),
    };
  }

  /**
   * Get personality characteristics
   */
  private getPersonalityCharacteristics(report: any): any {
    const primary = report.personality_profile?.primary_type || '';
    const secondary = report.personality_profile?.secondary_type || '';

    return {
      profile: `${primary} + ${secondary}`,
      archetype: this.getPersonalityArchetype(primary, secondary),
      strengths: this.getPersonalityStrengths(primary),
      challenges: this.getPersonalityChallenges(primary),
    };
  }

  /**
   * Get experience description
   */
  private getExperienceDescription(report: any): any {
    return {
      core_patterns: report.experience_profile?.core_patterns || [
        'Manajemen Proyek',
        'Pelatihan',
      ],
      transformative_experiences:
        report.experience_profile?.transformative_experiences || [],
      skill_development:
        report.experience_profile?.skill_development ||
        'Pengembangan keahlian berbasis proyek',
    };
  }

  /**
   * Get integration pattern description
   */
  private getIntegrationPatternDescription(report: any): string {
    const strength = report.strength_profile?.primary || '';
    const heart = report.heart_profile?.primary_interest || '';
    const ability = Object.entries(report.abilities_profile?.scores || {})
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 1)
      .map(([key]: any) => key)[0];

    return `Passion di bidang ${heart} menjadi bahan bakar pengembangan ${ability}, yang diperkuat oleh ${strength} bawaan.`;
  }

  /**
   * Get development recommendations
   */
  private getDevelopmentRecommendations(report: any): any {
    const strength = report.strength_profile?.primary || '';
    const heart = report.heart_profile?.primary_interest || '';
    const abilities = Object.entries(report.abilities_profile?.scores || {})
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 1)
      .map(([key]: any) => key)[0];
    const personality = report.personality_profile?.primary_type || '';

    return {
      strengthen: {
        title: `Kuatkan (Strength × Ability)`,
        recommendations: [
          this.getStrengthAbilityRecommendation(strength, abilities),
          `Proyek: Develop modul pelatihan inovatif`,
        ],
      },
      explore: {
        title: `Eksplorasi (Heart × Experience)`,
        recommendations: [
          this.getHeartExperienceRecommendation(heart),
          `Studi kasus: "Seni sebagai Media Edukasi"`,
        ],
      },
      adapt: {
        title: `Adaptasi (Personality)`,
        recommendations: [this.getPersonalityRecommendation(personality)],
      },
      roadmap: {
        phases: [
          {
            name: 'Penguatan',
            activity: 'Pelatihan Manajemen Edukasi',
            duration: '90 hari',
          },
          {
            name: 'Eksplorasi',
            activity: 'Proyek Seni-Edukasi',
            duration: '60 hari',
          },
          {
            name: 'Konsolidasi',
            activity: 'Membuat Portofolio',
            duration: '30 hari',
          },
        ],
      },
    };
  }

  /**
   * Helper function definitions for various components
   */
  private getStrengthDefinition(strength: string): string {
    const definitions: { [key: string]: string } = {
      'Visionary Pioneers':
        'Kemampuan melihat peluang baru dan memulai terobosan',
      'Insightful Truth-Seekers':
        'Kemampuan menemukan inti kebenaran dan prinsip fundamental',
      'Inspiring Connectors':
        'Kemampuan menghubungkan orang dengan ide dan peluang',
      'Supportive Nurturers': 'Kemampuan mendukung pertumbuhan orang lain',
      'Clarifying Mentors':
        'Kemampuan menjelaskan konsep kompleks secara sistematis',
    };

    return definitions[strength] || 'Kemampuan unik yang perlu dikembangkan';
  }

  private getHeartDefinition(interest: string): string {
    const definitions: { [key: string]: string } = {
      'Art & Entertainment': 'Minat pada seni, budaya, dan industri hiburan',
      Business: 'Minat pada kewirausahaan, manajemen, dan pengembangan bisnis',
      'Communication/Media':
        'Minat pada jurnalistik, media, dan komunikasi publik',
      Family: 'Minat pada penguatan keluarga dan hubungan personal',
      Religion: 'Minat pada spiritualitas, agama, dan makna hidup',
      Education: 'Minat pada pembelajaran, pengajaran, dan pengembangan ilmu',
      Government:
        'Minat pada kebijakan publik, pelayanan masyarakat, dan tata kelola',
    };

    return (
      definitions[interest] || 'Area minat yang memberi energi dan motivasi'
    );
  }

  private getAbilityDefinition(ability: string): string {
    const definitions: { [key: string]: string } = {
      cognitive:
        'Kemampuan analisis, penalaran, dan pemecahan masalah kompleks',
      social: 'Kemampuan memahami orang, berkolaborasi, dan membangun hubungan',
      technical: 'Kemampuan menguasai sistem, alat, dan teknologi',
      management: 'Kemampuan mengorganisasi sumber daya, proses, dan orang',
      creative: 'Kemampuan inovasi, desain, dan kreasi konten orisinal',
      physical:
        'Kemampuan ketangkasan, keterampilan tangan, dan aktivitas fisik',
    };

    return definitions[ability] || 'Keahlian praktis yang dapat ditingkatkan';
  }

  private getStrengthSynergy(primary: string, secondary: string): string {
    const synergyPairs: { [key: string]: string } = {
      'Visionary Pioneers_Insightful Truth-Seekers':
        'Innovator Strategis - visi radikal berbasis prinsip fundamental',
      'Inspiring Connectors_Supportive Nurturers':
        'Community Architect - membangun ekosistem saling mendukung',
      'Clarifying Mentors_Insightful Truth-Seekers':
        'Systems Philosopher - kerangka kerja berbasis kebijaksanaan',
      'Visionary Pioneers_Inspiring Connectors':
        'Movement Starter - mengubah ide menjadi gerakan masif',
      'Supportive Nurturers_Clarifying Mentors':
        'Talent Alchemist - mengubah potensi mentah menjadi keunggulan',
    };

    const key = `${primary}_${secondary}`;
    return (
      synergyPairs[key] || 'Kombinasi unik yang menguatkan keunggulan personal'
    );
  }

  private getHeartSynergy(primary: string, secondary: string): string {
    const synergyPairs: { [key: string]: string } = {
      'Education_Art & Entertainment':
        'Pendidikan Kreatif - pembelajaran melalui eksplorasi artistik',
      Business_Education:
        'Knowledge Enterprise - monetisasi pengetahuan dan keahlian',
      'Communication/Media_Government':
        'Public Affairs Specialist - menjembatani kebijakan dan masyarakat',
      Family_Religion:
        'Pembina Nilai - menanamkan prinsip hidup melalui hubungan erat',
    };

    const key = `${primary}_${secondary}`;
    return (
      synergyPairs[key] || 'Kombinasi minat yang menciptakan keunikan personal'
    );
  }

  private getPersonalityArchetype(primary: string, secondary: string): string {
    const archetypes: { [key: string]: string } = {
      'Visionary Driver_Precision Analyst':
        'Strategist: Pemimpin visioner dengan pendekatan analitis',
      'Dynamic Connector_Visionary Driver':
        'Catalyst: Penggerak perubahan yang menginspirasi orang lain',
      'Steady Supporter_Precision Analyst':
        'Guardian: Penjaga kualitas dengan pendekatan stabil',
      'Dynamic Connector_Steady Supporter':
        'Diplomat: Pembangun hubungan dengan fondasi kuat',
    };

    const key = `${primary}_${secondary}`;
    return archetypes[key] || 'Profil unik dengan kombinasi gaya personal';
  }

  private getPersonalityStrengths(personalityType: string): string[] {
    const strengths: { [key: string]: string[] } = {
      'Visionary Driver': [
        'Inisiatif tinggi',
        'Berorientasi hasil',
        'Pemikiran strategis',
      ],
      'Dynamic Connector': [
        'Keterampilan sosial tinggi',
        'Membangun koneksi cepat',
        'Energi positif',
      ],
      'Steady Supporter': ['Konsistensi tinggi', 'Kesetiaan', 'Keandalan'],
      'Precision Analyst': [
        'Perhatian pada detail',
        'Kualitas tinggi',
        'Analisis mendalam',
      ],
    };

    return (
      strengths[personalityType] || [
        'Kombinasi kekuatan unik',
        'Adaptabilitas situasional',
      ]
    );
  }

  private getPersonalityChallenges(personalityType: string): string[] {
    const challenges: { [key: string]: string[] } = {
      'Visionary Driver': [
        'Kesulitan dengan detail operasional',
        'Kurang sabar',
        'Terkesan dominan',
      ],
      'Dynamic Connector': [
        'Terlalu bergantung pada hubungan',
        'Menghindari konflik',
        'Kurang fokus',
      ],
      'Steady Supporter': [
        'Sulit menghadapi perubahan cepat',
        'Menunda keputusan',
        'Kurang tegas',
      ],
      'Precision Analyst': [
        'Perfeksionisme berlebihan',
        'Kesulitan melihat gambaran besar',
        'Terkesan kritis',
      ],
    };

    return (
      challenges[personalityType] || [
        'Potensi ketidakseimbangan',
        'Area untuk refleksi',
      ]
    );
  }

  private getStrengthAbilityRecommendation(
    strength: string,
    ability: string
  ): string {
    const recommendations: { [key: string]: { [key: string]: string } } = {
      'Visionary Pioneers': {
        management: 'Kursus "Manajemen Inovasi dan Pengembangan Produk"',
        cognitive: 'Workshop "Strategic Foresight & Scenario Planning"',
        creative: 'Program "Design Thinking for Disruptive Innovation"',
      },
      'Clarifying Mentors': {
        management: 'Kursus "Manajemen Program Pendidikan"',
        cognitive: 'Sertifikasi "Instructional Design & Learning Architecture"',
        creative: 'Workshop "Visual Learning & Knowledge Mapping"',
      },
    };

    return (
      recommendations[strength]?.[ability] ||
      'Kursus pengembangan profesional berbasis kekuatan'
    );
  }

  private getHeartExperienceRecommendation(heart: string): string {
    const recommendations: { [key: string]: string } = {
      Education: 'Kolaborasi dengan komunitas seni-edukasi',
      'Art & Entertainment':
        'Eksplorasi pendekatan terapi seni untuk pembelajaran',
      Business: 'Mentoring startup di industri kreatif',
      Government: 'Keterlibatan dalam program kebijakan pendidikan publik',
    };

    return recommendations[heart] || 'Proyek pengembangan berbasis passion';
  }

  private getPersonalityRecommendation(personality: string): string {
    const recommendations: { [key: string]: string } = {
      'Visionary Driver':
        'Pelatihan manajemen detail untuk melengkapi visi besar',
      'Dynamic Connector':
        'Workshop fokus dan penyelesaian tugas untuk menyeimbangkan kegiatan sosial',
      'Steady Supporter':
        'Program pengembangan ketegasan dan pengambilan keputusan cepat',
      'Precision Analyst':
        'Pelatihan komunikasi visual untuk menyederhanakan ide kompleks',
    };

    return (
      recommendations[personality] ||
      'Program pengembangan untuk menyeimbangkan profil kepribadian'
    );
  }

  /**
   * Generate comprehensive HTML report based on Overall.md guidance template
   * This method creates a complete SHAPE report following the guidance structure
   */
  generateComprehensiveHtmlReport(assessmentData: any): string {
    if (!assessmentData) {
      return '<p>No assessment data available</p>';
    }

    // Process the assessment data using the enhanced report processor
    const processedReport =
      this.reportProcessor.processShapeReport(assessmentData);

    if (!processedReport) {
      return '<p>Unable to process assessment data</p>';
    }

    const overallReport = processedReport.overall_report || {};
    const detailedReports = processedReport.detailed_reports || {};

    // Generate HTML following the Overall.md template structure
    const htmlContent = `
      <!DOCTYPE html>
      <html lang="id">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>SHAPE Report - ${assessmentData.user?.name || 'User'}</title>
        <style>
          ${this.getReportStyles()}
        </style>
      </head>
      <body>
        <div class="report-container">

          <!-- Header Section -->
          <header class="report-header">
            <h1>LAPORAN SHAPE TERPADU</h1>
            <div class="user-info">
              <p><strong>Nama:</strong> ${
                assessmentData.user?.name || 'User'
              }</p>
              <p><strong>Tanggal:</strong> ${new Date().toLocaleDateString(
                'id-ID'
              )}</p>
            </div>
          </header>

          <!-- 1. SHAPE PROFILE OVERVIEW -->
          <section class="overview-section">
            <h2>🎯 SHAPE Profile Overview</h2>

            <!-- SHAPE Summary -->
            <div class="shape-summary">
              <h3>Your SHAPE Profile Summary</h3>
              <div class="shape-acronym">
                <div class="shape-item"><strong>S:</strong> ${
                  assessmentData.strength?.primary || 'Unknown'
                }</div>
                <div class="shape-item"><strong>H:</strong> ${
                  assessmentData.heart?.primary || 'Unknown'
                }</div>
                <div class="shape-item"><strong>A:</strong> ${
                  assessmentData.abilities?.primary || 'Unknown'
                }</div>
                <div class="shape-item"><strong>P:</strong> ${
                  assessmentData.personality?.type || 'Unknown'
                }</div>
                <div class="shape-item"><strong>E:</strong> ${
                  assessmentData.experience?.pattern || 'Unknown'
                }</div>
              </div>
            </div>

            <!-- Genius Zone -->
            <div class="genius-zone">
              <h3>🌟 Your Genius Zone</h3>
              <p class="genius-description">${
                overallReport.zona_genius ||
                'Your unique combination of strengths creates a powerful impact zone.'
              }</p>
            </div>

            <!-- Integrative Profile -->
            <div class="integrative-profile">
              <h3>🔗 Integrative Profile</h3>
              <blockquote class="profile-quote">
                "${
                  overallReport.profil_integratif ||
                  'Your unique SHAPE profile creates a distinctive professional identity.'
                }"
              </blockquote>
            </div>
          </section>

          <!-- 2. DETAILED AREA ANALYSIS -->
          ${this.generateDetailedAreaSections(assessmentData, detailedReports)}
            <div class="component-analysis strength-card">
              <h3>💪 Analisis Kekuatan (Strength)</h3>
              <p><strong>Kekuatan Utama:</strong> ${
                detailedReports.strength_report?.primary_strength?.name ||
                'Balanced Strength'
              }</p>
              <p><strong>Superpower:</strong> ${
                detailedReports.strength_report?.primary_strength?.superpower ||
                'Unique combination of capabilities'
              }</p>
              <p><strong>Deskripsi:</strong> ${
                detailedReports.strength_report?.primary_strength
                  ?.description || 'Well-rounded professional capabilities'
              }</p>

              <h4>Power Partners:</h4>
              <ul>
                ${(detailedReports.strength_report?.power_partners || [])
                  .map((partner: string) => `<li>${partner}</li>`)
                  .join('')}
              </ul>

              <h4>Area Pengembangan:</h4>
              <div class="warning-box">
                <strong>Perhatikan:</strong> ${
                  detailedReports.strength_report?.growth_opportunities
                    ?.watch_out_for || 'Balance is key'
                }
                <br><strong>Kembangkan:</strong> ${
                  detailedReports.strength_report?.growth_opportunities
                    ?.develop || 'Continuous improvement'
                }
              </div>
            </div>

            <!-- Heart Analysis -->
            <div class="component-analysis heart-card">
              <h3>❤️ Analisis Passion (Heart)</h3>
              <p><strong>Passion Utama:</strong> ${
                detailedReports.heart_report?.primary_passion?.name ||
                'Personal Development'
              }</p>
              <p><strong>Kekuatan Tersembunyi:</strong> ${
                detailedReports.heart_report?.primary_passion
                  ?.hidden_strengths || 'Unique perspective and approach'
              }</p>
              <p><strong>Deskripsi:</strong> ${
                detailedReports.heart_report?.primary_passion?.description ||
                'Driven by personal growth and contribution'
              }</p>

              <h4>Ide Kreatif:</h4>
              <ul>
                ${(detailedReports.heart_report?.creative_ideas || [])
                  .map((idea: string) => `<li>${idea}</li>`)
                  .join('')}
              </ul>

              <h4>Peringatan:</h4>
              <div class="warning-box">
                ${
                  detailedReports.heart_report?.warning_signs ||
                  'Maintain balance in pursuing your passions'
                }
              </div>
            </div>

          </section>

          <!-- 5. CETAK BIRU PENGEMBANGAN -->
          <section class="section">
            <h2>5. CETAK BIRU PENGEMBANGAN</h2>
            <div class="development-blueprint">
              <h3>Rencana Pengembangan 7/30/90 Hari</h3>

              <div class="timeline">
                <h4>🎯 Langkah 7 Hari:</h4>
                <ul class="recommendations-list">
                  <li>Identifikasi satu kekuatan utama untuk difokuskan</li>
                  <li>Mulai journaling tentang passion dan motivasi harian</li>
                  <li>Lakukan self-assessment kemampuan saat ini</li>
                </ul>

                <h4>📈 Langkah 30 Hari:</h4>
                <ul class="recommendations-list">
                  <li>Eksperimen dengan proyek kecil yang menggabungkan strength dan heart</li>
                  <li>Cari mentor atau partner yang melengkapi kemampuan Anda</li>
                  <li>Evaluasi dan adjust strategi berdasarkan feedback</li>
                </ul>

                <h4>🚀 Langkah 90 Hari:</h4>
                <ul class="recommendations-list">
                  <li>Luncurkan proyek signifikan yang memanfaatkan zona genius</li>
                  <li>Bangun network dan platform untuk sharing expertise</li>
                  <li>Dokumentasikan pembelajaran dan iterasi untuk improvement</li>
                </ul>
              </div>
            </div>
          </section>

          <!-- Footer -->
          <footer class="report-footer">
            <p><em>"${
              overallReport.wisdom_quote ||
              'Embrace your unique SHAPE and make your mark on the world.'
            }"</em></p>
            <p>Generated on ${new Date().toLocaleDateString(
              'id-ID'
            )} | SHAPE Assessment System</p>
          </footer>

        </div>
      </body>
      </html>
    `;

    return htmlContent;
  }

  /**
   * Get comprehensive CSS styles for the report
   */
  private getReportStyles(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f8f9fa;
      }

      .report-container {
        max-width: 210mm;
        margin: 0 auto;
        background: white;
        padding: 20mm;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
      }

      .report-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 3px solid #4f46e5;
      }

      .report-header h1 {
        color: #4f46e5;
        font-size: 2.5em;
        margin-bottom: 10px;
        font-weight: 700;
      }

      .user-info {
        background: #f8f9ff;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
      }

      .section {
        margin-bottom: 40px;
        page-break-inside: avoid;
      }

      .section h2 {
        color: #1e293b;
        font-size: 1.8em;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e2e8f0;
      }

      .section h3 {
        color: #475569;
        font-size: 1.4em;
        margin: 25px 0 15px 0;
      }

      .integrative-profile {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        text-align: center;
        margin-bottom: 20px;
      }

      .genius-zone {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 25px;
        border-radius: 10px;
        text-align: center;
      }

      .component-analysis {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }

      .strength-card {
        background: #fef2f2;
        border-left: 4px solid #dc2626;
        padding: 15px;
        margin: 10px 0;
      }

      .heart-card {
        background: #fdf2f8;
        border-left: 4px solid #ec4899;
        padding: 15px;
        margin: 10px 0;
      }

      .abilities-card {
        background: #fffbeb;
        border-left: 4px solid #d97706;
        padding: 15px;
        margin: 10px 0;
      }

      .personality-card {
        background: #f0f9ff;
        border-left: 4px solid #0284c7;
        padding: 15px;
        margin: 10px 0;
      }

      .experience-card {
        background: #f0fdf4;
        border-left: 4px solid #16a34a;
        padding: 15px;
        margin: 10px 0;
      }

      .recommendations-list {
        list-style: none;
        padding: 0;
      }

      .recommendations-list li {
        background: #f1f5f9;
        margin: 8px 0;
        padding: 12px;
        border-radius: 6px;
        border-left: 3px solid #3b82f6;
      }

      .warning-box {
        background: #fef3cd;
        border: 1px solid #fbbf24;
        border-radius: 6px;
        padding: 15px;
        margin: 15px 0;
      }

      .warning-box::before {
        content: "⚠️ ";
        font-weight: bold;
      }

      .report-footer {
        text-align: center;
        margin-top: 40px;
        padding-top: 20px;
        border-top: 2px solid #e2e8f0;
        color: #64748b;
      }

      /* New Layout Styles */
      .overview-section {
        margin-bottom: 40px;
        padding: 25px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
      }

      .shape-summary {
        background: rgba(255, 255, 255, 0.1);
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
      }

      .shape-acronym {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
        margin-top: 15px;
      }

      .shape-item {
        background: rgba(255, 255, 255, 0.2);
        padding: 10px;
        border-radius: 6px;
        text-align: center;
        font-weight: 500;
      }

      .area-section {
        margin-bottom: 40px;
        padding: 25px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        page-break-inside: avoid;
      }

      .area-header {
        margin-bottom: 25px;
      }

      .profile-name {
        font-size: 24px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 10px;
      }

      .profile-description {
        font-size: 16px;
        color: #6b7280;
        line-height: 1.6;
      }

      .score-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
      }

      .score-table th,
      .score-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
      }

      .score-table th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #374151;
      }

      .strengths-development {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 25px;
      }

      .strengths,
      .development {
        background: #f9fafb;
        padding: 20px;
        border-radius: 8px;
      }

      .strengths h4 {
        color: #059669;
        margin-bottom: 15px;
      }

      .development h4 {
        color: #dc2626;
        margin-bottom: 15px;
      }

      @media print {
        .report-container {
          box-shadow: none;
          margin: 0;
          padding: 15mm;
        }

        .section {
          page-break-inside: avoid;
        }
      }
    `;
  }

  /**
   * Generate detailed area sections with new structure
   */
  private generateDetailedAreaSections(
    assessmentData: any,
    detailedReports: any
  ): string {
    const areas = [
      { key: 'strength', title: '💪 Strength Analysis', color: '#ef4444' },
      { key: 'heart', title: '❤️ Heart Analysis', color: '#ec4899' },
      { key: 'abilities', title: '🧠 Abilities Analysis', color: '#f59e0b' },
      {
        key: 'personality',
        title: '👤 Personality Analysis',
        color: '#3b82f6',
      },
      { key: 'experience', title: '📈 Experience Analysis', color: '#22c55e' },
    ];

    return areas
      .map((area) => {
        const areaData = assessmentData[area.key];
        const areaReport = detailedReports[`${area.key}_report`];

        if (!areaData) return '';

        return `
        <section class="area-section" style="border-left: 4px solid ${
          area.color
        };">
          <h2>${area.title}</h2>

          <!-- Profile Name and Description -->
          <div class="area-header">
            <h3 class="profile-name">${
              areaData.primary || areaData.type || 'Unknown'
            }</h3>
            <p class="profile-description">${this.getAreaDescription(
              area.key,
              areaData
            )}</p>
          </div>

          <!-- Score Breakdown -->
          <div class="score-breakdown">
            <h4>Score Breakdown</h4>
            ${this.generateScoreTable(areaData.category_scores || {})}
          </div>

          <!-- Key Characteristics -->
          <div class="characteristics">
            <h4>Key Characteristics</h4>
            <ul>
              ${this.getAreaCharacteristics(area.key, areaData)
                .map((char) => `<li>${char}</li>`)
                .join('')}
            </ul>
          </div>

          <!-- Strengths and Development Areas -->
          <div class="strengths-development">
            <div class="strengths">
              <h4>Strengths</h4>
              <ul>
                ${this.getAreaStrengths(area.key, areaData)
                  .map((strength) => `<li>${strength}</li>`)
                  .join('')}
              </ul>
            </div>
            <div class="development">
              <h4>Development Areas</h4>
              <ul>
                ${this.getAreaDevelopment(area.key, areaData)
                  .map((dev) => `<li>${dev}</li>`)
                  .join('')}
              </ul>
            </div>
          </div>
        </section>
      `;
      })
      .join('');
  }

  /**
   * Generate score table for area
   */
  private generateScoreTable(categoryScores: any): string {
    if (!categoryScores || Object.keys(categoryScores).length === 0) {
      return '<p>No score data available</p>';
    }

    const sortedScores = Object.entries(categoryScores).sort(
      ([, a], [, b]) => Number(b) - Number(a)
    );

    return `
      <table class="score-table">
        <thead>
          <tr>
            <th>Category</th>
            <th>Score</th>
            <th>Level</th>
          </tr>
        </thead>
        <tbody>
          ${sortedScores
            .map(
              ([category, score]) => `
            <tr>
              <td>${category}</td>
              <td>${score}</td>
              <td>${this.getScoreLevel(Number(score))}</td>
            </tr>
          `
            )
            .join('')}
        </tbody>
      </table>
    `;
  }

  /**
   * Get score level
   */
  private getScoreLevel(score: number): string {
    if (score >= 36) return 'Tinggi';
    if (score >= 21) return 'Moderat';
    return 'Rendah';
  }

  /**
   * Get area description
   */
  private getAreaDescription(area: string, areaData: any): string {
    const descriptions: { [key: string]: string } = {
      strength:
        'Your natural talents and core competencies that drive your effectiveness.',
      heart: 'Your passions and interests that energize and motivate you.',
      abilities:
        'Your developed skills and capabilities across different domains.',
      personality: 'Your behavioral patterns and communication style.',
      experience: 'Your background and journey that shapes your perspective.',
    };
    return descriptions[area] || 'Your unique profile in this area.';
  }

  /**
   * Get area characteristics
   */
  private getAreaCharacteristics(area: string, areaData: any): string[] {
    // This would ideally come from guidance content
    const defaultCharacteristics: { [key: string]: string[] } = {
      strength: ['Natural talent', 'Core competency', 'Effortless excellence'],
      heart: ['Passionate interest', 'Energy source', 'Motivational driver'],
      abilities: ['Developed skill', 'Learned capability', 'Applied knowledge'],
      personality: [
        'Behavioral pattern',
        'Communication style',
        'Interaction preference',
      ],
      experience: [
        'Background knowledge',
        'Learned wisdom',
        'Practical insight',
      ],
    };
    return defaultCharacteristics[area] || ['Unique characteristic'];
  }

  /**
   * Get area strengths
   */
  private getAreaStrengths(area: string, areaData: any): string[] {
    const defaultStrengths: { [key: string]: string[] } = {
      strength: [
        'Leverages natural talents effectively',
        'Demonstrates consistent excellence',
      ],
      heart: ['Brings passion to work', 'Motivates others through enthusiasm'],
      abilities: [
        'Applies skills strategically',
        'Continues learning and growing',
      ],
      personality: ['Communicates effectively', 'Builds strong relationships'],
      experience: [
        'Draws from rich background',
        'Provides valuable perspective',
      ],
    };
    return defaultStrengths[area] || ['Demonstrates competence in this area'];
  }

  /**
   * Get area development opportunities
   */
  private getAreaDevelopment(area: string, areaData: any): string[] {
    const defaultDevelopment: { [key: string]: string[] } = {
      strength: [
        'Explore complementary strengths',
        'Develop supporting skills',
      ],
      heart: ['Broaden interest areas', 'Connect passion to purpose'],
      abilities: ['Strengthen weaker areas', 'Develop new capabilities'],
      personality: [
        'Adapt style to situations',
        'Build emotional intelligence',
      ],
      experience: ['Seek new challenges', 'Share knowledge with others'],
    };
    return defaultDevelopment[area] || ['Continue growth in this area'];
  }
}
