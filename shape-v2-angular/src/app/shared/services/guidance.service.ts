import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, forkJoin } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface GuidanceContent {
  id: number;
  area: string;
  profile_name: string;
  profile_code?: string;
  description: string;
  characteristics?: string[];
  communication_style?: any;
  strengths?: string[];
  weaknesses?: string[];
  team_roles?: any;
  development_tips?: string[];
  inspirational_figures?: string[];
  creative_ideas?: string[];
  love_language?: string[];
  hidden_strengths?: string[];
  warnings?: string[];
  combinations?: any;
  superpower?: string;
  min_score: number;
  max_score: number;
  active: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

@Injectable({
  providedIn: 'root',
})
export class GuidanceService {
  private http = inject(HttpClient);
  private apiUrl = `${environment.apiUrl}/guidance`;

  /**
   * Get all guidance content for all areas
   */
  getAllAreas(): Observable<{ [key: string]: GuidanceContent[] }> {
    return this.http
      .get<ApiResponse<{ [key: string]: GuidanceContent[] }>>(
        `${this.apiUrl}/areas`
      )
      .pipe(
        map((response) => response.data),
        catchError((error) => {
          console.error('Error fetching all areas guidance:', error);
          return of({});
        })
      );
  }

  /**
   * Get guidance content by area
   */
  getByArea(area: string): Observable<GuidanceContent[]> {
    return this.http
      .get<ApiResponse<GuidanceContent[]>>(`${this.apiUrl}/${area}`)
      .pipe(
        map((response) => response.data),
        catchError((error) => {
          console.error(`Error fetching guidance for area ${area}:`, error);
          return of([]);
        })
      );
  }

  /**
   * Get specific guidance content by area and profile
   */
  getByAreaAndProfile(
    area: string,
    profileName: string
  ): Observable<GuidanceContent | null> {
    // Skip API call for "Unknown" or empty profiles
    if (
      !profileName ||
      profileName === 'Unknown' ||
      profileName.trim() === ''
    ) {
      return of(null);
    }

    return this.http
      .get<ApiResponse<GuidanceContent>>(
        `${this.apiUrl}/${area}/${encodeURIComponent(profileName)}`
      )
      .pipe(
        map((response) => response.data),
        catchError((error) => {
          console.error(
            `Error fetching guidance for ${area}/${profileName}:`,
            error
          );
          return of(null);
        })
      );
  }

  /**
   * Get guidance content by score
   */
  getByScore(area: string, score: number): Observable<GuidanceContent | null> {
    return this.http
      .get<ApiResponse<GuidanceContent>>(
        `${this.apiUrl}/${area}/score/${score}`
      )
      .pipe(
        map((response) => response.data),
        catchError((error) => {
          console.error(
            `Error fetching guidance for ${area} with score ${score}:`,
            error
          );
          return of(null);
        })
      );
  }

  /**
   * Get comprehensive guidance for all areas based on assessment results
   */
  getComprehensiveGuidance(
    results: any
  ): Observable<{ [key: string]: GuidanceContent }> {
    return this.http
      .post<ApiResponse<{ [key: string]: GuidanceContent }>>(
        `${this.apiUrl}/comprehensive`,
        { results }
      )
      .pipe(
        map((response) => response.data),
        catchError((error) => {
          console.error('Error fetching comprehensive guidance:', error);
          return of({});
        })
      );
  }

  /**
   * Get guidance content for multiple areas and profiles
   */
  getMultipleGuidance(
    requests: { area: string; profileName: string }[]
  ): Observable<{ [key: string]: GuidanceContent | null }> {
    // Handle empty requests array
    if (requests.length === 0) {
      return of({});
    }

    const observables = requests.map((req) =>
      this.getByAreaAndProfile(req.area, req.profileName).pipe(
        map((content) => ({
          key: req.area, // Use area as key instead of area_profileName
          content,
        })),
        catchError((error) => {
          console.warn(
            `Failed to load guidance for ${req.area}/${req.profileName}:`,
            error
          );
          // Return null content but don't fail the observable
          return of({
            key: req.area,
            content: null,
          });
        })
      )
    );

    return forkJoin(observables).pipe(
      map((results) => {
        const guidance: { [key: string]: GuidanceContent | null } = {};
        results.forEach((result) => {
          guidance[result.key] = result.content;
        });
        console.log('Multiple guidance loaded:', guidance);
        return guidance;
      }),
      catchError((error) => {
        console.error('Error fetching multiple guidance:', error);
        return of({});
      })
    );
  }

  /**
   * Helper method to get guidance for SHAPE results
   */
  getShapeGuidance(
    shapeResults: any
  ): Observable<{ [key: string]: GuidanceContent | null }> {
    const requests: { area: string; profileName: string }[] = [];

    if (shapeResults.strength?.primary) {
      requests.push({
        area: 'strength',
        profileName: shapeResults.strength.primary,
      });
    }
    if (shapeResults.heart?.primary) {
      requests.push({ area: 'heart', profileName: shapeResults.heart.primary });
    }
    if (shapeResults.abilities?.primary) {
      requests.push({
        area: 'abilities',
        profileName: shapeResults.abilities.primary,
      });
    }
    if (shapeResults.personality?.type) {
      requests.push({
        area: 'personality',
        profileName: shapeResults.personality.type,
      });
    }
    if (shapeResults.experience?.pattern) {
      requests.push({
        area: 'experience',
        profileName: shapeResults.experience.pattern,
      });
    }

    return this.getMultipleGuidance(requests);
  }

  /**
   * Get fallback guidance content from local files if API fails
   */
  private getFallbackGuidance(
    area: string,
    profileName: string
  ): GuidanceContent | null {
    // This would contain fallback data extracted from the markdown files
    // For now, return null and rely on API
    return null;
  }

  /**
   * Format guidance content for display
   */
  formatGuidanceForDisplay(content: GuidanceContent): any {
    return {
      title: content.profile_name,
      description: content.description,
      superpower: content.superpower,
      characteristics: content.characteristics || [],
      strengths: content.strengths || [],
      weaknesses: content.weaknesses || [],
      developmentTips: content.development_tips || [],
      teamRoles: content.team_roles || {},
      communicationStyle: content.communication_style || {},
      inspirationalFigures: content.inspirational_figures || [],
      creativeIdeas: content.creative_ideas || [],
      loveLanguage: content.love_language || [],
      hiddenStrengths: content.hidden_strengths || [],
      warnings: content.warnings || [],
      combinations: content.combinations || {},
    };
  }

  /**
   * Get score range for a profile
   */
  getScoreRange(content: GuidanceContent): { min: number; max: number } {
    return {
      min: content.min_score,
      max: content.max_score,
    };
  }

  /**
   * Check if a score falls within a profile's range
   */
  isScoreInRange(score: number, content: GuidanceContent): boolean {
    return score >= content.min_score && score <= content.max_score;
  }
}
