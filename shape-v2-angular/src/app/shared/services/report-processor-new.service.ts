import { Injectable, inject } from '@angular/core';
import { AssessmentScoringService } from './assessment-scoring.service';

@Injectable({
  providedIn: 'root',
})
export class ReportProcessorService {
  private scoringService = inject(AssessmentScoringService);

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {}

  /**
   * Process SHAPE assessment results into comprehensive report
   */
  processShapeReport(rawResults: any): any {
    // Check if assessment data is complete
    if (!this.isAssessmentDataComplete(rawResults)) {
      return null; // Will trigger warning in component
    }

    const processedReport = {
      integrative_profile: this.generateIntegrativeProfile(rawResults),
      strength_profile: this.processStrengthProfile(rawResults.strength),
      heart_profile: this.processHeartProfile(rawResults.heart),
      abilities_profile: this.processAbilitiesProfile(rawResults.abilities),
      personality_profile: this.processPersonalityProfile(
        rawResults.personality
      ),
      experience_profile: this.processExperienceProfile(rawResults.experience),
      genius_zone: this.identifyGeniusZone(rawResults),
      development_plan: this.createDevelopmentPlan(rawResults),
      wisdom_quote: this.selectWisdomQuote(rawResults),
      categories: this.processHighScoreCategories(rawResults.categories || []),
      feedback: this.generateComprehensiveFeedback(rawResults),
    };

    return processedReport;
  }

  /**
   * Check if assessment data is complete for report generation
   */
  private isAssessmentDataComplete(results: any): boolean {
    if (!results || typeof results !== 'object') {
      return false;
    }

    const requiredComponents = [
      'strength',
      'heart',
      'abilities',
      'personality',
      'experience',
    ];

    for (const component of requiredComponents) {
      if (!results[component] || Object.keys(results[component]).length === 0) {
        return false;
      }
    }

    if (
      !results.categories ||
      !Array.isArray(results.categories) ||
      results.categories.length === 0
    ) {
      return false;
    }

    return true;
  }

  /**
   * Process categories with high scores and provide detailed explanations
   */
  private processHighScoreCategories(categories: any[]): any[] {
    return categories
      .filter((cat) => cat.score >= 70) // High score threshold
      .map((cat) => ({
        ...cat,
        explanation: this.getCategoryExplanation(cat.name, cat.score),
        insights: this.getCategoryInsights(cat.name, cat.score),
      }))
      .sort((a, b) => b.score - a.score); // Sort by score descending
  }

  /**
   * Get detailed explanation for high-scoring categories
   */
  private getCategoryExplanation(categoryName: string, score: number): string {
    const explanations: { [key: string]: string } = {
      Communication: `Dengan skor ${score}%, Anda menunjukkan kemampuan luar biasa dalam "menyulam realitas" - menyambung fragmen informasi menjadi narasi kohesif. Anda memandang keheningan sebagai ruang kosong untuk diisi makna dan mampu mengubah kebisingan menjadi simfoni.`,

      Leadership: `Skor ${score}% menunjukkan Anda adalah pemimpin natural yang mampu membaca kebutuhan audiens dan memobilisasi tim menuju perubahan. Seperti "Inspiring Connector", Anda ahli menemukan kesamaan dari perbedaan dan membangun aliansi strategis.`,

      Creativity: `Dengan skor ${score}%, Anda memiliki mata seni yang memandang dunia sebagai palet emosi yang hidup. Anda mampu mengubah rutinitas menjadi pertunjukan dan mengkomunikasikan yang tak terucap melalui metafora - sebuah "terapi kejut estetika".`,

      'Problem Solving': `Skor ${score}% mencerminkan kemampuan Anda sebagai "Insightful Truth-Seeker" yang memiliki analisis tajam untuk mencari akar masalah. Anda memiliki radar untuk mendeteksi ketidakkonsistenan dan bias tersembunyi.`,

      Collaboration: `Dengan skor ${score}%, Anda adalah "Community Architect" yang mampu menciptakan lingkungan psikologis aman di mana orang bisa berkembang maksimal. Anda memiliki radar tinggi untuk kebutuhan emosional tim.`,

      Innovation: `Skor ${score}% menunjukkan Anda adalah "Visionary Pioneer" dengan pola pikir futuristik. Anda secara natural melihat kemungkinan di tempat orang lain melihat jalan buntu dan berani mengambil risiko terukur.`,

      'Technical Skills': `Dengan skor ${score}%, Anda memiliki kemampuan "Detail Master" yang memastikan akurasi dan kualitas. Anda sistematis dalam pendekatan dan memiliki kemampuan problem solving teknis yang kuat.`,

      'Analytical Thinking': `Skor ${score}% mencerminkan kemampuan kognitif Anda yang tajam. Seperti "Clarifying Mentor", Anda mampu menyusun pengetahuan sistematis dan mengubah kompleksitas menjadi kejelasan.`,
    };

    return (
      explanations[categoryName] ||
      `Dengan skor ${score}%, Anda menunjukkan keunggulan yang signifikan dalam ${categoryName}. Ini adalah salah satu kekuatan natural Anda yang dapat dikembangkan lebih lanjut.`
    );
  }

  /**
   * Get actionable insights for high-scoring categories
   */
  private getCategoryInsights(categoryName: string, score: number): string[] {
    const insights: { [key: string]: string[] } = {
      Communication: [
        'Kembangkan kemampuan storytelling untuk berbagai audiens',
        'Latih teknik active listening dan empathetic communication',
        'Ciptakan konten yang menghubungkan ide kompleks dengan bahasa sederhana',
      ],

      Leadership: [
        'Fokus pada authentic leadership style yang sesuai kepribadian',
        'Bangun kemampuan delegation dan team empowerment',
        'Kembangkan emotional intelligence untuk memimpin dengan empati',
      ],

      Creativity: [
        'Eksplor medium kreatif baru untuk memperluas ekspresi',
        'Gabungkan kreativitas dengan problem solving praktis',
        'Ciptakan lingkungan yang mendukung kreativitas berkelanjutan',
      ],

      'Problem Solving': [
        'Latih systematic thinking dan root cause analysis',
        'Kembangkan kemampuan pattern recognition',
        'Praktikkan design thinking untuk solusi inovatif',
      ],

      Collaboration: [
        'Pelajari different collaboration styles untuk berbagai tim',
        'Kembangkan conflict resolution skills',
        'Praktikkan inclusive leadership approach',
      ],

      Innovation: [
        'Buat sistem untuk capture dan develop ide-ide baru',
        'Latih rapid prototyping dan testing mindset',
        'Bangun network dengan fellow innovators',
      ],
    };

    return (
      insights[categoryName] || [
        `Manfaatkan kekuatan ${categoryName} untuk menciptakan value unik`,
        'Cari mentor atau coach yang dapat membantu mengembangkan area ini',
        'Praktikkan skill ini dalam proyek nyata untuk memperdalam kemampuan',
      ]
    );
  }

  /**
   * Generate integrative SHAPE profile with nature metaphors
   */
  private generateIntegrativeProfile(results: any): string {
    const strengthMetaphor = this.getStrengthMetaphor(
      results.strength?.primary
    );
    const heartDomain = results.heart?.primary || 'Personal Development';
    const abilityType = results.abilities?.primary || 'Cognitive Skills';
    const personalityStyle = results.personality?.type || 'Balanced Individual';

    return `<strong>${
      results.userName || 'Anda'
    }</strong> adalah <em>${strengthMetaphor}</em> di bidang <strong>${heartDomain}</strong>. 
            Dengan kekuatan <strong>${
              results.strength?.primary || 'Natural Talent'
            }</strong> dan kemampuan <strong>${abilityType}</strong>, 
            gaya <strong>${personalityStyle}</strong> Anda menciptakan arketipe unik <strong>'${this.generateArchetype(
      results
    )}'</strong>.`;
  }

  /**
   * Process Strength profile based on comprehensive guidance
   */
  private processStrengthProfile(strengthData: any): any {
    if (!strengthData) return null;

    const strengthTypes: { [key: string]: any } = {
      'Visionary Pioneers': {
        description:
          'Pola pikir futuristik & berorientasi pada kemungkinan baru. Berani mengambil risiko terukur dan tidak nyaman dengan rutinitas.',
        superpower:
          'Anda secara natural melihat kemungkinan di tempat orang lain melihat jalan buntu.',
        teamContribution:
          'Sebagai Innovation Catalyst, Anda membantu tim beradaptasi sebelum disrupsi terjadi.',
        growthOpportunity:
          'Kembangkan kemampuan implementasi dan evaluasi proyek yang sistematis.',
        watchOut: 'Hati-hati meremehkan kompleksitas implementasi.',
        powerPartners: 'Detail-Oriented Implementers',
        famousFigures: ['Elon Musk', 'Steve Jobs', 'Nikola Tesla'],
      },
      'Insightful Truth-Seekers': {
        description:
          'Kepekaan tinggi pada ketidaksesuaian dengan dorongan kuat untuk integritas & keadilan. Analisis akar masalah yang tajam.',
        superpower:
          'Anda memiliki radar untuk mendeteksi ketidakkonsistenan dan bias tersembunyi.',
        teamContribution:
          'Sebagai Ethical Guardian, Anda melindungi tim dari keputusan yang merugikan jangka panjang.',
        growthOpportunity:
          'Latih kemampuan menyampaikan kritik dengan solusi konstruktif.',
        watchOut:
          'Jangan terlalu fokus pada masalah tanpa menawarkan alternatif.',
        powerPartners: 'Inspiring Connectors',
        famousFigures: ['Warren Buffett', 'Mahatma Gandhi', 'Maya Angelou'],
      },
      'Inspiring Connectors': {
        description:
          'Kemampuan membaca kebutuhan audiens dengan energi tinggi dalam interaksi sosial. Ahli menemukan kesamaan dari perbedaan.',
        superpower:
          'Anda mengubah ide individual menjadi gerakan kolektif melalui storytelling yang kuat.',
        teamContribution:
          'Sebagai Community Architect, Anda membangun ekosistem yang saling mendukung.',
        growthOpportunity:
          'Fokus pada konsistensi dan follow-through dalam komitmen.',
        watchOut:
          'Hati-hati dengan over-promise untuk menyenangkan semua orang.',
        powerPartners: 'Detail Masters',
        famousFigures: ['Oprah Winfrey', 'Tony Robbins', 'Barack Obama'],
      },
    };

    const primaryStrength = strengthData.primary || 'Balanced Individual';
    const profile = strengthTypes[primaryStrength] || {
      description:
        'Individu dengan kombinasi kekuatan yang seimbang dan adaptif',
      superpower:
        'Anda memiliki kemampuan untuk beradaptasi dengan berbagai situasi.',
      teamContribution:
        'Sebagai Versatile Contributor, Anda dapat mengisi berbagai peran sesuai kebutuhan.',
      growthOpportunity:
        'Identifikasi dan fokuskan pada kekuatan dominan Anda.',
      watchOut: 'Hati-hati menjadi generalis tanpa keahlian yang mendalam.',
      powerPartners: 'Specialists in complementary areas',
      famousFigures: ['Leonardo da Vinci', 'Benjamin Franklin'],
    };

    return {
      primary: primaryStrength,
      ...profile,
      score: strengthData.score || 0,
    };
  }

  /**
   * Process Heart profile with detailed guidance explanations
   */
  private processHeartProfile(heartData: any): any {
    if (!heartData) return null;

    const heartTypes: { [key: string]: any } = {
      'Art & Entertainment': {
        description:
          'Memandang dunia sebagai palet emosi yang hidup. Mengubah rutinitas menjadi pertunjukan dan mengkomunikasikan yang tak terucap melalui metafora.',
        hiddenPower:
          'Kemampuan menyembuhkan melalui "terapi kejut estetika" - menggunakan keindahan tak terduga untuk memutus spiral pikiran negatif',
        warning:
          'Risiko "Keracunan Keindahan" - kehilangan kemampuan melihat nilai dalam yang biasa-biasa saja',
        inspirationalFigures: [
          'Puck (Midsummer Night Dream)',
          'Baron Munchausen',
          'Scheherazade',
        ],
      },
      Business: {
        description:
          'Melihat masalah sebagai model bisnis yang belum lahir. Memandang hubungan manusia sebagai jaringan nilai.',
        hiddenPower:
          'Kemampuan menciptakan "virus kemakmuran" - sistem yang menyebarkan kesejahteraan melalui interaksi alami',
        warning:
          'Bahaya "Logika Kalkulator" - mengukur segala sesuatu termasuk yang tak seharusnya terukur',
        inspirationalFigures: [
          'Midas (versi positif)',
          'Hermes',
          'Sisyphus yang Bahagia',
        ],
      },
      Education: {
        description:
          'Memandang ketidaktahuan sebagai lahan subur. Melihat kurikulum dalam alur kehidupan dan mengubah kebosanan menjadi keingintahuan.',
        hiddenPower:
          'Kemampuan "penyambung titik tak terlihat" - menemukan pola dalam pengetahuan yang tampak acak',
        warning:
          'Bahaya "Kutukan Kepastian" - kehilangan keajaiban dalam proses mencari jawaban',
        inspirationalFigures: ['Prometheus', 'Sokrates', 'Thoth'],
      },
    };

    const primaryHeart = heartData.primary || 'Personal Development';
    const profile = heartTypes[primaryHeart] || {
      description:
        'Passion yang mendalam untuk pengembangan diri dan pertumbuhan berkelanjutan',
      hiddenPower:
        'Kemampuan beradaptasi dan menemukan makna dalam berbagai situasi',
      warning: 'Jangan terlalu sering berganti fokus tanpa mendalami satu area',
      inspirationalFigures: ['Leonardo da Vinci', 'Carl Jung'],
    };

    return {
      primary: primaryHeart,
      ...profile,
      score: heartData.score || 0,
    };
  }

  /**
   * Other processing methods (simplified for space)
   */
  private processAbilitiesProfile(abilitiesData: any): any {
    if (!abilitiesData) return null;
    return {
      primary: abilitiesData.primary || 'Cognitive Skills',
      description: 'Kemampuan yang telah terbukti melalui assessment',
      score: abilitiesData.score || 0,
    };
  }

  private processPersonalityProfile(personalityData: any): any {
    if (!personalityData) return null;
    return {
      type: personalityData.type || 'Balanced Individual',
      description: 'Gaya kepribadian yang dominan',
      score: personalityData.score || 0,
    };
  }

  private processExperienceProfile(experienceData: any): any {
    if (!experienceData) return null;
    return {
      pattern: experienceData.pattern || 'Adaptive Learner',
      insight: experienceData.insight || 'Terus belajar dan beradaptasi',
    };
  }

  private identifyGeniusZone(results: any): any[] {
    return [
      {
        field: 'Personal Development',
        reason: 'Area pengembangan potensial berdasarkan profil SHAPE',
        evidence: 'Konvergensi dari pola assessment Anda',
      },
    ];
  }

  private createDevelopmentPlan(results: any): any[] {
    return [
      {
        title: 'Leverage Your Strengths',
        description: 'Aktifkan dan kembangkan kekuatan natural Anda',
        timeline: '30-90 hari',
      },
    ];
  }

  private selectWisdomQuote(results: any): string {
    return 'Alon-alon asal kelakon - Perlahan namun pasti';
  }

  private generateComprehensiveFeedback(results: any): string {
    return `<div class="comprehensive-feedback">
      <h4>Insight Mendalam tentang Profil SHAPE Anda</h4>
      <p>Berdasarkan hasil assessment yang lengkap, Anda menunjukkan pola unik yang mencerminkan 
      potensi luar biasa dalam mengintegrasikan berbagai dimensi SHAPE.</p>
    </div>`;
  }

  private getStrengthMetaphor(strength: string): string {
    const metaphors: { [key: string]: string } = {
      'Visionary Pioneers': 'Sungai yang Membuka Lembah Baru',
      'Insightful Truth-Seekers': 'Mercusuar Kebijaksanaan',
      'Inspiring Connectors': 'Jembatan yang Menghubungkan Pulau-pulau',
    };
    return metaphors[strength] || 'Penjelajah Potensial';
  }

  private generateArchetype(results: any): string {
    const strength = results.strength?.primary;
    const heart = results.heart?.primary;

    if (strength === 'Visionary Pioneers' && heart === 'Education') {
      return 'Guru Visioner';
    }
    if (strength === 'Inspiring Connectors' && heart === 'Business') {
      return 'Entrepreneur Sosial';
    }

    return 'Catalyst Transformasi';
  }
}
