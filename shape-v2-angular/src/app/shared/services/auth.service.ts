import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private http = inject(HttpClient);

  private tokenKey = 'auth_token';
  private userKey = 'user_data';
  private apiUrl = environment.apiUrl;

  private userSubject = new BehaviorSubject<any>(null);
  public user$ = this.userSubject.asObservable();

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {
    // Initialize user from local storage
    this.loadUser();
  }

  private loadUser(): void {
    const userData = localStorage.getItem(this.userKey);
    if (userData) {
      this.userSubject.next(JSON.parse(userData));
    }
  }

  login(email: string, password: string): Observable<any> {
    return this.http
      .post<any>(`${this.apiUrl}/login`, { email, password })
      .pipe(
        tap((response) => {
          if (response.token && response.user) {
            localStorage.setItem(this.tokenKey, response.token);
            localStorage.setItem(this.userKey, JSON.stringify(response.user));
            this.userSubject.next(response.user);
          }
        }),
        catchError((error) => {
          return throwError(() => error);
        })
      );
  }

  signup(
    name: string,
    email: string,
    password: string,
    password_confirmation: string
  ): Observable<any> {
    return this.http
      .post<any>(`${this.apiUrl}/register`, {
        name,
        email,
        password,
        password_confirmation,
      })
      .pipe(
        tap((response) => {
          if (response.token && response.user) {
            localStorage.setItem(this.tokenKey, response.token);
            localStorage.setItem(this.userKey, JSON.stringify(response.user));
            this.userSubject.next(response.user);
          }
        }),
        catchError((error) => {
          return throwError(() => error);
        })
      );
  }

  logout(): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/logout`, {}).pipe(
      tap(() => {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.userKey);
        this.userSubject.next(null);
      }),
      catchError((error) => {
        // Even if the API call fails, clear the local data
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.userKey);
        this.userSubject.next(null);
        return throwError(() => error);
      })
    );
  }

  /**
   * Clear all authentication data without calling API
   * Use when token is invalid
   */
  clearAuth(): void {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
    this.userSubject.next(null);
  }

  isLoggedIn(): boolean {
    return !!this.getToken();
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  getCurrentUser(): Observable<any> {
    if (this.isLoggedIn()) {
      return this.http.get<any>(`${this.apiUrl}/user`).pipe(
        map((response) => response),
        tap((user) => {
          localStorage.setItem(this.userKey, JSON.stringify(user));
          this.userSubject.next(user);
        }),
        catchError((error) => {
          if (error.status === 401) {
            this.logout();
          }
          return throwError(() => error);
        })
      );
    }
    return throwError(() => new Error('User not logged in'));
  }

  /**
   * Validate the current token by making a lightweight API call
   * Returns Observable<boolean> that indicates if token is valid
   */
  validateToken(): Observable<boolean> {
    if (!this.getToken()) {
      return throwError(() => new Error('No token available'));
    }

    return this.http.get<any>(`${this.apiUrl}/validate-token`).pipe(
      map(() => true),
      catchError((error) => {
        console.error('Token validation failed:', error);

        // If unauthorized or token invalid, clear auth data
        if (error.status === 401 || error.status === 403) {
          this.clearAuth();
        }

        return throwError(() => error);
      })
    );
  }
}
