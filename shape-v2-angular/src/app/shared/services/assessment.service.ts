import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { environment } from '../../../environments/environment';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class AssessmentService {
  private http = inject(HttpClient);

  private apiUrl = environment.apiUrl;

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {}

  getAssessmentCategories(): Observable<any[]> {
    return this.http.get<any>(`${this.apiUrl}/assessments`).pipe(
      map((response) => {
        const assessments = response.data.map((assessment: any) => {
          return {
            id: assessment.id,
            name: assessment.name,
            description: assessment.description,
            icon: assessment.icon,
            type: assessment.type,
            slug: assessment.slug,
            // We'll update these with actual counts later
            totalQuestions: 0,
            completedQuestions: 0,
            progress: assessment.progress || 0,
          };
        });

        // Ambil data assessment dengan progress dari API
        this.getUserAssessments().subscribe((userAssessments) => {
          // Update progress data pada assessment categories
          assessments.forEach((category: any) => {
            // Cari assessment yang cocok berdasarkan ID
            const matchingAssessment = userAssessments.find(
              (a) => a.id === category.id
            );
            if (matchingAssessment) {
              // Gunakan progress dari data pengguna
              category.progress = matchingAssessment.progress || 0;
            }
          });
        });

        // Get question counts for each assessment
        assessments.forEach((assessment: any) => {
          this.getQuestionsByAssessmentId(assessment.id).subscribe({
            next: (questions) => {
              assessment.totalQuestions = questions.length;

              // Juga ambil jawaban pengguna untuk assessment ini
              this.getUserAnswers(assessment.id).subscribe({
                next: (answers) => {
                  if (answers && answers.length > 0) {
                    // Hitung pertanyaan yang telah dijawab
                    const questionIds = questions.map((q: any) =>
                      q.id.toString()
                    );
                    const answeredIds = answers.map((a: any) =>
                      a.question_id.toString()
                    );

                    const uniqueAnsweredIds = new Set(answeredIds);
                    assessment.completedQuestions = uniqueAnsweredIds.size;

                    // Jika tidak ada progress yang diset dari getUserAssessments
                    // Hitung progress berdasarkan jawaban
                    if (!assessment.progress || assessment.progress === 0) {
                      assessment.progress = Math.round(
                        (assessment.completedQuestions /
                          assessment.totalQuestions) *
                          100
                      );
                    }
                  }
                },
                error: (error) => {
                  console.warn(
                    'Error loading user answers for assessment',
                    assessment.id,
                    error
                  );
                },
              });
            },
          });
        });

        return assessments;
      })
    );
  }

  getQuestionsByAssessmentId(assessmentId: number): Observable<any[]> {
    return this.http
      .get<any>(`${this.apiUrl}/assessments/${assessmentId}/questions`)
      .pipe(
        map((response) => {
          return response.data.map((question: any) => {
            return {
              id: question.question_id,
              question_id: question.id, // The database ID
              question_text: question.question_text,
              question_type: question.question_type,
              subprompts: question.subprompts,
              icon: question.icon,
              category_id: question.assessment_category_id,
            };
          });
        })
      );
  }

  saveAnswer(questionId: number, answer: any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/answers`, {
      question_id: questionId,
      assessment_id: answer.assessmentId,
      answer_text: answer.answerType === 'narrative' ? answer.text : null,
      answer_value: answer.answerType === 'rating' ? answer.value : null,
      subprompt_answers: answer.subpromptAnswers || null,
    });
  }

  saveMultipleAnswers(assessmentId: number, answers: any[]): Observable<any> {
    const formattedAnswers = answers.map((answer) => ({
      question_id: answer.questionId,
      answer_text: answer.answerType === 'narrative' ? answer.text : null,
      answer_value: answer.answerType === 'rating' ? answer.value : null,
      subprompt_answers: answer.subpromptAnswers || null,
    }));

    return this.http.post<any>(`${this.apiUrl}/answers/bulk`, {
      assessment_id: assessmentId,
      answers: formattedAnswers,
    });
  }

  completeAssessment(assessmentId: number): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/assessments/${assessmentId}/process`,
      {}
    );
  }

  getAssessmentResults(assessmentId: number): Observable<any> {
    return this.http
      .get<any>(`${this.apiUrl}/assessments/${assessmentId}/results`)
      .pipe(map((response) => response.data));
  }

  getAllResults(): Observable<any> {
    return this.http
      .get<any>(`${this.apiUrl}/results`)
      .pipe(map((response) => response.data));
  }

  getUserAnswers(assessmentId: number): Observable<any> {
    return this.http
      .get<any>(`${this.apiUrl}/assessments/${assessmentId}/answers`)
      .pipe(
        map((response) => {
          console.log('Retrieved user answers:', response);
          return response.data || [];
        })
      );
  }

  getUserAssessments(): Observable<any[]> {
    return this.http.get<any>(`${this.apiUrl}/user/assessments`).pipe(
      map((response) => {
        const assessments = response.data.map((assessment: any) => {
          return {
            id: assessment.id,
            name: assessment.name,
            description: assessment.description,
            progress: assessment.progress || 0,
            status: this.determineAssessmentStatus(assessment.progress || 0),
            current_question_index: assessment.current_question_index || 0,
          };
        });
        return assessments;
      })
    );
  }

  /**
   * Helper function to determine assessment status based on progress
   */
  private determineAssessmentStatus(progress: number): string {
    if (progress === 0) return 'Not Started';
    if (progress === 100) return 'Completed';
    return 'In Progress';
  }

  /**
   * Save the current user's position in assessment with retry logic
   * @param assessmentId The ID of the assessment
   * @param questionIndex The index of the current question
   */
  saveAssessmentProgress(
    assessmentId: number,
    questionIndex: number
  ): Observable<any> {
    console.log(
      `Saving progress: assessment ${assessmentId}, question index ${questionIndex}`
    );

    return this.http
      .post<any>(`${this.apiUrl}/assessments/${assessmentId}/progress`, {
        current_question_index: questionIndex,
      })
      .pipe(
        map((response) => {
          console.log('Progress saved successfully:', response);
          return response;
        }),
        catchError((error) => {
          console.error('Error saving progress:', error);
          // Return a failed observable but don't break the app flow
          return throwError(() => error);
        })
      );
  }

  /**
   * Get the user's last position in assessment with fallback
   * @param assessmentId The ID of the assessment
   */
  getAssessmentProgress(assessmentId: number): Observable<any> {
    console.log(`Loading progress for assessment ${assessmentId}`);

    return this.http
      .get<any>(`${this.apiUrl}/assessments/${assessmentId}/progress`)
      .pipe(
        map((response) => {
          const progressData = response.data || { current_question_index: 0 };
          console.log('Progress loaded:', progressData);
          return progressData;
        }),
        catchError((error) => {
          console.error('Error loading progress:', error);
          // Return a default progress object so the app can continue
          return of({ current_question_index: 0 });
        })
      );
  }
}
