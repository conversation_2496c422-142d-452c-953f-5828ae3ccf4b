import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { User } from './user-state.service';

// Extended user interface with assessment statistics
export interface UserWithAssessments extends User {
  registrationDate?: string;
  lastActive?: string;
  totalAssessments?: number;
  completedAssessments?: number;
  role?: string;
}

// Mock admin statistics
export interface AdminStats {
  totalUsers: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  totalAssessments: number;
  completionRate: number;
}

// Chart data interface
interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string;
  }[];
}

@Injectable({
  providedIn: 'root',
})
export class AdminService {
  constructor() {}

  /**
   * Get admin dashboard statistics
   */
  getAdminStats(): Observable<AdminStats> {
    // Mock data - will be replaced with actual API call
    const stats: AdminStats = {
      totalUsers: 87,
      newUsersThisWeek: 12,
      newUsersThisMonth: 34,
      totalAssessments: 245,
      completionRate: 68,
    };

    return of(stats);
  }

  /**
   * Get all users with their assessment statistics
   */
  getUsers(): Observable<UserWithAssessments[]> {
    // Mock data - will be replaced with actual API call
    const users: UserWithAssessments[] = [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        registrationDate: '2025-01-15T10:30:00Z',
        lastActive: '2025-06-20T14:45:00Z',
        totalAssessments: 3,
        completedAssessments: 2,
        role: 'user',
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        registrationDate: '2025-02-22T09:15:00Z',
        lastActive: '2025-06-22T11:20:00Z',
        totalAssessments: 5,
        completedAssessments: 5,
        role: 'user',
      },
      {
        id: '3',
        name: 'Bob Johnson',
        email: '<EMAIL>',
        registrationDate: '2025-03-10T16:40:00Z',
        lastActive: '2025-05-18T08:30:00Z',
        totalAssessments: 2,
        completedAssessments: 0,
        role: 'user',
      },
      {
        id: '4',
        name: 'Alice Williams',
        email: '<EMAIL>',
        registrationDate: '2025-04-05T13:25:00Z',
        lastActive: '2025-06-23T17:10:00Z',
        totalAssessments: 4,
        completedAssessments: 3,
        role: 'admin',
      },
      {
        id: '5',
        name: 'Sarah Miller',
        email: '<EMAIL>',
        registrationDate: '2025-05-18T08:50:00Z',
        lastActive: '2025-06-24T09:45:00Z',
        totalAssessments: 1,
        completedAssessments: 1,
        role: 'user',
      },
      {
        id: '6',
        name: 'Michael Brown',
        email: '<EMAIL>',
        registrationDate: '2025-06-01T11:10:00Z',
        lastActive: '2025-06-25T13:15:00Z',
        totalAssessments: 0,
        completedAssessments: 0,
        role: 'user',
      },
      {
        id: '7',
        name: 'Emily Davis',
        email: '<EMAIL>',
        registrationDate: '2025-01-30T15:20:00Z',
        lastActive: '2025-06-20T16:05:00Z',
        totalAssessments: 6,
        completedAssessments: 4,
        role: 'user',
      },
    ];

    return of(users);
  }

  /**
   * Get chart data for user registrations
   */
  getUserRegistrationChartData(): Observable<ChartData> {
    // Mock data - will be replaced with actual API call
    const chartData: ChartData = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          data: [12, 19, 15, 8, 22, 11],
          backgroundColor: '#4f46e5',
        },
      ],
    };

    return of(chartData);
  }

  /**
   * Get user's assessments
   */
  getUserAssessments(userId: string): Observable<any[]> {
    // Mock data - will be replaced with actual API call
    const assessments = [
      {
        id: 1,
        name: 'Experience Assessment',
        startedAt: '2025-06-10T09:30:00Z',
        completedAt: '2025-06-10T10:45:00Z',
        score: 85,
        status: 'Completed',
        progress: 100,
        answers: [
          {
            questionId: 101,
            answer: 'My previous role as team lead',
            score: 8,
          },
          {
            questionId: 102,
            answer: 'Implementing the new CRM system',
            score: 9,
          },
          { questionId: 103, answer: 'My international assignment', score: 7 },
        ],
      },
      {
        id: 2,
        name: 'Personality Assessment',
        startedAt: '2025-06-15T14:20:00Z',
        completedAt: '2025-06-15T15:10:00Z',
        score: 92,
        status: 'Completed',
        progress: 100,
        answers: [
          { questionId: 201, answer: 'Strongly Agree', score: 5 },
          { questionId: 202, answer: 'Agree', score: 4 },
          { questionId: 203, answer: 'Neutral', score: 3 },
        ],
      },
      {
        id: 3,
        name: 'Skills Assessment',
        startedAt: '2025-06-20T11:05:00Z',
        completedAt: null,
        score: null,
        status: 'In Progress',
        progress: 45,
        answers: [
          { questionId: 301, answer: 'Advanced', score: 5 },
          { questionId: 302, answer: 'Intermediate', score: 3 },
        ],
      },
    ];

    return of(assessments);
  }

  /**
   * Reset user's assessment progress
   */
  resetUserProgress(
    userId: string,
    assessmentId?: number
  ): Observable<boolean> {
    // This would be an API call in a real application
    console.log(
      `Resetting progress for user ${userId}, assessment ${
        assessmentId || 'all'
      }`
    );
    return of(true);
  }

  /**
   * Update user role
   */
  updateUserRole(userId: string, role: string): Observable<boolean> {
    // This would be an API call in a real application
    console.log(`Updating user ${userId} role to ${role}`);
    return of(true);
  }
}
