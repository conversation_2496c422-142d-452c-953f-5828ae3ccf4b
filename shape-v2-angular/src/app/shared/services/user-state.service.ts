import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AuthService } from './auth.service';
import { Router } from '@angular/router';

export interface User {
  id?: string;
  name?: string;
  email?: string;
  token?: string;
  role?: string;
}

@Injectable({
  providedIn: 'root',
})
export class UserStateService {
  private authService = inject(AuthService);
  private router = inject(Router);

  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser: Observable<User | null>;

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {
    // Initialize current user from local storage if available
    this.currentUserSubject = new BehaviorSubject<User | null>(
      this.getUserFromStorage()
    );
    this.currentUser = this.currentUserSubject.asObservable();

    // Check if user is logged in and initialize
    if (this.authService.isLoggedIn()) {
      this.initializeUser();
    }
  }

  // Initialize user state from AuthService
  private initializeUser(): void {
    this.authService.user$.subscribe((userData) => {
      if (userData) {
        const user: User = {
          id: userData.id.toString(),
          name: userData.name,
          email: userData.email,
          token: this.authService.getToken() || undefined,
          role: userData.role || 'user', // Default to 'user' if role not provided
        };
        this.currentUserSubject.next(user);
        localStorage.setItem('current_user', JSON.stringify(user));
      }
    });
  }

  private getUserFromStorage(): User | null {
    const userJson = localStorage.getItem('current_user');
    return userJson ? JSON.parse(userJson) : null;
  }

  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  public isLoggedIn(): boolean {
    return !!this.currentUserValue;
  }

  public login(email: string, password: string): Observable<any> {
    return new Observable((observer) => {
      this.authService.login(email, password).subscribe({
        next: (response) => {
          if (response.token && response.user) {
            this.initializeUser();
            observer.next(response);
            observer.complete();
          } else {
            observer.error(response);
          }
        },
        error: (error) => observer.error(error),
      });
    });
  }

  public signup(
    name: string,
    email: string,
    password: string,
    password_confirmation: string
  ): Observable<any> {
    return new Observable((observer) => {
      this.authService
        .signup(name, email, password, password_confirmation)
        .subscribe({
          next: (response) => {
            if (response.token && response.user) {
              this.initializeUser();
              observer.next(response);
              observer.complete();
            } else {
              observer.error(response);
            }
          },
          error: (error) => observer.error(error),
        });
    });
  }

  public logout(): void {
    this.authService.logout();
    localStorage.removeItem('current_user');
    this.currentUserSubject.next(null);
    this.router.navigate(['/login']);
  }

  public updateUser(user: User): void {
    this.currentUserSubject.next(user);
    localStorage.setItem('current_user', JSON.stringify(user));
  }
}
