import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AssessmentScoringService {
  constructor() {}

  /**
   * Calculate Strength profile based on answers
   */
  calculateStrengthProfile(answers: any[]): any {
    // Strength categories based on guidance
    const strengthCategories = {
      'Visionary Pioneers': [
        'adaptable',
        'innovative',
        'risk-taking',
        'future-focused',
      ],
      'Insightful Truth-Seekers': [
        'analytical',
        'principled',
        'detail-oriented',
        'systematic',
      ],
      'Inspiring Connectors': [
        'social',
        'persuasive',
        'energetic',
        'networking',
      ],
      'Steady Supporters': [
        'collaborative',
        'patient',
        'reliable',
        'supportive',
      ],
      'Detail Masters': ['precise', 'organized', 'thorough', 'quality-focused'],
    };

    const scores = this.calculateCategoryScores(answers, strengthCategories);
    const primary = this.findPrimaryCategory(scores);

    return {
      primary,
      score: scores[primary] || 0,
      secondary: this.findSecondaryCategory(scores, primary),
      all_scores: scores,
    };
  }

  /**
   * Calculate Heart profile based on passion indicators
   */
  calculateHeartProfile(answers: any[]): any {
    const heartCategories = {
      'Art & Entertainment': [
        'creative',
        'aesthetic',
        'expressive',
        'artistic',
      ],
      Business: [
        'entrepreneurial',
        'strategic',
        'profit-oriented',
        'market-focused',
      ],
      'Communication / Media': [
        'storytelling',
        'information',
        'media',
        'journalism',
      ],
      Family: ['relationships', 'nurturing', 'home', 'children'],
      'Health & Fitness': ['wellness', 'physical', 'nutrition', 'exercise'],
      Education: ['teaching', 'learning', 'knowledge', 'development'],
      Technology: ['innovation', 'digital', 'systems', 'automation'],
      'Social Impact': ['community', 'volunteering', 'service', 'charity'],
    };

    const scores = this.calculateCategoryScores(answers, heartCategories);
    const primary = this.findPrimaryCategory(scores);

    return {
      primary,
      score: scores[primary] || 0,
      passion_intensity: this.calculatePassionIntensity(scores),
      all_scores: scores,
    };
  }

  /**
   * Calculate Abilities profile from assessment
   */
  calculateAbilitiesProfile(answers: any[]): any {
    const abilityCategories = {
      'Cognitive Skills': [
        'analytical',
        'problem-solving',
        'logical',
        'strategic',
      ],
      'Social Skills': ['communication', 'empathy', 'leadership', 'teamwork'],
      'Technical Skills': ['technology', 'tools', 'systems', 'programming'],
      'Management Skills': [
        'organization',
        'planning',
        'coordination',
        'delegation',
      ],
      'Creative Skills': ['innovation', 'design', 'artistic', 'imagination'],
      'Physical Skills': ['coordination', 'strength', 'dexterity', 'endurance'],
    };

    const scores = this.calculateCategoryScores(answers, abilityCategories);
    const primary = this.findPrimaryCategory(scores);

    return {
      primary,
      score: scores[primary] || 0,
      top_three: this.getTopCategories(scores, 3),
      all_scores: scores,
    };
  }

  /**
   * Calculate Personality profile based on DISC-like model
   */
  calculatePersonalityProfile(answers: any[]): any {
    const personalityDimensions = {
      'Visionary Driver': [
        'decisive',
        'results-oriented',
        'direct',
        'fast-paced',
      ],
      'Dynamic Connector': [
        'enthusiastic',
        'optimistic',
        'people-oriented',
        'persuasive',
      ],
      'Steady Supporter': ['patient', 'loyal', 'team-oriented', 'consistent'],
      'Precision Analyst': [
        'careful',
        'systematic',
        'quality-focused',
        'detailed',
      ],
    };

    const scores = this.calculateCategoryScores(answers, personalityDimensions);
    const primary = this.findPrimaryCategory(scores);

    return {
      type: primary,
      score: scores[primary] || 0,
      blend: this.calculatePersonalityBlend(scores),
      all_scores: scores,
    };
  }

  /**
   * Process Experience data into meaningful patterns
   */
  processExperienceProfile(experienceData: any): any {
    // This would analyze experience history for patterns
    const patterns: { [key: string]: string } = {
      'Collaborative Leader': 'Menunjukkan pola memimpin melalui kolaborasi',
      'Independent Achiever': 'Mencapai hasil terbaik dengan kerja mandiri',
      'Adaptive Learner': 'Terus belajar dan beradaptasi dengan tantangan baru',
      'Specialist Expert': 'Fokus mendalam pada area keahlian spesifik',
      'Generalist Bridge': 'Menghubungkan berbagai bidang dan perspektif',
    };

    // Simplified logic - would be more sophisticated in real implementation
    const pattern =
      Object.keys(patterns)[
        Math.floor(Math.random() * Object.keys(patterns).length)
      ];

    return {
      pattern,
      insight: patterns[pattern],
      growth_trajectory: 'Ascending',
      key_experiences: experienceData?.key_experiences || [],
    };
  }

  /**
   * Helper method to calculate category scores
   */
  private calculateCategoryScores(answers: any[], categories: any): any {
    const scores: any = {};

    // Initialize scores
    Object.keys(categories).forEach((category) => {
      scores[category] = 0;
    });

    // Calculate scores based on answers
    answers.forEach((answer) => {
      const answerText = answer.answer_text?.toLowerCase() || '';
      const answerValue = answer.answer_value || 0;

      Object.entries(categories).forEach(([category, keywords]) => {
        const keywordArray = keywords as string[];
        const matchCount = keywordArray.filter((keyword) =>
          answerText.includes(keyword.toLowerCase())
        ).length;

        // Add score based on keyword matches and answer value
        scores[category] += matchCount * 10 + answerValue * 2;
      });
    });

    // Normalize scores to percentage
    const maxScore = Math.max(...Object.values(scores).map((s) => Number(s)));
    if (maxScore > 0) {
      Object.keys(scores).forEach((category) => {
        scores[category] = Math.round((scores[category] / maxScore) * 100);
      });
    }

    return scores;
  }

  /**
   * Find primary category with highest score
   */
  private findPrimaryCategory(scores: any): string {
    return Object.entries(scores).reduce((a: any, b: any) =>
      scores[a] > scores[b] ? a : b
    )[0];
  }

  /**
   * Find secondary category (second highest score)
   */
  private findSecondaryCategory(scores: any, primary: string): string {
    const scoresWithoutPrimary = { ...scores };
    delete scoresWithoutPrimary[primary];

    return Object.entries(scoresWithoutPrimary).reduce((a: any, b: any) =>
      scoresWithoutPrimary[a] > scoresWithoutPrimary[b] ? a : b
    )[0];
  }

  /**
   * Get top N categories by score
   */
  private getTopCategories(scores: any, n: number): string[] {
    return Object.entries(scores)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, n)
      .map(([category]) => category);
  }

  /**
   * Calculate passion intensity
   */
  private calculatePassionIntensity(scores: any): string {
    const maxScore = Math.max(...Object.values(scores).map((s) => Number(s)));
    if (maxScore >= 80) return 'Very High';
    if (maxScore >= 60) return 'High';
    if (maxScore >= 40) return 'Moderate';
    return 'Developing';
  }

  /**
   * Calculate personality blend
   */
  private calculatePersonalityBlend(scores: any): string {
    const topTwo = this.getTopCategories(scores, 2);
    if (topTwo.length >= 2) {
      return `${topTwo[0]} with ${topTwo[1]} tendencies`;
    }
    return topTwo[0] || 'Balanced';
  }

  /**
   * Calculate overall SHAPE coherence score
   */
  calculateShapeCoherence(
    strengthProfile: any,
    heartProfile: any,
    abilitiesProfile: any,
    personalityProfile: any
  ): number {
    // Sophisticated algorithm to measure how well SHAPE components align
    let coherenceScore = 0;

    // Check alignment between strength and personality
    const strengthPersonalityAlignment = this.checkAlignment(
      strengthProfile.primary,
      personalityProfile.type
    );
    coherenceScore += strengthPersonalityAlignment * 25;

    // Check alignment between heart and abilities
    const heartAbilitiesAlignment = this.checkAlignment(
      heartProfile.primary,
      abilitiesProfile.primary
    );
    coherenceScore += heartAbilitiesAlignment * 25;

    // Check overall balance (no single dimension dominates too much)
    const balanceScore = this.calculateBalance([
      strengthProfile.score,
      heartProfile.score,
      abilitiesProfile.score,
      personalityProfile.score,
    ]);
    coherenceScore += balanceScore * 50;

    return Math.round(coherenceScore);
  }

  /**
   * Check alignment between two SHAPE components
   */
  private checkAlignment(component1: string, component2: string): number {
    // Alignment matrix - simplified version
    const alignmentMatrix: any = {
      'Visionary Pioneers': {
        'Visionary Driver': 0.9,
        'Dynamic Connector': 0.7,
        Business: 0.8,
        Technology: 0.9,
      },
      'Inspiring Connectors': {
        'Dynamic Connector': 0.9,
        'Steady Supporter': 0.7,
        'Social Skills': 0.9,
        Education: 0.8,
      },
      // Would include full matrix in real implementation
    };

    return alignmentMatrix[component1]?.[component2] || 0.5; // Default moderate alignment
  }

  /**
   * Calculate balance score
   */
  private calculateBalance(scores: number[]): number {
    const mean = scores.reduce((a, b) => a + b, 0) / scores.length;
    const variance =
      scores.reduce((acc, score) => acc + Math.pow(score - mean, 2), 0) /
      scores.length;
    const standardDeviation = Math.sqrt(variance);

    // Lower standard deviation = better balance
    const balanceScore = Math.max(0, 100 - standardDeviation * 2);
    return balanceScore / 100;
  }
}
