import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { UserStateService } from '../../shared/services/user-state.service';
import { User } from '../../shared/services/user-state.service';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-3xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">Your Profile</h1>

        <div class="bg-white shadow overflow-hidden rounded-lg">
          <div class="px-4 py-5 sm:px-6 flex items-center justify-between">
            <div>
              <h2 class="text-xl font-semibold text-gray-900">
                Account Information
              </h2>
              <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Personal details and preferences
              </p>
            </div>
            <div
              class="h-16 w-16 rounded-full bg-indigo-100 flex items-center justify-center"
            >
              <span class="text-2xl font-medium text-indigo-600">{{
                userInitial
              }}</span>
            </div>
          </div>

          <div class="border-t border-gray-200">
            <form
              [formGroup]="profileForm"
              (ngSubmit)="onSubmit()"
              class="px-4 py-5 sm:p-6"
            >
              <div class="grid grid-cols-1 gap-6">
                <div>
                  <label
                    for="name"
                    class="block text-sm font-medium text-gray-700"
                    >Name</label
                  >
                  <input
                    type="text"
                    id="name"
                    formControlName="name"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700"
                    >Email</label
                  >
                  <input
                    type="email"
                    id="email"
                    formControlName="email"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    readonly
                  />
                  <p class="mt-1 text-xs text-gray-500">
                    Email cannot be changed
                  </p>
                </div>

                <div>
                  <button
                    type="submit"
                    [disabled]="
                      !profileForm.dirty || profileForm.invalid || isSubmitting
                    "
                    class="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-300"
                  >
                    {{ isSubmitting ? 'Saving...' : 'Save Changes' }}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>

        <div class="mt-8 bg-white shadow overflow-hidden rounded-lg">
          <div class="px-4 py-5 sm:px-6">
            <h2 class="text-xl font-semibold text-gray-900">
              Assessment History
            </h2>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
              Your recently completed assessments
            </p>
          </div>
          <div class="border-t border-gray-200">
            <div class="px-4 py-5 sm:p-6">
              @if (assessments.length > 0) {
              <ul class="divide-y divide-gray-200">
                @for (assessment of assessments; track assessment.id) {
                <li class="py-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-sm font-medium text-gray-900">
                        {{ assessment.name }}
                      </p>
                      <p class="text-sm text-gray-500">
                        Completed on {{ assessment.completedDate }}
                      </p>
                    </div>
                    <div class="text-sm font-medium text-indigo-600">
                      Score: {{ assessment.score }}%
                    </div>
                  </div>
                </li>
                }
              </ul>
              } @else {
              <p class="text-sm text-gray-500">
                You haven't completed any assessments yet.
              </p>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [],
})
export class ProfileComponent implements OnInit {
  private formBuilder = inject(FormBuilder);
  private userStateService = inject(UserStateService);

  profileForm: FormGroup;
  currentUser: User | null = null;
  isSubmitting = false;
  userInitial = 'U';

  // Mock assessment history data
  assessments = [
    {
      id: 1,
      name: 'JavaScript Fundamentals',
      completedDate: 'June 20, 2025',
      score: 85,
    },
    {
      id: 2,
      name: 'UX Design Principles',
      completedDate: 'June 18, 2025',
      score: 92,
    },
    {
      id: 3,
      name: 'Project Management Basics',
      completedDate: 'June 15, 2025',
      score: 78,
    },
  ];

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {
    this.profileForm = this.formBuilder.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
    });
  }

  ngOnInit(): void {
    this.userStateService.currentUser.subscribe((user) => {
      this.currentUser = user;
      if (user) {
        this.profileForm.patchValue({
          name: user.name,
          email: user.email,
        });

        if (user.name) {
          this.userInitial = user.name.charAt(0).toUpperCase();
        }
      }
    });
  }

  onSubmit(): void {
    if (this.profileForm.invalid || !this.profileForm.dirty) {
      return;
    }

    this.isSubmitting = true;

    // In a real app, this would call an API to update the profile
    setTimeout(() => {
      // Mock successful update
      const updatedUser = {
        ...this.currentUser,
        name: this.profileForm.value.name,
      } as User;

      // Update local storage
      localStorage.setItem('current_user', JSON.stringify(updatedUser));

      // Update BehaviorSubject in the service
      this.userStateService.updateUser(updatedUser);

      this.profileForm.markAsPristine();
      this.isSubmitting = false;
    }, 1000);
  }
}
