import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { NgClass } from '@angular/common';
import { AssessmentService } from '../../shared/services/assessment.service';
import { AuthService } from '../../shared/services/auth.service';
import {
  UserStateService,
  User,
} from '../../shared/services/user-state.service';
import { ReportProcessorService } from '../../shared/services/report-processor.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterLink, NgClass],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  private router = inject(Router);
  private assessmentService = inject(AssessmentService);
  private userStateService = inject(UserStateService);
  private authService = inject(AuthService);
  private reportProcessor = inject(ReportProcessorService);

  isLoading = true; // Add loading state
  // Define the type for assessments
  assessments: {
    id: number;
    name: string;
    description: string;
    progress: number;
    status: string;
    current_question_index?: number; // Optional property for assessment progress
  }[] = [];

  userProfile: User | null = null;
  selectedCategory: string = 'all';
  filteredAssessments: any[] = [];
  inProgressCount: number = 0;
  completedCount: number = 0;
  completionRate: number = 0;

  // Assessment completion status
  assessmentResults: any = null;
  shapeCompletionStatus: any = null;
  allAreasComplete: boolean = false;

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {}

  ngOnInit(): void {
    console.log('Dashboard component initializing...');

    // Get current user profile
    this.userStateService.currentUser.subscribe((user) => {
      this.userProfile = user;
      console.log('User profile loaded:', this.userProfile);
    });

    // Validate API connection first
    this.validateApiConnection();

    // Fetch real assessment data from API
    this.loadUserAssessments();
  }

  /**
   * Load user assessments from API
   */
  loadUserAssessments(): void {
    this.isLoading = true;
    console.log('Loading user assessments...');

    this.assessmentService.getUserAssessments().subscribe({
      next: (data) => {
        console.log('Received assessments data:', data);
        // Always use data from API
        this.assessments = data || [];

        // Log details about assessments with current_question_index
        const assessmentsWithProgress = this.assessments.filter(
          (a) =>
            a.hasOwnProperty('current_question_index') &&
            a.current_question_index !== null &&
            a.current_question_index !== undefined
        );

        if (assessmentsWithProgress.length > 0) {
          console.log(
            `Found ${assessmentsWithProgress.length} assessments with saved progress:`,
            assessmentsWithProgress.map((a) => ({
              id: a.id,
              name: a.name,
              progress: a.progress,
              index: a.current_question_index,
            }))
          );
        } else {
          console.log('No assessments with saved progress found');
        }

        this.filterByCategory('all');
        this.calculateStats();

        // Check SHAPE assessment completion status
        this.checkShapeCompletionStatus();

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading user assessments:', error);

        // Check if error is auth-related
        if (error.status === 401 || error.status === 403) {
          console.warn('Authentication error while loading assessments');
          // Auth interceptor will handle redirect to login
        }

        // Reset to empty array instead of using dummy data
        this.assessments = [];
        this.filterByCategory('all');
        this.calculateStats();
        this.isLoading = false;
      },
    });
  }

  /**
   * Check if all SHAPE assessments are complete and fetch results
   */
  checkShapeCompletionStatus(): void {
    // First check if we need to fetch results
    this.assessmentService.getAllResults().subscribe({
      next: (data: any) => {
        console.log('Received SHAPE results:', data);
        this.assessmentResults = data;

        if (
          this.assessmentResults &&
          Array.isArray(this.assessmentResults) &&
          this.assessmentResults.length > 0
        ) {
          // Transform the array of results to the expected format
          const transformedResults = this.transformResultsToExpectedFormat(
            this.assessmentResults
          );
          console.log('Transformed results:', transformedResults);

          // Calculate completion status using the transformed data
          this.shapeCompletionStatus =
            this.reportProcessor.getAssessmentCompletionStatus(
              transformedResults
            );
          this.allAreasComplete = this.shapeCompletionStatus.allComplete;

          console.log('SHAPE completion status:', this.shapeCompletionStatus);
        }
      },
      error: (error: any) => {
        console.error('Error loading SHAPE results:', error);
        this.assessmentResults = null;
        this.shapeCompletionStatus = null;
        this.allAreasComplete = false;
      },
    });
  }

  /**
   * Transform array of assessment results into the format expected by ReportProcessorService
   * @param resultsArray Array of assessment results from API
   * @returns Object with properties for each assessment area
   */
  transformResultsToExpectedFormat(resultsArray: any[]): any {
    // Initialize the transformed object
    const transformed: any = {
      categories: [],
    };

    // Map assessment IDs to their type
    const assessmentTypeMap: { [key: number]: string } = {
      1: 'strength',
      2: 'heart',
      3: 'abilities',
      4: 'personality',
      5: 'experience',
    };

    // Process each result in the array
    resultsArray.forEach((result) => {
      // Get assessment type from mapping
      const type = assessmentTypeMap[result.assessment_id];

      // Skip if no mapping exists
      if (!type) {
        console.log(
          `No type mapping for assessment_id: ${result.assessment_id}`
        );
        return;
      }

      // Get processed results
      const processedData = result.processed_results || {};

      // Map assessment types to expected keys
      switch (type) {
        case 'strength':
          transformed.strength = {
            primary:
              processedData.primary ||
              processedData.strength_type ||
              'Balanced Individual',
            score: processedData.score || 0,
            ...processedData,
          };
          break;

        case 'heart':
          transformed.heart = {
            primary:
              processedData.primary ||
              processedData.passion_area ||
              'Personal Development',
            score: processedData.score || 0,
            ...processedData,
          };
          break;

        case 'abilities':
          transformed.abilities = {
            primary:
              processedData.primary ||
              processedData.ability_type ||
              'Cognitive Skills',
            score: processedData.score || 0,
            ...processedData,
          };
          break;

        case 'personality':
          transformed.personality = {
            type:
              processedData.type ||
              processedData.personality_type ||
              'Balanced Individual',
            score: processedData.score || 0,
            ...processedData,
          };
          break;

        case 'experience':
          transformed.experience = {
            pattern:
              processedData.pattern ||
              processedData.experience_pattern ||
              'Adaptive Learner',
            insight: processedData.insight || '',
            ...processedData,
          };
          break;
      }

      // Add categories if they exist in the processed data
      if (Array.isArray(processedData.categories)) {
        processedData.categories.forEach((cat: any) => {
          if (
            cat.name &&
            cat.score &&
            !transformed.categories.some((c: any) => c.name === cat.name)
          ) {
            transformed.categories.push({
              name: cat.name,
              score: cat.score,
            });
          }
        });
      }

      // If this result has a category field, add it to categories
      if (
        processedData.category &&
        !transformed.categories.some(
          (c: any) => c.name === processedData.category
        )
      ) {
        transformed.categories.push({
          name: processedData.category,
          score: processedData.score || 75,
        });
      }
    });

    // Add userName if available
    if (resultsArray[0]?.user?.name) {
      transformed.userName = resultsArray[0].user.name;
    } else if (resultsArray[0]?.user_name) {
      transformed.userName = resultsArray[0].user_name;
    }

    // If no categories were found, create some based on assessment areas
    if (transformed.categories.length === 0) {
      [
        'Communication',
        'Leadership',
        'Problem Solving',
        'Creativity',
        'Collaboration',
      ].forEach((name) => {
        transformed.categories.push({
          name,
          score: 75, // Default score
        });
      });
    }

    return transformed;
  }

  /**
   * Filter assessments by category
   */
  filterByCategory(category: string): void {
    this.selectedCategory = category;
    console.log(`Filtering assessments by category: ${category}`);

    if (category === 'all') {
      this.filteredAssessments = [...this.assessments];
    } else if (category === 'not-started') {
      this.filteredAssessments = this.assessments.filter(
        (a) => a.status === 'Not Started'
      );
    } else if (category === 'in-progress') {
      this.filteredAssessments = this.assessments.filter(
        (a) => a.status === 'In Progress'
      );
    } else if (category === 'completed') {
      this.filteredAssessments = this.assessments.filter(
        (a) => a.status === 'Completed'
      );
    }

    console.log(
      `Found ${this.filteredAssessments.length} assessments in category '${category}'`
    );

    // Log more details for in-progress assessments
    if (category === 'in-progress' && this.filteredAssessments.length > 0) {
      this.filteredAssessments.forEach((assessment) => {
        console.log(
          `Assessment ${assessment.id}: ${assessment.name} - Progress: ${
            assessment.progress
          }%, Question Index: ${assessment.current_question_index || 'unknown'}`
        );
      });
    }
  }

  /**
   * Calculate dashboard statistics
   */
  calculateStats(): void {
    // Count assessments by status
    const notStartedCount = this.assessments.filter(
      (a) => a.status === 'Not Started'
    ).length;

    this.inProgressCount = this.assessments.filter(
      (a) => a.status === 'In Progress'
    ).length;

    this.completedCount = this.assessments.filter(
      (a) => a.status === 'Completed'
    ).length;

    // Calculate completion rate (percentage of total assessments that are completed)
    if (this.assessments.length > 0) {
      this.completionRate = Math.round(
        (this.completedCount / this.assessments.length) * 100
      );
    } else {
      this.completionRate = 0;
    }

    console.log('Dashboard statistics calculated:');
    console.log(`- Total assessments: ${this.assessments.length}`);
    console.log(`- Not started: ${notStartedCount}`);
    console.log(`- In progress: ${this.inProgressCount}`);
    console.log(`- Completed: ${this.completedCount}`);
    console.log(`- Completion rate: ${this.completionRate}%`);
  }

  /**
   * Start a new assessment
   */
  startNewAssessment(): void {
    // Check if we have any not-started assessments
    const notStartedAssessments = this.assessments.filter(
      (a) => a.status === 'Not Started'
    );
    console.log(
      `Navigating to assessment selection with ${notStartedAssessments.length} not-started assessments`
    );

    // Navigate to assessment selection page or start new assessment flow
    this.router.navigate(['/assessments']);
  }

  /**
   * Continue the last assessment that is in progress
   */
  continueLastAssessment(): void {
    const inProgressAssessments = this.assessments.filter(
      (a) => a.status === 'In Progress'
    );
    if (inProgressAssessments.length > 0) {
      // Sort by progress value to find the one with the most progress
      const latestAssessment = [...inProgressAssessments].sort(
        (a, b) => b.progress - a.progress
      )[0];

      console.log('Continuing assessment:', latestAssessment);

      // Navigate directly to the assessment - the backend will handle the current question index
      this.router.navigate(['/assessments', latestAssessment.id]);
    } else {
      console.log('No in-progress assessments found to continue');
      // Consider showing a message to the user or redirecting to assessment selection
      this.startNewAssessment();
    }
  }

  /**
   * Get average completion progress for in-progress assessments
   * Useful for analytics and showing user their overall progress
   */
  getAverageProgress(): number {
    const inProgressAssessments = this.assessments.filter(
      (a) => a.status === 'In Progress'
    );

    if (inProgressAssessments.length === 0) {
      return 0;
    }

    const totalProgress = inProgressAssessments.reduce(
      (sum, assessment) => sum + assessment.progress,
      0
    );
    return Math.round(totalProgress / inProgressAssessments.length);
  }

  /**
   * Get the most recent assessment with progress
   * This is used to determine what to highlight in the UI
   */
  getMostRecentAssessment() {
    // First try to find in-progress assessments
    const inProgressAssessments = this.assessments.filter(
      (a) => a.status === 'In Progress'
    );

    if (inProgressAssessments.length > 0) {
      // Sort by progress to get the one most worked on
      return [...inProgressAssessments].sort(
        (a, b) => b.progress - a.progress
      )[0];
    }

    // If no in-progress, check for completed ones (for reviewing)
    const completedAssessments = this.assessments.filter(
      (a) => a.status === 'Completed'
    );
    if (completedAssessments.length > 0) {
      // Return most recently completed (this would require additional timestamp data)
      return completedAssessments[0];
    }

    // If nothing in progress or completed, suggest a new one
    const notStartedAssessments = this.assessments.filter(
      (a) => a.status === 'Not Started'
    );
    return notStartedAssessments.length > 0 ? notStartedAssessments[0] : null;
  }

  /**
   * Test API connection and token validity
   * This can be used to proactively ensure the user's token is valid
   */
  validateApiConnection(): void {
    if (!this.authService.isLoggedIn()) {
      console.warn('No authentication token available');
      return;
    }

    console.log('Testing API connection and token validity...');

    this.authService.validateToken().subscribe({
      next: () => {
        console.log('API connection successful and token is valid');
      },
      error: (error) => {
        console.error('API connection test failed:', error);

        // If token is invalid, AuthInterceptor will handle the redirect
        if (error.status !== 401 && error.status !== 403) {
          console.warn(
            'API connection issue (not auth-related):',
            error.message
          );
        }
      },
    });
  }
}
