<div class="bg-gradient-to-r from-indigo-700 to-blue-500 text-white">
  <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <div class="flex flex-col md:flex-row justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">
          Welcome back, {{ userProfile?.name || "User" }}
        </h1>
        <p class="mt-1 text-lg">Continue your self-discovery journey</p>
      </div>
      <div class="flex mt-4 md:mt-0">
        <!-- Admin Dashboard Link - Only show if user is admin -->
        <a
          *ngIf="userProfile?.role === 'admin'"
          [routerLink]="['/admin']"
          class="inline-flex items-center mr-4 px-4 py-2 border border-white rounded-md shadow-sm text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"
            />
          </svg>
          Admin Dashboard
        </a>

        <!-- SHAPE Results Link - Only show if all areas are complete or completion rate is 100% -->
        <a
          *ngIf="allAreasComplete || completionRate === 100"
          [routerLink]="['/results']"
          class="inline-flex items-center mr-4 px-4 py-2 border border-white rounded-md shadow-sm text-base font-medium text-white bg-green-700 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          View SHAPE Report
        </a>

        <button
          (click)="startNewAssessment()"
          class="inline-flex items-center px-5 py-2.5 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-900 hover:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            ></path>
          </svg>
          Start New Assessment
        </button>
      </div>
    </div>
  </div>
</div>

<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
  <!-- Stats Cards -->
  <div class="px-4 py-6 sm:px-0">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-5 mb-8">
      <!-- Total Assessments -->
      <div
        class="bg-white overflow-hidden shadow rounded-lg border-b-4 border-indigo-500"
      >
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-indigo-100 rounded-md p-3">
              <svg
                class="h-6 w-6 text-indigo-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                ></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Total Assessments
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    {{ assessments.length }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3">
          <div class="text-sm">
            <a
              [routerLink]="['/assessments']"
              class="font-medium text-indigo-600 hover:text-indigo-500"
            >
              View all assessments <span aria-hidden="true">&rarr;</span>
            </a>
          </div>
        </div>
      </div>

      <!-- Completed -->
      <div
        class="bg-white overflow-hidden shadow rounded-lg border-b-4 border-green-500"
      >
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
              <svg
                class="h-6 w-6 text-green-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Completed Assessments
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    {{ completedCount }}
                  </div>
                  <div
                    class="ml-2 flex items-baseline text-sm font-semibold text-green-600"
                  >
                    <span>{{ completionRate }}%</span>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3">
          <div class="text-sm">
            <span class="font-medium text-gray-900">
              Overall completion rate of your assessments
            </span>
          </div>
        </div>
      </div>

      <!-- In Progress -->
      <div
        class="bg-white overflow-hidden shadow rounded-lg border-b-4 border-yellow-500"
      >
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3">
              <svg
                class="h-6 w-6 text-yellow-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  In Progress
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    {{ inProgressCount }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3">
          <div class="text-sm">
            <button
              *ngIf="inProgressCount > 0"
              (click)="continueLastAssessment()"
              class="font-medium text-yellow-600 hover:text-yellow-500"
            >
              Continue latest assessment ({{ getAverageProgress() }}% avg.
              progress) <span aria-hidden="true">&rarr;</span>
            </button>
            <span
              *ngIf="inProgressCount === 0"
              class="font-medium text-gray-500"
            >
              No assessments in progress
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Category Selection Tabs -->
    <div class="border-b border-gray-200 mb-6">
      <nav class="-mb-px flex space-x-6">
        <button
          (click)="filterByCategory('all')"
          class="py-4 px-1 border-b-2"
          [ngClass]="{
            'border-indigo-500 text-indigo-600 font-medium':
              selectedCategory === 'all',
            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
              selectedCategory !== 'all'
          }"
        >
          All Assessments
        </button>
        <button
          (click)="filterByCategory('not-started')"
          class="py-4 px-1 border-b-2"
          [ngClass]="{
            'border-indigo-500 text-indigo-600 font-medium':
              selectedCategory === 'not-started',
            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
              selectedCategory !== 'not-started'
          }"
        >
          Not Started
        </button>
        <button
          (click)="filterByCategory('in-progress')"
          class="py-4 px-1 border-b-2"
          [ngClass]="{
            'border-indigo-500 text-indigo-600 font-medium':
              selectedCategory === 'in-progress',
            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
              selectedCategory !== 'in-progress'
          }"
        >
          In Progress
        </button>
        <button
          (click)="filterByCategory('completed')"
          class="py-4 px-1 border-b-2"
          [ngClass]="{
            'border-indigo-500 text-indigo-600 font-medium':
              selectedCategory === 'completed',
            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
              selectedCategory !== 'completed'
          }"
        >
          Completed
        </button>
      </nav>
    </div>

    <!-- Loading indicator -->
    @if (isLoading) {
    <div class="flex justify-center items-center py-12">
      <div
        class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"
      ></div>
      <p class="ml-3 text-gray-600">Loading your assessments...</p>
    </div>
    }

    <!-- Empty State when no filteredAssessments -->
    @if (!isLoading && filteredAssessments.length === 0) {
    <div class="flex flex-col items-center justify-center py-12">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-16 w-16 text-gray-400"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
      <h3 class="mt-4 text-lg font-medium text-gray-900">
        No assessments found
      </h3>
      <p class="mt-1 text-sm text-gray-500">
        Start a new assessment to begin your discovery journey.
      </p>
      <button
        (click)="startNewAssessment()"
        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        Start New Assessment
      </button>
    </div>
    }

    <!-- Assessment Cards Grid -->
    @if (!isLoading && filteredAssessments.length > 0) {
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      @for (assessment of filteredAssessments; track assessment.id) {
      <div
        class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow duration-300"
      >
        <div class="px-6 py-5 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">
              {{ assessment.name }}
            </h3>
            <div>
              @if (assessment.status === 'Completed') {
              <span
                class="px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"
              >
                {{ assessment.status }}
              </span>
              } @else if (assessment.status === 'In Progress') {
              <span
                class="px-3 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800"
              >
                {{ assessment.status }}
              </span>
              } @else {
              <span
                class="px-3 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"
              >
                {{ assessment.status }}
              </span>
              }
            </div>
          </div>
        </div>
        <div class="px-6 py-4">
          <p class="text-sm text-gray-600 mb-4">{{ assessment.description }}</p>
          <div class="mb-2">
            <div
              class="flex justify-between text-xs font-medium text-gray-500 mb-1"
            >
              <span>Progress</span>
              <span>{{ assessment.progress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="rounded-full h-2"
                [ngClass]="{
                  'bg-green-500': assessment.status === 'Completed',
                  'bg-yellow-500': assessment.status === 'In Progress',
                  'bg-indigo-500': assessment.status === 'Not Started'
                }"
                [style.width.%]="assessment.progress"
              ></div>
            </div>
          </div>
        </div>
        <div class="px-6 py-3 bg-gray-50 flex justify-end">
          @if (assessment.status === 'Completed') {
          <button
            class="inline-flex items-center px-3 py-1.5 border border-transparent rounded text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Finished
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 ml-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              ></path>
            </svg>
          </button>
          } @else {
          <button
            [routerLink]="['/assessments', assessment.id]"
            class="inline-flex items-center px-3 py-1.5 border border-transparent rounded text-sm font-medium text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            @if (assessment.status === 'In Progress') { Continue } @else { Start
            }
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 ml-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              ></path>
            </svg>
          </button>
          }
        </div>
      </div>
      }
    </div>
    }
  </div>
</div>
