/* Dashboard component specific styles */
.bg-gradient-to-r {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(to right, rgba(255,255,255,0.2), rgba(255,255,255,0.5));
  }
}

/* Animation for cards */
.bg-white {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
}

/* Progress bar animations */
.rounded-full {
  transition: width 1s ease-in-out;
}

/* Tab transitions */
button.border-b-2 {
  transition: all 0.2s ease;
}

/* Custom scroll styling for the dashboard */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #c7d2fe;
  border-radius: 10px;
  
  &:hover {
    background-color: #818cf8;
  }
}
