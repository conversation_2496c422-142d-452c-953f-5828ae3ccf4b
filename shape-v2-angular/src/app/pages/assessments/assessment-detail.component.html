<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
  <div class="px-4 py-6 sm:px-0">
    <div class="pb-5 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <button
            type="button"
            (click)="goBack()"
            class="inline-flex items-center mr-4 text-sm font-medium text-gray-500 hover:text-indigo-600"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back to Assessments
          </button>
        </div>
        <div class="flex items-center">
          <span class="text-sm text-gray-500"
            >{{ currentQuestionIndex + 1 }}/{{ questions.length }}</span
          >
        </div>
      </div>
    </div>

    <div class="mt-6">
      <!-- Progress Bar -->
      <div class="w-full bg-gray-200 rounded-full h-2.5 mb-6">
        <div
          class="bg-indigo-600 h-2.5 rounded-full"
          [style.width.%]="progress"
        ></div>
      </div>
      <div class="flex justify-between text-xs text-gray-500 mb-4">
        <span
          >Pertanyaan terjawab: {{ getAnsweredQuestionsCount() }}/{{
            questions.length
          }}</span
        >
        <span>Progress: {{ progress | number : "1.0-0" }}%</span>
      </div>

      @if (questions && questions.length > 0) {
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <div class="flex items-center">
            @if (currentQuestion.icon) {
            <div class="flex-shrink-0 text-3xl mr-3">
              {{ currentQuestion.icon }}
            </div>
            }
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              {{ currentQuestion.question_text }}
            </h3>
          </div>

          @if (currentQuestion.subprompts && currentQuestion.subprompts.length >
          0) {
          <div class="mt-2 flex flex-wrap gap-2">
            @for (subprompt of currentQuestion.subprompts; track subprompt) {
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800"
            >
              {{ subprompt }}
            </span>
            }
          </div>
          }
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
          <form [formGroup]="answerForm" (ngSubmit)="nextQuestion()">
            <div>
              <!-- Rating Input (Likert scale 1-5) -->
              @if (currentQuestion.question_type === 'rating') {
              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-3">
                  Pilih skala 1-5:
                </label>
                <div class="flex flex-col space-y-6">
                  <div class="flex justify-between items-center px-2">
                    <span class="text-sm text-gray-500"
                      >Sangat Tidak Setuju</span
                    >
                    <span class="text-sm text-gray-500">Sangat Setuju</span>
                  </div>

                  <div class="grid grid-cols-5 gap-2 px-1">
                    @for (value of [1, 2, 3, 4, 5]; track value) {
                    <div class="flex flex-col items-center">
                      <input
                        id="rating-{{ value }}"
                        type="radio"
                        formControlName="answer"
                        [value]="value.toString()"
                        required
                        (click)="answerForm.markAsTouched()"
                        class="h-5 w-5 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                      />
                      <label
                        for="rating-{{ value }}"
                        class="mt-2 block text-sm font-medium text-gray-700"
                      >
                        {{ value }}
                      </label>
                    </div>
                    }
                  </div>

                  <div class="h-1 bg-gray-200 rounded-full">
                    <div
                      class="h-1 bg-indigo-600 rounded-full"
                      [style.width.%]="calculateRatingProgress()"
                    ></div>
                  </div>
                </div>
              </div>
              }

              <!-- Narrative Input (Essay/Text) -->
              @if (currentQuestion.question_type === 'narrative') {
              <div class="mt-1">
                <textarea
                  rows="5"
                  formControlName="answer"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  placeholder="Write your answer here..."
                ></textarea>
              </div>
              } @if (answerForm.get('answer')?.invalid &&
              answerForm.get('answer')?.touched) {
              <p class="mt-2 text-sm text-red-600">
                @if (currentQuestion.question_type === 'rating') { Please select
                a rating from 1-5 before proceeding. } @else { Please provide an
                answer before proceeding. }
              </p>
              }
            </div>
            <div class="mt-6 flex justify-between">
              <button
                type="button"
                (click)="previousQuestion()"
                [disabled]="currentQuestionIndex === 0"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                type="submit"
                [disabled]="answerForm.invalid || isSubmitting"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                <span *ngIf="isSubmitting" class="inline-block mr-2">
                  <span
                    class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"
                  ></span>
                </span>
                {{ isLastQuestion ? "Complete" : "Next" }}
              </button>
            </div>
          </form>
        </div>
      </div>
      } @else {
      <div class="text-center py-10">
        <svg
          class="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">
          No questions found
        </h3>
        <p class="mt-1 text-sm text-gray-500">
          There are no questions available for this assessment.
        </p>
        <div class="mt-6">
          <button
            type="button"
            (click)="goBack()"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Assessments
          </button>
        </div>
      </div>
      }
    </div>
  </div>
</div>
