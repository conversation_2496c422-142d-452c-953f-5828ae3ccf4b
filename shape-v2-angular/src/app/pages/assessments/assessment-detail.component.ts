import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AssessmentService } from '../../shared/services/assessment.service';
import { AuthService } from '../../shared/services/auth.service';

interface Question {
  id: string;
  question_id: number; // Database ID
  question_text: string;
  question_type: string;
  subprompts?: string[];
  icon?: string;
}

@Component({
  selector: 'app-assessment-detail',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './assessment-detail.component.html',
  styleUrls: ['./assessment-detail.component.scss'],
})
export class AssessmentDetailComponent implements OnInit {
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private formBuilder = inject(FormBuilder);
  private assessmentService = inject(AssessmentService);
  private authService = inject(AuthService);

  assessmentId!: number;
  currentQuestionIndex = 0;
  questions: Question[] = [];
  answerForm: FormGroup;
  isSubmitting = false;
  userAnswers: any[] = [];

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {
    // Initialize form with stricter validators
    this.answerForm = this.formBuilder.group({
      answer: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {
    this.assessmentId = Number(this.route.snapshot.paramMap.get('id'));
    this.loadQuestions();
  }

  /**
   * Load all questions and then load progress to set currentQuestionIndex
   */
  loadQuestionsAndProgress(): void {
    this.assessmentService
      .getQuestionsByAssessmentId(this.assessmentId)
      .subscribe({
        next: (questions) => {
          this.questions = questions;
          console.log(
            `Loaded ${questions.length} questions for assessment ${this.assessmentId}`
          );

          if (this.questions.length > 0) {
            // After questions are loaded, get saved progress
            this.loadSavedProgress();
          }
        },
        error: (error) => {
          console.error('Error loading questions:', error);
        },
      });
  }

  /**
   * Load saved progress from the API
   */
  loadSavedProgress(): void {
    this.assessmentService.getAssessmentProgress(this.assessmentId).subscribe({
      next: (progress) => {
        if (progress && typeof progress.current_question_index === 'number') {
          this.currentQuestionIndex = progress.current_question_index;
          console.log(
            'Loaded saved progress, starting at question:',
            this.currentQuestionIndex + 1
          );
        } else {
          console.log('No saved progress found, starting from question 1');
        }

        // Load user answers after progress is restored
        this.loadUserAnswers();
      },
      error: (error) => {
        console.error('Error loading progress:', error);
        // Still load user answers even if progress fails
        this.loadUserAnswers();
      },
    });
  }
  loadQuestions(): void {
    // Simply call the loadQuestionsAndProgress method
    this.loadQuestionsAndProgress();
  }

  /**
   * Load existing user answers if any
   */
  loadUserAnswers(): void {
    // Check if we're authenticated first (to prevent unnecessary API calls)
    if (!this.authService.isLoggedIn()) {
      console.warn('User not logged in, skipping answer loading');
      this.userAnswers = []; // Reset to empty array
      this.resetAnswerFormForCurrentQuestion();
      return;
    }

    console.log(`Loading answers for assessment ID: ${this.assessmentId}`);

    this.assessmentService.getUserAnswers(this.assessmentId).subscribe({
      next: (answers) => {
        this.userAnswers = answers || [];
        console.log(
          `Loaded ${this.userAnswers.length} answer(s) for assessment ${this.assessmentId}`
        );

        // Log the structure for debugging
        if (this.userAnswers.length > 0) {
          console.log('Sample answer structure:', this.userAnswers[0]);

          const questionIds = this.questions.map((q) => q.question_id || q.id);
          const answeredQuestionIds = this.userAnswers.map(
            (a) => a.question_id
          );

          console.log('Question IDs in assessment:', questionIds);
          console.log('Answered question IDs:', answeredQuestionIds);

          // Find which questions have been answered
          const answeredCount = questionIds.filter((qId) => {
            const qIdStr = qId.toString();
            const qIdNum = typeof qId === 'string' ? parseInt(qId) : qId;
            return (
              answeredQuestionIds.includes(qIdNum) ||
              answeredQuestionIds.includes(qIdStr) ||
              answeredQuestionIds.map((id) => id.toString()).includes(qIdStr)
            );
          }).length;

          console.log(
            `${answeredCount} out of ${questionIds.length} questions have been answered`
          );
        }

        // Reset the form with the current question's answer if any
        this.resetAnswerFormForCurrentQuestion();
      },
      error: (error) => {
        console.error('Error loading user answers:', error);

        // Check if error is due to auth
        if (error.status === 401) {
          console.warn(
            'Authentication error while loading answers, user may need to log in'
          );
          this.authService.clearAuth();
        }

        this.userAnswers = []; // Reset to empty array
        this.resetAnswerFormForCurrentQuestion();
      },
    });
  }

  get currentQuestion(): Question {
    return this.questions[this.currentQuestionIndex];
  }

  get progress(): number {
    // Menggunakan helper method untuk mendapatkan jumlah pertanyaan yang sudah dijawab
    const answeredCount = this.getAnsweredQuestionsCount();

    return this.questions.length > 0
      ? (answeredCount / this.questions.length) * 100
      : 0;
  }

  nextQuestion(): void {
    if (!this.currentQuestion) {
      console.error('Cannot proceed: No current question available');
      return;
    }

    if (this.answerForm.invalid) {
      // Mark the form controls as touched to show validation errors
      this.answerForm.markAllAsTouched();
      console.warn('Form is invalid, cannot proceed');
      return;
    }

    // Debug information
    console.log('Form value:', this.answerForm.value);
    console.log('Question type:', this.currentQuestion.question_type);

    // Save answer to server
    this.isSubmitting = true;
    console.log(
      `Saving answer for question ID ${this.currentQuestion.question_id}`
    );

    // Prepare the answer based on question type
    const formattedAnswer = {
      questionId: this.currentQuestion.question_id,
      answerType: this.currentQuestion.question_type,
      value:
        this.currentQuestion.question_type === 'rating'
          ? parseInt(this.answerForm.value.answer || '0')
          : null,
      text:
        this.currentQuestion.question_type === 'narrative'
          ? this.answerForm.value.answer
          : null,
      assessmentId: this.assessmentId,
    };

    console.log('Formatted answer:', formattedAnswer);

    this.assessmentService
      .saveAnswer(formattedAnswer.questionId, formattedAnswer)
      .subscribe({
        next: (response) => {
          console.log('Answer saved successfully:', response);

          // Refresh user answers to ensure we have the latest data
          this.refreshUserAnswers();

          // Move to next question or complete
          if (this.currentQuestionIndex < this.questions.length - 1) {
            this.currentQuestionIndex++;
            console.log(
              `Moving to next question: ${this.currentQuestionIndex + 1}/${
                this.questions.length
              }`
            );

            // Save progress AFTER moving to next question and answer is saved
            this.saveCurrentProgress();

            // Reset form for the new question
            this.resetAnswerFormForCurrentQuestion();
          } else {
            // Complete assessment - call API to process results
            console.log('All questions answered, completing assessment');

            this.assessmentService
              .completeAssessment(this.assessmentId)
              .subscribe({
                next: (response) => {
                  console.log('Assessment completed and processed:', response);
                  this.isSubmitting = false;
                  this.router.navigate(['/results', this.assessmentId]);
                },
                error: (error) => {
                  console.error('Error completing assessment:', error);
                  this.isSubmitting = false;
                  // Still navigate away even if there was an error
                  this.router.navigate(['/dashboard']);
                },
              });
          }

          this.isSubmitting = false;
        },
        error: (error) => {
          console.error('Error saving answer:', error);
          this.isSubmitting = false;

          // Check if the error is auth-related (token expired or user deleted)
          if (error.status === 401 || error.status === 403) {
            console.warn(
              'Authentication error while saving answer, redirecting to login'
            );
            // AuthInterceptor will handle the redirect
          } else {
            // Show some feedback to the user for non-auth errors
            // For now just console error
            console.error('Failed to save answer. Please try again.');
          }
        },
      });
  }

  previousQuestion(): void {
    if (this.currentQuestionIndex > 0) {
      console.log(
        `Moving from question ${this.currentQuestionIndex + 1} to ${
          this.currentQuestionIndex
        }`
      );

      // Save current answer if form is valid before moving
      if (this.answerForm.valid && this.answerForm.value.answer) {
        const formattedAnswer = {
          questionId: this.currentQuestion.question_id,
          answerType: this.currentQuestion.question_type,
          value:
            this.currentQuestion.question_type === 'rating'
              ? parseInt(this.answerForm.value.answer || '0')
              : null,
          text:
            this.currentQuestion.question_type === 'narrative'
              ? this.answerForm.value.answer
              : null,
          assessmentId: this.assessmentId,
        };

        // Save answer silently (don't block navigation)
        this.assessmentService
          .saveAnswer(formattedAnswer.questionId, formattedAnswer)
          .subscribe({
            next: (response) => {
              console.log('Answer saved before navigation:', response);
              // Refresh answers after saving
              this.refreshUserAnswers();
            },
            error: (error) => {
              console.warn('Failed to save answer before navigation:', error);
            },
          });
      }

      this.currentQuestionIndex--;

      // Save progress after moving back
      this.saveCurrentProgress();

      // Reset form for the new question
      this.resetAnswerFormForCurrentQuestion();
    } else {
      console.log('Already at first question, cannot go back');
    }
  }

  get isLastQuestion(): boolean {
    return this.currentQuestionIndex === this.questions.length - 1;
  }

  goBack(): void {
    this.router.navigate(['/assessments']);
  }
  /**
   * Reset the answer form for the current question and load previous answer if exists
   */
  resetAnswerFormForCurrentQuestion(): void {
    // Reset the form
    this.answerForm.reset();

    if (!this.currentQuestion) {
      console.warn('No current question available to reset form');
      return;
    }

    console.log('Resetting form for question:', this.currentQuestion);
    console.log('Available user answers:', this.userAnswers.length);

    // Find existing answer - use more robust matching
    const currentQuestionId =
      this.currentQuestion.question_id || this.currentQuestion.id;

    const existingAnswer = this.userAnswers.find((answer) => {
      // Convert both to strings for comparison to handle type mismatches
      const answerQuestionId = answer.question_id;
      return answerQuestionId.toString() === currentQuestionId.toString();
    });

    if (existingAnswer) {
      console.log('Found existing answer for question:', existingAnswer);

      // Set answer in form based on question type
      if (this.currentQuestion.question_type === 'rating') {
        const ratingValue = existingAnswer.answer_value?.toString() || '';
        console.log('Setting rating value:', ratingValue);
        this.answerForm.patchValue({ answer: ratingValue });
      } else if (this.currentQuestion.question_type === 'narrative') {
        const narrativeValue = existingAnswer.answer_text || '';
        console.log('Setting narrative value:', narrativeValue);
        this.answerForm.patchValue({ answer: narrativeValue });
      }
    } else {
      console.log(
        `No existing answer found for question ID: ${currentQuestionId}`
      );

      // Log available question IDs for debugging
      const availableQuestionIds = this.userAnswers.map((a) => a.question_id);
      console.log('Available answered question IDs:', availableQuestionIds);

      // Set default empty values
      this.answerForm.patchValue({ answer: '' });
    }

    // Mark form as pristine after setting values
    this.answerForm.markAsPristine();
    this.answerForm.markAsUntouched();

    console.log('Form after reset:', this.answerForm.value);
  }

  /**
   * Helper method to parse string to integer for use in template
   */
  parseInt(value: string): number {
    return parseInt(value);
  }

  /**
   * Calculate the width percentage for the rating progress bar
   */
  calculateRatingProgress(): number {
    const value = this.answerForm.get('answer')?.value;
    if (!value) return 0;

    const numValue = parseInt(value);
    if (isNaN(numValue)) return 0;

    return numValue * 20; // 20% per point on a 1-5 scale
  }

  /**
   * Save the current position in the assessment
   */
  saveCurrentProgress(): void {
    console.log(
      `Saving progress: question ${this.currentQuestionIndex + 1}/${
        this.questions.length
      }`
    );

    this.assessmentService
      .saveAssessmentProgress(this.assessmentId, this.currentQuestionIndex)
      .subscribe({
        next: (response) => {
          console.log('Progress saved successfully:', response);
        },
        error: (error) => {
          console.error('Error saving progress:', error);

          // If error is authentication related, AuthInterceptor will handle it
          // For other errors, we'll just log and continue - not critical to UX
          if (!(error.status === 401 || error.status === 403)) {
            console.warn(
              'Non-critical error: Failed to save progress position'
            );
          }
        },
      });
  }

  /**
   * Refresh user answers after saving to ensure data consistency
   */
  refreshUserAnswers(): void {
    console.log('Refreshing user answers after save...');
    this.assessmentService.getUserAnswers(this.assessmentId).subscribe({
      next: (answers) => {
        this.userAnswers = answers || [];
        console.log(
          `Refreshed answers: ${this.userAnswers.length} answers loaded`
        );
      },
      error: (error) => {
        console.error('Error refreshing user answers:', error);
      },
    });
  }

  /**
   * Mendapatkan jumlah pertanyaan yang sudah dijawab (memiliki jawaban)
   */
  getAnsweredQuestionsCount(): number {
    if (
      !this.questions ||
      this.questions.length === 0 ||
      !this.userAnswers ||
      this.userAnswers.length === 0
    ) {
      return 0;
    }

    const questionIds = this.questions.map((q) =>
      (q.question_id || q.id).toString()
    );
    const answeredIds = this.userAnswers.map((a) => a.question_id?.toString());

    // Hitung berapa banyak questionIds yang ada di answeredIds
    return questionIds.filter((qId) => answeredIds.includes(qId)).length;
  }
}
