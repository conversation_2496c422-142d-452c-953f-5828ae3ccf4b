<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
  <div class="px-4 py-6 sm:px-0">
    <div class="pb-5 border-b border-gray-200">
      <h3 class="text-lg leading-6 font-medium text-gray-900">
        Available Assessments
      </h3>
      <p class="mt-2 max-w-4xl text-sm text-gray-500">
        Choose an assessment category to begin your self-evaluation journey.
      </p>
    </div>

    <div *ngIf="isLoading" class="mt-6 flex justify-center">
      <div
        class="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-600"
      ></div>
    </div>

    <div
      *ngIf="!isLoading && assessmentCategories.length === 0"
      class="mt-6 text-center"
    >
      <p class="text-gray-500">No assessment categories found</p>
    </div>

    <div
      *ngIf="!isLoading"
      class="mt-6 grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
    >
      @for (category of assessmentCategories; track category.id) {
      <div
        class="bg-white overflow-hidden shadow rounded-lg divide-y divide-gray-200"
      >
        <div class="px-4 py-5 sm:px-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-3xl">
              {{ category.icon }}
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">
                {{ category.name }}
              </h3>
              <p class="text-sm text-gray-500">
                {{ category.totalQuestions }} questions
              </p>
            </div>
          </div>
        </div>
        <div class="px-4 py-5 sm:p-6">
          <p class="text-sm text-gray-500">
            {{ category.description }}
          </p>
          <div class="mt-4">
            <div class="w-full bg-gray-200 rounded-full h-2.5">
              <div
                class="bg-indigo-600 h-2.5 rounded-full"
                [style.width.%]="category.progress"
              ></div>
            </div>
            <div class="mt-1 flex justify-between">
              <span class="text-xs text-gray-500">
                Progress: {{ category.progress | number : "1.0-0" }}%
              </span>
              <span class="text-xs text-gray-500">
                {{ category.completedQuestions }}/{{ category.totalQuestions }}
                completed
              </span>
            </div>
          </div>
        </div>
        <div class="px-4 py-4 sm:px-6">
          <a
            [routerLink]="['/assessments', category.id]"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Start Assessment
          </a>
        </div>
      </div>
      }
    </div>
  </div>
</div>
