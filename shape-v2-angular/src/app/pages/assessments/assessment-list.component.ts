import { Component, OnInit, inject } from '@angular/core';
import { CommonModule, DecimalPipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { AssessmentService } from '../../shared/services/assessment.service';

@Component({
  selector: 'app-assessment-list',
  standalone: true,
  imports: [CommonModule, RouterLink, DecimalPipe],
  templateUrl: './assessment-list.component.html',
  styleUrls: ['./assessment-list.component.scss'],
})
export class AssessmentListComponent implements OnInit {
  private assessmentService = inject(AssessmentService);

  assessmentCategories: any[] = [];
  isLoading = true;

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {}

  ngOnInit(): void {
    this.loadAssessmentCategories();
  }

  loadAssessmentCategories(): void {
    this.assessmentService.getAssessmentCategories().subscribe({
      next: (categories) => {
        this.assessmentCategories = categories;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading assessment categories:', error);
        this.isLoading = false;
      },
    });
  }
}
