import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 text-center mb-8">
          About Shape
        </h1>

        <div class="bg-white shadow overflow-hidden rounded-lg">
          <div class="px-4 py-5 sm:px-6">
            <h2 class="text-xl font-semibold text-gray-900">Our Mission</h2>
          </div>
          <div class="border-t border-gray-200">
            <div class="px-4 py-5 sm:p-6 text-gray-700">
              <p class="mb-4">
                Shape is dedicated to helping individuals and organizations
                assess and improve their skills through comprehensive,
                personalized assessments.
              </p>
              <p class="mb-4">
                Our platform provides detailed insights into strengths and areas
                for improvement, allowing users to focus their learning and
                development efforts more effectively.
              </p>
              <p>
                Whether you're an individual looking to advance your career or
                an organization seeking to develop your team's capabilities,
                Shape offers the tools and insights you need to succeed.
              </p>
            </div>
          </div>
        </div>

        <div class="mt-8 bg-white shadow overflow-hidden rounded-lg">
          <div class="px-4 py-5 sm:px-6">
            <h2 class="text-xl font-semibold text-gray-900">How It Works</h2>
          </div>
          <div class="border-t border-gray-200">
            <div class="px-4 py-5 sm:p-6 text-gray-700">
              <ol class="list-decimal list-inside space-y-3">
                <li>
                  <span class="font-medium">Browse Assessment Categories:</span>
                  Explore our wide range of assessment categories tailored to
                  different skills and industries.
                </li>
                <li>
                  <span class="font-medium">Take Assessments:</span> Complete
                  comprehensive question sets designed by experts in their
                  respective fields.
                </li>
                <li>
                  <span class="font-medium">Receive Detailed Feedback:</span>
                  Get insights into your performance with detailed breakdowns of
                  your strengths and areas for improvement.
                </li>
                <li>
                  <span class="font-medium">Track Progress:</span> Monitor your
                  growth over time through your personalized dashboard.
                </li>
              </ol>
            </div>
          </div>
        </div>

        <div class="mt-8 text-center">
          <a
            [routerLink]="['/']"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Get Started
          </a>
        </div>
      </div>
    </div>
  `,
  styles: [],
})
export class AboutComponent {}
