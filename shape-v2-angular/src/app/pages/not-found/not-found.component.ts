import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div
      class="min-h-screen flex flex-col items-center justify-center bg-gray-50 text-gray-800 px-4"
    >
      <div class="text-center">
        <h1 class="text-8xl font-bold text-indigo-600">404</h1>
        <h2 class="text-3xl md:text-4xl font-bold mt-4">Page Not Found</h2>
        <p class="mt-4 text-lg">
          Sorry, the page you are looking for doesn't exist or has been moved.
        </p>
        <div class="mt-8">
          <a
            [routerLink]="['/']"
            class="px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 transition duration-200"
          >
            Go back home
          </a>
        </div>
      </div>
    </div>
  `,
  styles: [],
})
export class NotFoundComponent {}
