<div class="min-h-screen bg-gray-100">
  <!-- Admin navigation header -->
  <header class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <!-- <div>
          <button
            [routerLink]="['/dashboard']"
            class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to User Dashboard
          </button>
        </div> -->
      </div>

      <!-- Admin navigation tabs -->
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
          <button
            (click)="switchTab('dashboard')"
            class="py-4 px-1 border-b-2 font-medium text-sm"
            [ngClass]="{
              'border-indigo-500 text-indigo-600': activeTab === 'dashboard',
              'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
                activeTab !== 'dashboard'
            }"
          >
            Dashboard
          </button>
          <button
            (click)="switchTab('users')"
            class="py-4 px-1 border-b-2 font-medium text-sm"
            [ngClass]="{
              'border-indigo-500 text-indigo-600': activeTab === 'users',
              'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
                activeTab !== 'users'
            }"
          >
            User Management
          </button>
        </nav>
      </div>
    </div>
  </header>

  <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Main dashboard view -->
    @if (activeTab === 'dashboard') {
    <div class="px-4 py-6 sm:px-0">
      <!-- Stats summary cards -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Total Users -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                <svg
                  class="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Users
                  </dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900">
                      {{ stats.totalUsers }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <!-- New Users This Week -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                <svg
                  class="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
                  />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    New Users (This Week)
                  </dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900">
                      {{ stats.newUsersThisWeek }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <!-- Completion Rate -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                <svg
                  class="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    Assessment Completion Rate
                  </dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900">
                      {{ stats.completionRate }}%
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <!-- Total Assessments -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0 bg-purple-500 rounded-md p-3">
                <svg
                  class="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Assessments
                  </dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900">
                      {{ stats.totalAssessments }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <!-- New Users This Month -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                <svg
                  class="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    New Users (This Month)
                  </dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900">
                      {{ stats.newUsersThisMonth }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Data Charts -->
      <div class="mt-8">
        <div
          class="bg-white overflow-hidden shadow rounded-lg divide-y divide-gray-200"
        >
          <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              User Registration Trends
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
              Monthly registration data
            </p>
          </div>
          <div class="px-4 py-5 sm:p-6">
            <div
              class="h-96 flex items-center justify-center bg-gray-50 rounded-lg"
            >
              <!-- In a real app, this would be a chart component -->
              <div class="flex w-full h-72">
                @for (value of userRegistrationData.datasets[0].data; track
                $index) {
                <div class="flex-1 flex flex-col justify-end items-center">
                  <div
                    class="bg-indigo-500 w-8"
                    [style.height.%]="value * 3"
                  ></div>
                  <div class="text-xs font-medium text-gray-500 mt-2">
                    {{ userRegistrationData.labels[$index] }}
                  </div>
                </div>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    }

    <!-- User management view -->
    @if (activeTab === 'users') {
    <div class="px-4 py-6 sm:px-0">
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <!-- Search and filter controls -->
        <div
          class="px-4 py-5 border-b border-gray-200 sm:px-6 flex flex-col sm:flex-row justify-between items-center"
        >
          <h3 class="text-lg leading-6 font-medium text-gray-900">Users</h3>
          <div class="mt-4 sm:mt-0 w-full sm:w-64">
            <label for="search" class="sr-only">Search users</label>
            <div class="relative rounded-md shadow-sm">
              <input
                type="text"
                [(ngModel)]="searchQuery"
                (input)="filterUsers()"
                name="search"
                id="search"
                class="focus:ring-indigo-500 focus:border-indigo-500 block w-full pr-10 sm:text-sm border-gray-300 rounded-md"
                placeholder="Search users..."
              />
              <div
                class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
              >
                <svg
                  class="h-5 w-5 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- User table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th
                  (click)="setSorting('name')"
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div class="flex items-center">
                    Name @if (sortField === 'name') {
                    <svg
                      class="ml-1 h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      @if (sortDirection === 'asc') {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 15l7-7 7 7"
                      />
                      } @else {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                      />
                      }
                    </svg>
                    }
                  </div>
                </th>
                <th
                  (click)="setSorting('email')"
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div class="flex items-center">
                    Email @if (sortField === 'email') {
                    <svg
                      class="ml-1 h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      @if (sortDirection === 'asc') {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 15l7-7 7 7"
                      />
                      } @else {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                      />
                      }
                    </svg>
                    }
                  </div>
                </th>
                <th
                  (click)="setSorting('registrationDate')"
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div class="flex items-center">
                    Registration Date @if (sortField === 'registrationDate') {
                    <svg
                      class="ml-1 h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      @if (sortDirection === 'asc') {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 15l7-7 7 7"
                      />
                      } @else {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                      />
                      }
                    </svg>
                    }
                  </div>
                </th>
                <th
                  (click)="setSorting('completedAssessments')"
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div class="flex items-center">
                    Assessments @if (sortField === 'completedAssessments') {
                    <svg
                      class="ml-1 h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      @if (sortDirection === 'asc') {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 15l7-7 7 7"
                      />
                      } @else {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                      />
                      }
                    </svg>
                    }
                  </div>
                </th>
                <th
                  (click)="setSorting('role')"
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div class="flex items-center">
                    Role @if (sortField === 'role') {
                    <svg
                      class="ml-1 h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      @if (sortDirection === 'asc') {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 15l7-7 7 7"
                      />
                      } @else {
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                      />
                      }
                    </svg>
                    }
                  </div>
                </th>
                <th scope="col" class="relative px-6 py-3">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              @for (user of paginatedUsers; track user.id) {
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div
                      class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-gray-200 text-gray-600"
                    >
                      {{ user.name ? user.name.charAt(0).toUpperCase() : "U" }}
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">
                        {{ user.name }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ user.email }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">
                    {{ user.registrationDate | date : "MMM d, y" }}
                  </div>
                  <div class="text-sm text-gray-500">
                    Last active: {{ user.lastActive | date : "MMM d, y" }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">
                    {{ user.completedAssessments }} /
                    {{ user.totalAssessments }}
                  </div>
                  @if (user.totalAssessments && user.totalAssessments > 0) {
                  <div class="w-24 bg-gray-200 rounded-full h-1.5 mt-1">
                    <div
                      class="bg-blue-600 h-1.5 rounded-full"
                      [style.width.%]="
                        user.completedAssessments && user.totalAssessments
                          ? (user.completedAssessments /
                              user.totalAssessments) *
                            100
                          : 0
                      "
                    ></div>
                  </div>
                  }
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    [ngClass]="{
                      'bg-purple-100 text-purple-800': user.role === 'admin',
                      'bg-gray-100 text-gray-800': user.role === 'user'
                    }"
                  >
                    {{ user.role }}
                  </span>
                </td>
                <td
                  class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                >
                  <div class="flex justify-end">
                    <button
                      (click)="viewUserResults(user.id || '')"
                      class="text-indigo-600 hover:text-indigo-900 mr-4"
                    >
                      View Results
                    </button>
                    <button
                      (click)="
                        updateUserRole(
                          user.id || '',
                          user.role === 'admin' ? 'user' : 'admin'
                        )
                      "
                      class="text-gray-600 hover:text-gray-900 mr-4"
                    >
                      {{
                        user.role === "admin" ? "Remove Admin" : "Make Admin"
                      }}
                    </button>
                    <button
                      (click)="resetUserProgress(user.id || '')"
                      class="text-red-600 hover:text-red-900"
                    >
                      Reset Data
                    </button>
                  </div>
                </td>
              </tr>
              }
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        @if (totalPages > 1) {
        <div
          class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
        >
          <div class="flex-1 flex justify-between items-center">
            <button
              (click)="currentPage = currentPage > 1 ? currentPage - 1 : 1"
              [disabled]="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              [ngClass]="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
            >
              Previous
            </button>
            <p class="text-sm text-gray-700">
              Showing page <span class="font-medium">{{ currentPage }}</span> of
              <span class="font-medium">{{ totalPages }}</span>
            </p>
            <button
              (click)="
                currentPage =
                  currentPage < totalPages ? currentPage + 1 : totalPages
              "
              [disabled]="currentPage === totalPages"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              [ngClass]="{
                'opacity-50 cursor-not-allowed': currentPage === totalPages
              }"
            >
              Next
            </button>
          </div>
        </div>
        }
      </div>
    </div>
    }

    <!-- User detail view -->
    @if (activeTab === 'userDetail' && selectedUser) {
    <div class="px-4 py-6 sm:px-0">
      <!-- User header with back button -->
      <div
        class="pb-5 border-b border-gray-200 sm:flex sm:items-center sm:justify-between"
      >
        <div class="flex items-center">
          <button
            (click)="backToUserList()"
            class="mr-4 inline-flex items-center p-1.5 border border-gray-300 rounded-full text-gray-600 hover:text-gray-900 hover:border-gray-400"
          >
            <svg
              class="h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            {{ selectedUser.name }}'s Assessment Results
          </h3>
        </div>
        <div class="mt-3 flex sm:mt-0">
          <span
            class="px-3 py-1 text-xs leading-5 font-semibold rounded-full"
            [ngClass]="{
              'bg-purple-100 text-purple-800': selectedUser.role === 'admin',
              'bg-gray-100 text-gray-800': selectedUser.role === 'user'
            }"
          >
            {{ selectedUser.role }}
          </span>
        </div>
      </div>

      <!-- User info and stats -->
      <div class="bg-white shadow overflow-hidden sm:rounded-lg mt-6">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            User Information
          </h3>
        </div>
        <div class="border-t border-gray-200">
          <dl>
            <div
              class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
            >
              <dt class="text-sm font-medium text-gray-500">Full name</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {{ selectedUser.name }}
              </dd>
            </div>
            <div
              class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
            >
              <dt class="text-sm font-medium text-gray-500">Email address</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {{ selectedUser.email }}
              </dd>
            </div>
            <div
              class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
            >
              <dt class="text-sm font-medium text-gray-500">
                Registration date
              </dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {{ selectedUser.registrationDate | date : "medium" }}
              </dd>
            </div>
            <div
              class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
            >
              <dt class="text-sm font-medium text-gray-500">Last active</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {{ selectedUser.lastActive | date : "medium" }}
              </dd>
            </div>
            <div
              class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
            >
              <dt class="text-sm font-medium text-gray-500">
                Assessment completion
              </dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {{ selectedUser.completedAssessments }} completed out of
                {{ selectedUser.totalAssessments }} total assessments
                <span
                  class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{
                    selectedUser.totalAssessments &&
                    selectedUser.completedAssessments
                      ? (
                          (selectedUser.completedAssessments /
                            selectedUser.totalAssessments) *
                          100
                        ).toFixed(0)
                      : 0
                  }}% completion rate
                </span>
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- User's Assessment Results -->
      <div class="mt-8">
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
          <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Assessment Results
            </h3>
            <button
              (click)="resetUserProgress(selectedUser.id || '')"
              class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Reset All Assessment Data
            </button>
          </div>
          <div class="border-t border-gray-200">
            @if (userAssessments.length === 0) {
            <div class="px-4 py-8 text-center">
              <svg
                class="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">
                No assessments
              </h3>
              <p class="mt-1 text-sm text-gray-500">
                This user has not taken any assessments yet.
              </p>
            </div>
            } @else {
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Assessment
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Started
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Completed
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Score
                    </th>
                    <th scope="col" class="relative px-6 py-3">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  @for (assessment of userAssessments; track assessment.id) {
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">
                        {{ assessment.name }}
                      </div>
                      <div class="text-sm text-gray-500">
                        Progress: {{ assessment.progress }}%
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">
                        {{ assessment.startedAt | date : "short" }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      @if (assessment.completedAt) {
                      <div class="text-sm text-gray-900">
                        {{ assessment.completedAt | date : "short" }}
                      </div>
                      } @else {
                      <div class="text-sm text-gray-500">Not completed</div>
                      }
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                        [ngClass]="{
                          'bg-green-100 text-green-800':
                            assessment.status === 'Completed',
                          'bg-yellow-100 text-yellow-800':
                            assessment.status === 'In Progress',
                          'bg-gray-100 text-gray-800':
                            assessment.status === 'Not Started'
                        }"
                      >
                        {{ assessment.status }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      @if (assessment.score !== null) {
                      <div class="text-sm font-medium text-gray-900">
                        {{ assessment.score }}/100
                      </div>
                      } @else {
                      <div class="text-sm text-gray-500">N/A</div>
                      }
                    </td>
                    <td
                      class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                    >
                      <button
                        (click)="
                          resetUserProgress(
                            selectedUser.id || '',
                            assessment.id
                          )
                        "
                        class="text-red-600 hover:text-red-900"
                      >
                        Reset
                      </button>
                    </td>
                  </tr>
                  <!-- Assessment Details (answers) -->
                  @if (assessment.answers && assessment.answers.length > 0) {
                  <tr>
                    <td colspan="6" class="px-6 py-4 bg-gray-50">
                      <div class="text-sm font-medium text-gray-900 mb-2">
                        Assessment Answers
                      </div>
                      <div class="border rounded-md overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                          <thead class="bg-gray-100">
                            <tr>
                              <th
                                scope="col"
                                class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                Question ID
                              </th>
                              <th
                                scope="col"
                                class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                Answer
                              </th>
                              <th
                                scope="col"
                                class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                Score
                              </th>
                            </tr>
                          </thead>
                          <tbody class="bg-white divide-y divide-gray-200">
                            @for (answer of assessment.answers; track
                            answer.questionId) {
                            <tr>
                              <td
                                class="px-4 py-2 whitespace-nowrap text-xs text-gray-900"
                              >
                                Q{{ answer.questionId }}
                              </td>
                              <td class="px-4 py-2 text-xs text-gray-900">
                                {{ answer.answer }}
                              </td>
                              <td class="px-4 py-2 whitespace-nowrap text-xs">
                                <span
                                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                  [ngClass]="{
                                    'bg-green-100 text-green-800':
                                      answer.score >= 7,
                                    'bg-yellow-100 text-yellow-800':
                                      answer.score >= 4 && answer.score < 7,
                                    'bg-red-100 text-red-800': answer.score < 4
                                  }"
                                >
                                  {{ answer.score }}/10
                                </span>
                              </td>
                            </tr>
                            }
                          </tbody>
                        </table>
                      </div>
                    </td>
                  </tr>
                  } }
                </tbody>
              </table>
            </div>
            }
          </div>
        </div>
      </div>
    </div>
    }
  </main>
</div>
