/* Admin dashboard specific styles */
.min-h-screen {
  min-height: 100vh;
}

/* Style for chart bars animation */
.bg-indigo-500 {
  transition: height 0.5s ease-in-out;
}

/* Add subtle hover effects to cards */
.shadow {
  transition: all 0.2s ease-in-out;
}

.shadow:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Custom styles for table sorting */
th.cursor-pointer:hover {
  background-color: #f3f4f6;
}

/* Custom scrollbar for tables */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  border-radius: 10px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background-color: #c7d2fe;
  border-radius: 10px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background-color: #818cf8;
}

/* Status indicator pulse animation */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

.status-active {
  animation: pulse 2s infinite;
}
