import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NgClass, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  UserStateService,
  User,
} from '../../shared/services/user-state.service';
import {
  AdminService,
  UserWithAssessments,
  AdminStats,
} from '../../shared/services/admin.service';

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [CommonModule, NgClass, FormsModule, DatePipe],
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.scss'],
})
export class AdminDashboardComponent implements OnInit {
  private router = inject(Router);
  private userStateService = inject(UserStateService);
  private adminService = inject(AdminService);

  stats: AdminStats = {
    totalUsers: 0,
    newUsersThisWeek: 0,
    newUsersThisMonth: 0,
    totalAssessments: 0,
    completionRate: 0,
  };

  users: UserWithAssessments[] = [];
  filteredUsers: UserWithAssessments[] = [];
  selectedUser: UserWithAssessments | null = null;
  userAssessments: any[] = [];
  activeTab: string = 'dashboard';
  searchQuery: string = '';
  currentPage: number = 1;
  itemsPerPage: number = 10;
  sortField: string = 'registrationDate';
  sortDirection: string = 'desc';

  // Chart data
  userRegistrationData = {
    labels: [] as string[],
    datasets: [
      {
        data: [] as number[],
        backgroundColor: '#4f46e5',
      },
    ],
  };

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {}

  ngOnInit(): void {
    this.loadAdminStats();
    this.loadUsers();
    this.generateChartData();
  }

  /**
   * Load admin statistics
   */
  loadAdminStats(): void {
    // In a real app, this would be an API call
    this.adminService.getAdminStats().subscribe((stats: AdminStats) => {
      this.stats = stats;
    });
  }

  /**
   * Load all users
   */
  loadUsers(): void {
    this.adminService.getUsers().subscribe((users: UserWithAssessments[]) => {
      this.users = users;
      this.filterUsers();
    });
  }

  /**
   * Generate chart data for visualizations
   */
  generateChartData(): void {
    // In a real app, this would be generated from actual data
    this.adminService.getUserRegistrationChartData().subscribe((data: any) => {
      this.userRegistrationData = data;
    });
  }

  /**
   * Filter users based on search query
   */
  filterUsers(): void {
    if (!this.searchQuery) {
      this.filteredUsers = [...this.users];
    } else {
      const query = this.searchQuery.toLowerCase();
      this.filteredUsers = this.users.filter(
        (user) =>
          user.name?.toLowerCase().includes(query) ||
          user.email?.toLowerCase().includes(query)
      );
    }
    this.sortUsers();
  }

  /**
   * Sort users based on field and direction
   */
  sortUsers(): void {
    this.filteredUsers.sort((a: any, b: any) => {
      let fieldA = a[this.sortField];
      let fieldB = b[this.sortField];

      if (typeof fieldA === 'string') {
        fieldA = fieldA.toLowerCase();
        fieldB = fieldB.toLowerCase();
      }

      if (fieldA < fieldB) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (fieldA > fieldB) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  /**
   * Change sort field and direction
   */
  setSorting(field: string): void {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'asc';
    }
    this.sortUsers();
  }

  /**
   * Get pagination for current view
   */
  get paginatedUsers(): UserWithAssessments[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return this.filteredUsers.slice(startIndex, startIndex + this.itemsPerPage);
  }

  /**
   * Get total pages for pagination
   */
  get totalPages(): number {
    return Math.ceil(this.filteredUsers.length / this.itemsPerPage);
  }

  /**
   * View user's assessment results
   */
  viewUserResults(userId: string): void {
    this.selectedUser = this.users.find((u) => u.id === userId) || null;
    if (this.selectedUser) {
      this.adminService
        .getUserAssessments(userId)
        .subscribe((assessments: any[]) => {
          this.userAssessments = assessments;
          this.activeTab = 'userDetail';
        });
    }
  }

  /**
   * Reset user's assessment progress
   */
  resetUserProgress(userId: string, assessmentId?: number): void {
    if (
      confirm(
        "Are you sure you want to reset this user's progress? This action cannot be undone."
      )
    ) {
      this.adminService.resetUserProgress(userId, assessmentId).subscribe({
        next: () => {
          alert('User progress has been reset successfully.');
          // Refresh user assessment data
          if (this.selectedUser) {
            this.adminService
              .getUserAssessments(userId)
              .subscribe((assessments: any[]) => {
                this.userAssessments = assessments;
              });
          }
        },
        error: (err: any) => {
          console.error('Error resetting user progress:', err);
          alert('Failed to reset user progress. Please try again.');
        },
      });
    }
  }

  /**
   * Update user role (admin/user)
   */
  updateUserRole(userId: string, role: string): void {
    this.adminService.updateUserRole(userId, role).subscribe({
      next: () => {
        // Update local user data
        const userIndex = this.users.findIndex((u) => u.id === userId);
        if (userIndex !== -1) {
          this.users[userIndex].role = role;
          this.filterUsers();
        }
        alert('User role updated successfully.');
      },
      error: (err: any) => {
        console.error('Error updating user role:', err);
        alert('Failed to update user role. Please try again.');
      },
    });
  }

  /**
   * Back to user list from user detail view
   */
  backToUserList(): void {
    this.activeTab = 'users';
    this.selectedUser = null;
  }

  /**
   * Switch between dashboard tabs
   */
  switchTab(tab: string): void {
    this.activeTab = tab;
    if (tab === 'users') {
      this.loadUsers();
    }
  }
}
