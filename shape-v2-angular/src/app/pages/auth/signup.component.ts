import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { UserStateService } from '../../shared/services/user-state.service';

@Component({
  selector: 'app-signup',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink],
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.scss'],
})
export class SignupComponent {
  private formBuilder = inject(FormBuilder);
  private router = inject(Router);
  private userStateService = inject(UserStateService);

  signupForm: FormGroup;
  isSubmitting = false;
  errorMessage = '';

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {
    this.signupForm = this.formBuilder.group(
      {
        name: ['', [Validators.required]],
        email: ['', [Validators.required, Validators.email]],
        password: ['', [Validators.required, Validators.minLength(6)]],
        password_confirmation: ['', [Validators.required]],
      },
      {
        validators: this.matchPasswords,
      }
    );
  }

  // Custom validator to check if passwords match
  matchPasswords(group: FormGroup) {
    const password = group.get('password')?.value;
    const confirmPassword = group.get('password_confirmation')?.value;

    return password === confirmPassword ? null : { passwordsNotMatch: true };
  }

  onSubmit() {
    if (this.signupForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const { name, email, password, password_confirmation } =
      this.signupForm.value;

    this.userStateService
      .signup(name, email, password, password_confirmation)
      .subscribe({
        next: (response: any) => {
          this.router.navigate(['/dashboard']);
        },
        error: (error: any) => {
          this.errorMessage = 'Signup failed. Please try again.';
          this.isSubmitting = false;
        },
        complete: () => {
          this.isSubmitting = false;
        },
      });
  }
}
