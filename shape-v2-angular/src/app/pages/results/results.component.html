<!-- Loading State -->
<div
  *ngIf="isLoading"
  class="min-h-screen bg-gray-50 flex items-center justify-center"
>
  <div class="text-center">
    <div
      class="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"
    ></div>
    <p class="mt-4 text-lg text-gray-600">Loading your SHAPE report...</p>
  </div>
</div>

<!-- Error State -->
<div
  *ngIf="hasError && !isLoading"
  class="min-h-screen bg-gray-50 flex items-center justify-center"
>
  <div class="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
    <div class="text-center">
      <svg
        class="mx-auto h-12 w-12 text-red-400"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
        />
      </svg>
      <h3 class="mt-2 text-lg font-medium text-gray-900">
        Assessment Incomplete
      </h3>
      <p class="mt-1 text-sm text-gray-500">{{ errorMessage }}</p>

      <!-- Show incomplete areas if available -->
      <div
        *ngIf="completionStatus && !completionStatus.allComplete"
        class="mt-4"
      >
        <p class="text-sm text-gray-600 mb-2">
          Please complete these assessments:
        </p>
        <div class="space-y-2">
          <button
            *ngFor="let area of getIncompleteAreas()"
            (click)="navigateToAssessment(getAssessmentIdByArea(area))"
            class="block w-full px-4 py-2 text-sm text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition duration-150"
          >
            Complete {{ area | titlecase }} Assessment
          </button>
        </div>
      </div>

      <div class="mt-6">
        <button
          (click)="goToDashboard()"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Dashboard
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main Results Display -->
<div
  *ngIf="!isLoading && !hasError && processedReport"
  class="min-h-screen bg-gray-50"
>
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Your SHAPE Report</h1>
          <p class="mt-1 text-sm text-gray-500">
            Comprehensive analysis of your unique profile
          </p>
        </div>
        <div class="flex space-x-3">
          <button
            (click)="generatePdfReport()"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <svg
              class="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Download PDF
          </button>
          <button
            (click)="goToDashboard()"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <button
          *ngFor="
            let tab of [
              'overview',
              'strength',
              'heart',
              'abilities',
              'personality',
              'experience'
            ]
          "
          (click)="switchTab(tab)"
          [class]="
            activeTab === tab
              ? 'border-indigo-500 text-indigo-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
          "
        >
          {{ tab | titlecase }}
        </button>
      </nav>
    </div>
  </div>

  <!-- Content Area -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Overview Tab -->
    <div *ngIf="activeTab === 'overview'" class="space-y-8">
      <!-- SHAPE Profile Overview Chart -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Your SHAPE Profile Overview
          </h3>
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Chart -->
            <div>
              <app-shape-overview-chart
                [data]="getShapeOverviewData()"
                title="SHAPE Profile"
              >
              </app-shape-overview-chart>
            </div>
            <!-- Summary -->
            <div class="space-y-4">
              <div
                class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4"
              >
                <h4 class="font-semibold text-gray-900 mb-2">
                  Your Profile Summary
                </h4>
                <div class="space-y-2 text-sm">
                  <div><strong>S:</strong> {{ getPrimaryStrength() }}</div>
                  <div><strong>H:</strong> {{ getPrimaryHeart() }}</div>
                  <div><strong>A:</strong> {{ getPrimaryAbilities() }}</div>
                  <div><strong>P:</strong> {{ getPrimaryPersonality() }}</div>
                  <div><strong>E:</strong> {{ getPrimaryExperience() }}</div>
                </div>
              </div>
              <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 class="font-semibold text-green-800 mb-2">
                  Your Genius Zone
                </h4>
                <p class="text-green-800 text-sm">{{ getGeniusZone() }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Integrative Profile -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Your Integrative Profile
          </h3>
          <div
            class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-6"
          >
            <blockquote class="text-lg italic text-gray-700 mb-4">
              "{{ getIntegrativeProfile() }}"
            </blockquote>
            <p class="text-sm text-gray-600">{{ getWisdomQuote() }}</p>
          </div>
        </div>
      </div>

      <!-- Development Recommendations -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Development Recommendations
          </h3>
          <ul class="space-y-2">
            <li
              *ngFor="let recommendation of getDevelopmentRecommendations()"
              class="flex items-start"
            >
              <svg
                class="flex-shrink-0 h-5 w-5 text-green-400 mt-0.5"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clip-rule="evenodd"
                />
              </svg>
              <span class="ml-2 text-sm text-gray-700">{{
                recommendation
              }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Strength Tab -->
    <div *ngIf="activeTab === 'strength'" class="space-y-6">
      <!-- TEMPORARY MINIMAL CONTENT TO DEBUG HANG ISSUE -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-xl font-bold text-gray-900 mb-2">
            Visionary Pioneers
          </h3>
          <p class="text-gray-600 mb-4">
            Pola pikir futuristik & berorientasi pada kemungkinan baru
          </p>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 class="font-semibold text-red-800 mb-2">Superpower Anda</h4>
            <p class="text-red-900 italic">
              "You naturally see possibilities where others see dead ends."
            </p>
          </div>
        </div>
      </div>

      <!-- TEMPORARY: DISABLE ALL DETAILED CONTENT TO DEBUG HANG -->
      <div class="bg-white shadow rounded-lg p-6">
        <h4 class="font-semibold text-gray-900 mb-3">Debug Mode</h4>
        <p class="text-gray-600">
          Strength tab content temporarily disabled for debugging.
        </p>
      </div>
    </div>

    <!-- Heart Tab -->
    <div *ngIf="activeTab === 'heart'" class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          ❤️ Analisis Passion (Heart)
        </h3>

        <div class="space-y-6">
          <!-- Primary Passion -->
          <div class="bg-pink-50 border border-pink-200 rounded-lg p-4">
            <h4 class="font-semibold text-pink-800 mb-2">Passion Utama</h4>
            <p class="text-lg font-medium text-pink-900">
              {{ getPrimaryHeart() }}
            </p>
            <p class="text-sm text-pink-700 mt-1">
              {{ getHeartDescription() }}
            </p>
          </div>

          <!-- Hidden Strengths -->
          <div class="bg-rose-50 border border-rose-200 rounded-lg p-4">
            <h4 class="font-semibold text-rose-800 mb-2">
              Kekuatan Tersembunyi
            </h4>
            <p class="text-rose-900">{{ getHeartHiddenStrength() }}</p>
          </div>

          <!-- Core Values -->
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 class="font-semibold text-red-800 mb-2">Nilai-Nilai Inti</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
              <span
                *ngFor="let value of getHeartValues()"
                class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm"
              >
                {{ value }}
              </span>
            </div>
          </div>

          <!-- Love Language -->
          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <h4 class="font-semibold text-purple-800 mb-2">Bahasa Cinta</h4>
            <ul class="list-disc list-inside text-purple-900 space-y-1">
              <li *ngFor="let language of getHeartLoveLanguage()">
                {{ language }}
              </li>
            </ul>
          </div>

          <!-- Creative Ideas -->
          <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
            <h4 class="font-semibold text-indigo-800 mb-2">Ide Kreatif</h4>
            <ul class="list-disc list-inside text-indigo-900 space-y-1">
              <li *ngFor="let idea of getHeartCreativeIdeas()">{{ idea }}</li>
            </ul>
          </div>

          <!-- Warning -->
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-semibold text-yellow-800 mb-2">⚠️ Peringatan</h4>
            <p class="text-yellow-900">{{ getHeartWarning() }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Abilities Tab -->
    <div *ngIf="activeTab === 'abilities'" class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          🧠 Analisis Kemampuan (Abilities)
        </h3>

        <div class="space-y-6">
          <!-- Top Abilities -->
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-semibold text-yellow-800 mb-2">
              Kemampuan Teratas
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                *ngFor="let ability of getTopAbilities()"
                class="bg-yellow-100 p-3 rounded"
              >
                <p class="font-medium text-yellow-900">{{ ability }}</p>
                <p class="text-sm text-yellow-700">Kemampuan unggulan</p>
              </div>
            </div>
          </div>

          <!-- Ability Breakdown -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              *ngFor="let category of getAbilityCategories()"
              class="bg-gray-50 border border-gray-200 rounded-lg p-4"
            >
              <h5 class="font-semibold text-gray-800 mb-2">
                {{ category.name }}
              </h5>
              <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div
                  class="bg-blue-600 h-2 rounded-full"
                  [style.width.%]="category.score"
                ></div>
              </div>
              <p class="text-sm text-gray-600">{{ category.score }}%</p>
              <p class="text-xs text-gray-500 mt-1">
                {{ getAbilityDescription(category.name) }}
              </p>
            </div>
          </div>

          <!-- Hidden Strengths -->
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 class="font-semibold text-green-800 mb-2">
              Kekuatan Tersembunyi
            </h4>
            <p class="text-green-900">{{ getAbilityHiddenStrength() }}</p>
          </div>

          <!-- Creative Ideas -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 class="font-semibold text-blue-800 mb-2">
              Ide Kreatif untuk Pengembangan
            </h4>
            <ul class="list-disc list-inside text-blue-900 space-y-1">
              <li *ngFor="let idea of getAbilityCreativeIdeas()">{{ idea }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Personality Tab -->
    <div *ngIf="activeTab === 'personality'" class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          👤 Analisis Kepribadian (Personality)
        </h3>

        <div class="space-y-6">
          <!-- Primary Type -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 class="font-semibold text-blue-800 mb-2">
              Tipe Kepribadian Utama
            </h4>
            <p class="text-lg font-medium text-blue-900">
              {{ getPersonalityType() }}
            </p>
            <p class="text-sm text-blue-700 mt-1">
              {{ getPersonalityDescription() }}
            </p>
          </div>

          <!-- Communication Pattern -->
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 class="font-semibold text-green-800 mb-2">Pola Komunikasi</h4>
            <div class="space-y-2">
              <p>
                <strong>Gaya:</strong> {{ getPersonalityCommunicationStyle() }}
              </p>
              <p>
                <strong>Preferensi:</strong>
                {{ getPersonalityCommunicationPreference() }}
              </p>
            </div>
          </div>

          <!-- Team Strengths -->
          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <h4 class="font-semibold text-purple-800 mb-2">
              Kekuatan dalam Tim
            </h4>
            <ul class="list-disc list-inside text-purple-900 space-y-1">
              <li *ngFor="let strength of getPersonalityTeamStrengths()">
                {{ strength }}
              </li>
            </ul>
          </div>

          <!-- Development Areas -->
          <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h4 class="font-semibold text-orange-800 mb-2">
              Area Pengembangan
            </h4>
            <ul class="list-disc list-inside text-orange-900 space-y-1">
              <li *ngFor="let area of getPersonalityDevelopmentAreas()">
                {{ area }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Experience Tab -->
    <div *ngIf="activeTab === 'experience'" class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          📈 Analisis Pengalaman (Experience)
        </h3>

        <div class="space-y-6">
          <!-- Experience Pattern -->
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 class="font-semibold text-green-800 mb-2">Pola Pengalaman</h4>
            <p class="text-lg font-medium text-green-900">
              {{ getExperiencePattern() }}
            </p>
            <p class="text-sm text-green-700 mt-1">
              {{ getExperienceDescription() }}
            </p>
          </div>

          <!-- Key Experiences -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 class="font-semibold text-blue-800 mb-2">Pengalaman Kunci</h4>
            <div class="space-y-2">
              <div
                *ngFor="let experience of getKeyExperiences()"
                class="bg-blue-100 p-3 rounded"
              >
                <p class="font-medium text-blue-900">{{ experience.area }}</p>
                <p class="text-sm text-blue-700">
                  {{ experience.description }}
                </p>
              </div>
            </div>
          </div>

          <!-- Learning Pattern -->
          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <h4 class="font-semibold text-purple-800 mb-2">
              Pola Pembelajaran
            </h4>
            <p class="text-purple-900">{{ getLearningPattern() }}</p>
          </div>

          <!-- Skill Evolution -->
          <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
            <h4 class="font-semibold text-indigo-800 mb-2">
              Evolusi Kemampuan
            </h4>
            <ul class="list-disc list-inside text-indigo-900 space-y-1">
              <li *ngFor="let evolution of getSkillEvolution()">
                {{ evolution }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
