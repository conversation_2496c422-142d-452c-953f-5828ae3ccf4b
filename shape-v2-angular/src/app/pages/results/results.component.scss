// Results Component Styles

.results-container {
  min-height: 100vh;
  background-color: #f9fafb;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Tab navigation styles
.tab-nav {
  border-bottom: 1px solid #e5e7eb;
  
  .tab-button {
    padding: 0.5rem 1rem;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    font-size: 0.875rem;
    white-space: nowrap;
    transition: all 0.15s ease-in-out;
    
    &.active {
      border-bottom-color: #4f46e5;
      color: #4f46e5;
    }
    
    &:not(.active) {
      color: #6b7280;
      
      &:hover {
        color: #374151;
        border-bottom-color: #d1d5db;
      }
    }
  }
}

// Card styles
.result-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
  
  .card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    
    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #111827;
      margin: 0;
    }
  }
  
  .card-content {
    padding: 1.5rem;
  }
}

// Profile metaphor styles
.profile-metaphor {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0.5rem;
  padding: 2rem;
  color: white;
  text-align: center;
  
  .metaphor-text {
    font-size: 1.25rem;
    font-style: italic;
    margin-bottom: 1rem;
    line-height: 1.6;
  }
  
  .wisdom-quote {
    font-size: 0.875rem;
    opacity: 0.9;
  }
}

// Summary grid styles
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.25rem;
  margin: 2rem 0;
}

.summary-card {
  background: white;
  border-radius: 0.5rem;
  padding: 1.25rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  
  .icon-container {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.75rem;
    
    &.strength {
      background-color: #fef2f2;
      color: #dc2626;
    }
    
    &.heart {
      background-color: #fdf2f8;
      color: #ec4899;
    }
    
    &.abilities {
      background-color: #fffbeb;
      color: #d97706;
    }
    
    &.personality {
      background-color: #f0f9ff;
      color: #0284c7;
    }
    
    &.experience {
      background-color: #f0fdf4;
      color: #16a34a;
    }
  }
  
  .card-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 0.25rem;
  }
  
  .card-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
  }
}

// Genius zone styles
.genius-zone {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 0.5rem;
  padding: 1.5rem;
  color: white;
  text-align: center;
  
  .zone-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
  }
  
  .zone-description {
    font-size: 1rem;
    line-height: 1.6;
    opacity: 0.95;
  }
}

// Recommendations styles
.recommendations-list {
  list-style: none;
  padding: 0;
  margin: 0;
  
  li {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .check-icon {
      flex-shrink: 0;
      width: 1.25rem;
      height: 1.25rem;
      color: #10b981;
      margin-right: 0.75rem;
      margin-top: 0.125rem;
    }
    
    .recommendation-text {
      font-size: 0.875rem;
      color: #374151;
      line-height: 1.5;
    }
  }
}

// Error state styles
.error-container {
  text-align: center;
  padding: 3rem 1.5rem;
  
  .error-icon {
    width: 3rem;
    height: 3rem;
    color: #ef4444;
    margin: 0 auto 1rem;
  }
  
  .error-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
  }
  
  .error-message {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1.5rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-nav {
    overflow-x: auto;
    white-space: nowrap;
    
    .tab-button {
      display: inline-block;
    }
  }
  
  .profile-metaphor,
  .genius-zone {
    padding: 1.25rem;
    
    .metaphor-text,
    .zone-title {
      font-size: 1.125rem;
    }
  }
}

// Print styles
@media print {
  .results-container {
    background: white;
  }
  
  .tab-nav,
  .action-buttons {
    display: none;
  }
  
  .result-card {
    box-shadow: none;
    border: 1px solid #e5e7eb;
    margin-bottom: 1rem;
    page-break-inside: avoid;
  }
}
