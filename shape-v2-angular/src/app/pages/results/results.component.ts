import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AssessmentService } from '../../shared/services/assessment.service';
import { ReportProcessorService } from '../../shared/services/report-processor.service';
import { ShapePdfReportService } from '../../shared/services/shape-pdf-report.service';
import { AuthService } from '../../shared/services/auth.service';
import {
  GuidanceService,
  GuidanceContent,
} from '../../shared/services/guidance.service';
import {
  ShapeOverviewChartComponent,
  ShapeOverviewData,
} from '../../shared/components/shape-overview-chart/shape-overview-chart.component';
import {
  AreaDetailChartComponent,
  AreaDetailData,
} from '../../shared/components/area-detail-chart/area-detail-chart.component';

interface ShapeResults {
  strength?: any;
  heart?: any;
  abilities?: any;
  personality?: any;
  experience?: any;
  categories?: any[];
}

interface ProcessedReport {
  overall_report?: any;
  strength_analysis?: any;
  heart_analysis?: any;
  abilities_analysis?: any;
  personality_analysis?: any;
  experience_analysis?: any;
}

@Component({
  selector: 'app-results',
  standalone: true,
  imports: [
    CommonModule,
    ShapeOverviewChartComponent,
    AreaDetailChartComponent,
  ],
  templateUrl: './results.component.html',
  styleUrls: ['./results.component.scss'],
})
export class ResultsComponent implements OnInit {
  private router = inject(Router);
  private assessmentService = inject(AssessmentService);
  private reportProcessor = inject(ReportProcessorService);
  private pdfReportService = inject(ShapePdfReportService);
  private authService = inject(AuthService);
  private guidanceService = inject(GuidanceService);

  isLoading = true;
  hasError = false;
  errorMessage = '';

  shapeResults: ShapeResults | null = null;
  processedReport: ProcessedReport | null = null;
  completionStatus: any = null;
  guidanceContent: { [key: string]: GuidanceContent | null } = {};

  // UI state
  activeTab = 'overview';
  showPdfPreview = false;

  ngOnInit(): void {
    this.loadShapeResults();
  }

  /**
   * Load all SHAPE assessment results
   */
  loadShapeResults(): void {
    this.isLoading = true;
    this.hasError = false;

    this.assessmentService.getAllResults().subscribe({
      next: (rawResults: any) => {
        console.log('SHAPE Results loaded:', rawResults);

        // Transform the raw results to the expected format (same as Dashboard)
        const transformedResults = this.transformRawResults(rawResults);
        this.shapeResults = transformedResults;

        // Check completion status
        this.completionStatus =
          this.reportProcessor.getAssessmentCompletionStatus(
            transformedResults
          );

        if (!this.completionStatus.allComplete) {
          this.hasError = true;
          const missingAreas = this.getIncompleteAreas();
          console.log('Missing assessment areas:', missingAreas);
          console.log('Transformed data:', transformedResults);

          this.errorMessage = `Please complete all SHAPE assessments before viewing your report.
            Missing: ${missingAreas.join(', ')}
            Categories complete: ${this.completionStatus.categoriesComplete}`;
          this.isLoading = false;
          return;
        }

        // Process the results for comprehensive report
        this.processedReport =
          this.reportProcessor.processShapeReport(transformedResults);
        console.log('Processed Report:', this.processedReport);

        // Load guidance content
        this.loadGuidanceContent();

        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading SHAPE results:', error);
        this.hasError = true;
        this.errorMessage =
          'Failed to load your assessment results. Please try again.';
        this.isLoading = false;
      },
    });
  }

  /**
   * Transform raw assessment results array into structured object
   * This matches the transformation logic used in the Dashboard component
   */
  private transformRawResults(rawResults: any[]): any {
    if (!Array.isArray(rawResults) || rawResults.length === 0) {
      return {
        categories: [],
        strength: null,
        heart: null,
        abilities: null,
        personality: null,
        experience: null,
      };
    }

    const transformed: any = {
      categories: [],
    };

    // Process each assessment result
    rawResults.forEach((result: any) => {
      if (!result || !result.assessment) return;

      const assessmentType = result.assessment.type.toLowerCase();
      console.log(`Processing assessment type: ${assessmentType}`, result);

      // Map assessment types to our expected structure
      switch (assessmentType) {
        case 'strength':
          const strengthData = result.processed_results || {};
          transformed.strength = {
            primary: strengthData.top_strengths?.[0] || 'Unknown',
            secondary: strengthData.top_strengths?.[1] || '',
            score: this.calculateAverageScore(result.category_scores),
            answers: result.raw_scores || [],
            completed_at: result.completed_at,
            details: strengthData,
            category_scores: result.category_scores,
          };
          break;

        case 'heart':
          const heartData = result.processed_results || {};
          transformed.heart = {
            primary:
              heartData.primary_passion || heartData.top_passion || 'Unknown',
            secondary: heartData.secondary_passion || '',
            score: this.calculateAverageScore(result.category_scores),
            answers: result.raw_scores || [],
            completed_at: result.completed_at,
            details: heartData,
            category_scores: result.category_scores,
          };
          break;

        case 'abilities':
          const abilitiesData = result.processed_results || {};
          transformed.abilities = {
            primary: abilitiesData.top_abilities?.[0] || 'Unknown',
            secondary: abilitiesData.complementary_abilities?.[0] || '',
            score: this.calculateAverageScore(result.category_scores),
            answers: result.raw_scores || [],
            completed_at: result.completed_at,
            details: abilitiesData,
            category_scores: result.category_scores,
          };

          // Extract categories from abilities assessment for the main categories array
          if (result.category_scores) {
            transformed.categories = Object.entries(result.category_scores).map(
              ([name, score]) => ({
                name,
                score: score as number,
              })
            );
          }
          break;

        case 'personality':
        case 'personalities':
          const personalityData = result.processed_results || {};
          transformed.personality = {
            type: personalityData.primary_type || 'Unknown',
            secondary: personalityData.secondary_type || '',
            score: this.calculateAverageScore(result.category_scores),
            answers: result.raw_scores || [],
            completed_at: result.completed_at,
            details: personalityData,
            category_scores: result.category_scores,
          };
          break;

        case 'experience':
          const experienceData = result.processed_results || {};
          transformed.experience = {
            pattern:
              experienceData.experience_pattern ||
              experienceData.primary_pattern ||
              'Unknown',
            level: experienceData.experience_level || '',
            score: this.calculateAverageScore(result.category_scores),
            answers: result.raw_scores || [],
            completed_at: result.completed_at,
            details: experienceData,
            summary: experienceData.summary || {},
            category_scores: result.category_scores,
          };
          break;

        default:
          console.warn(`Unknown assessment type: ${assessmentType}`);
      }
    });

    return transformed;
  }

  /**
   * Calculate average score from category scores object
   */
  private calculateAverageScore(categoryScores: any): number {
    if (!categoryScores || typeof categoryScores !== 'object') {
      return 0;
    }

    const scores = Object.values(categoryScores) as number[];
    if (scores.length === 0) return 0;

    const sum = scores.reduce((acc, score) => acc + (score || 0), 0);
    return Math.round(sum / scores.length);
  }

  /**
   * Navigate to specific assessment if incomplete
   */
  navigateToAssessment(assessmentId: number): void {
    this.router.navigate(['/assessments', assessmentId]);
  }

  /**
   * Navigate back to dashboard
   */
  goToDashboard(): void {
    this.router.navigate(['/dashboard']);
  }

  /**
   * Switch between different report tabs
   */
  switchTab(tab: string): void {
    this.activeTab = tab;
  }

  /**
   * Generate and download PDF report
   */
  generatePdfReport(): void {
    if (!this.shapeResults) {
      return;
    }

    try {
      // Generate comprehensive HTML report
      const htmlReport = this.pdfReportService.generateComprehensiveHtmlReport(
        this.shapeResults
      );

      // Open in new window for printing/saving as PDF
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(htmlReport);
        newWindow.document.close();

        // Focus on the new window and trigger print dialog
        newWindow.focus();
        setTimeout(() => {
          newWindow.print();
        }, 500);
      } else {
        // Fallback: create downloadable HTML file
        const blob = new Blob([htmlReport], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `SHAPE-Report-${
          new Date().toISOString().split('T')[0]
        }.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF report. Please try again.');
    }
  }

  /**
   * Get completion percentage for display
   */
  getCompletionPercentage(): number {
    return this.completionStatus?.completionPercentage || 0;
  }

  /**
   * Get primary strength name
   */
  getPrimaryStrength(): string {
    return (
      this.processedReport?.strength_analysis?.primary_strength?.name ||
      'Not Available'
    );
  }

  /**
   * Get primary heart passion
   */
  getPrimaryHeart(): string {
    return (
      this.processedReport?.heart_analysis?.primary_passion?.name ||
      'Not Available'
    );
  }

  /**
   * Get top abilities
   */
  getTopAbilities(): string[] {
    return (
      this.processedReport?.abilities_analysis?.overview?.dominant_abilities ||
      []
    );
  }

  /**
   * Get personality type
   */
  getPersonalityType(): string {
    return (
      this.processedReport?.personality_analysis?.personality_type?.type ||
      'Not Available'
    );
  }

  /**
   * Get genius zone description
   */
  getGeniusZone(): string {
    return (
      this.processedReport?.overall_report?.zona_genius?.description ||
      'Specialized Professional'
    );
  }

  /**
   * Get integrative profile
   */
  getIntegrativeProfile(): string {
    return (
      this.processedReport?.overall_report?.profil_integratif?.metaphor ||
      'Unique Individual'
    );
  }

  /**
   * Get wisdom quote
   */
  getWisdomQuote(): string {
    return (
      this.processedReport?.overall_report?.wisdom_quote ||
      'Embrace your unique SHAPE.'
    );
  }

  /**
   * Get development recommendations
   */
  getDevelopmentRecommendations(): string[] {
    const recommendations =
      this.processedReport?.overall_report?.cetak_biru_pengembangan
        ?.recommendations || [];
    return Array.isArray(recommendations) ? recommendations : [];
  }

  /**
   * Get assessment areas that need completion
   */
  getIncompleteAreas(): string[] {
    if (!this.completionStatus) return [];

    const areas = [
      'strength',
      'heart',
      'abilities',
      'personality',
      'experience',
    ];
    return areas.filter((area) => !this.completionStatus[area]);
  }

  /**
   * Get assessment ID by area name
   */
  getAssessmentIdByArea(area: string): number {
    const areaToIdMap: { [key: string]: number } = {
      strength: 1,
      heart: 2,
      abilities: 3,
      personality: 4,
      experience: 5,
    };
    return areaToIdMap[area] || 1;
  }

  // ===== STRENGTH ANALYSIS METHODS =====

  /**
   * Get strength description from processed report
   */
  getStrengthDescription(): string {
    const strengthReport = this.processedReport?.strength_analysis;
    return (
      strengthReport?.primary_strength?.description ||
      this.getEnhancedStrengthDescription(this.getPrimaryStrength())
    );
  }

  /**
   * Get strength superpower
   */
  getStrengthSuperpower(): string {
    const strengthReport = this.processedReport?.strength_analysis;
    return (
      strengthReport?.primary_strength?.superpower ||
      this.getEnhancedStrengthSuperpower(this.getPrimaryStrength())
    );
  }

  /**
   * Get strength traits
   */
  getStrengthTraits(): string[] {
    const strengthReport = this.processedReport?.strength_analysis;
    return (
      strengthReport?.primary_strength?.core_traits ||
      this.getEnhancedStrengthTraits(this.getPrimaryStrength())
    );
  }

  /**
   * Get strength talents
   */
  getStrengthTalents(): string[] {
    const strengthReport = this.processedReport?.strength_analysis;
    return (
      strengthReport?.primary_strength?.natural_talents ||
      this.getEnhancedStrengthTalents(this.getPrimaryStrength())
    );
  }

  /**
   * Get strength power partners
   */
  getStrengthPowerPartners(): string[] {
    const strengthReport = this.processedReport?.strength_analysis;
    return (
      strengthReport?.power_partners ||
      this.getEnhancedStrengthPowerPartners(this.getPrimaryStrength())
    );
  }

  /**
   * Get strength warning
   */
  getStrengthWarning(): string {
    const strengthReport = this.processedReport?.strength_analysis;
    return (
      strengthReport?.growth_opportunities?.watch_out_for ||
      this.getEnhancedStrengthWarning(this.getPrimaryStrength())
    );
  }

  /**
   * Get strength development area
   */
  getStrengthDevelopment(): string {
    const strengthReport = this.processedReport?.strength_analysis;
    return (
      strengthReport?.growth_opportunities?.develop ||
      this.getEnhancedStrengthDevelopment(this.getPrimaryStrength())
    );
  }

  /**
   * Get strength action
   */
  getStrengthAction(): string {
    const strengthReport = this.processedReport?.strength_analysis;
    return (
      strengthReport?.growth_opportunities?.action ||
      this.getEnhancedStrengthAction(this.getPrimaryStrength())
    );
  }

  // ===== ENHANCED STRENGTH GUIDANCE METHODS =====

  private getEnhancedStrengthDescription(strength: string): string {
    const descriptions: { [key: string]: string } = {
      'Visionary Pioneers':
        'Kemampuan melihat peluang baru dan memulai terobosan',
      'Insightful Truth-Seekers':
        'Kemampuan menemukan inti kebenaran dan prinsip fundamental',
      'Inspiring Connectors':
        'Kemampuan menghubungkan orang dengan ide dan peluang',
      'Supportive Nurturers': 'Kemampuan mendukung pertumbuhan orang lain',
      'Clarifying Mentors':
        'Kemampuan menjelaskan konsep kompleks secara sistematis',
    };
    return descriptions[strength] || 'Kombinasi unik dari berbagai kekuatan';
  }

  private getEnhancedStrengthSuperpower(strength: string): string {
    const superpowers: { [key: string]: string } = {
      'Visionary Pioneers':
        'You naturally see possibilities where others see dead ends',
      'Insightful Truth-Seekers':
        'You see through surface issues to find the root truth',
      'Inspiring Connectors':
        'You build bridges between people and possibilities',
      'Supportive Nurturers':
        'You create safe spaces where people can grow and thrive',
      'Clarifying Mentors':
        'You transform complexity into clarity and understanding',
    };
    return (
      superpowers[strength] ||
      'You bring a unique blend of capabilities to every situation'
    );
  }

  private getEnhancedStrengthTraits(strength: string): string[] {
    const traits: { [key: string]: string[] } = {
      'Visionary Pioneers': [
        'Pola pikir futuristik',
        'Berani mengambil risiko terukur',
        'Tidak nyaman dengan rutinitas',
      ],
      'Insightful Truth-Seekers': [
        'Kepekaan tinggi pada ketidaksesuaian',
        'Dorongan kuat untuk integritas',
        'Analisis akar masalah yang tajam',
      ],
      'Inspiring Connectors': [
        'Kemampuan membaca kebutuhan audiens',
        'Energi tinggi dalam interaksi sosial',
        'Ahli menemukan kesamaan dari perbedaan',
      ],
      'Supportive Nurturers': [
        'Radar tinggi untuk kebutuhan emosional',
        'Kesabaran dalam proses perkembangan',
        'Fokus pada kesejahteraan jangka panjang',
      ],
      'Clarifying Mentors': [
        'Bakat menyusun pengetahuan sistematis',
        'Kesabaran dalam repetisi pengajaran',
        'Fokus pada keterpahaman bukan kesepakatan',
      ],
    };
    return traits[strength] || ['Adaptable', 'Balanced', 'Versatile'];
  }

  private getEnhancedStrengthTalents(strength: string): string[] {
    const talents: { [key: string]: string[] } = {
      'Visionary Pioneers': [
        'Membuka pasar/jalur baru',
        'Mengenali peluang sebelum orang lain',
        'Memobilisasi tim menuju perubahan',
      ],
      'Insightful Truth-Seekers': [
        'Mencegah tim dari keputusan gegabah',
        'Membongkar bias tersembunyi',
        'Penjaga standar etika/kualitas',
      ],
      'Inspiring Connectors': [
        'Membentuk aliansi strategis',
        'Meningkatkan engagement stakeholder',
        'Menerjemahkan ide kompleks ke bahasa awam',
      ],
      'Supportive Nurturers': [
        'Menciptakan lingkungan psikologis aman',
        'Mempertahankan talenta kunci',
        'Mendeteksi masalah dini melalui observasi',
      ],
      'Clarifying Mentors': [
        'Mempercepat onboarding',
        'Mencegah kesalahan berulang',
        'Menstandarisasi keunggulan',
      ],
    };
    return (
      talents[strength] || [
        'Multi-faceted problem solving',
        'Cross-functional collaboration',
      ]
    );
  }

  private getEnhancedStrengthPowerPartners(strength: string): string[] {
    const partners: { [key: string]: string[] } = {
      'Visionary Pioneers': [
        'Detail-Oriented Implementers',
        'Risk Assessment Specialists',
        'Process Managers',
      ],
      'Insightful Truth-Seekers': [
        'Creative Problem Solvers',
        'Implementation Experts',
        'Communication Specialists',
      ],
      'Inspiring Connectors': [
        'Technical Specialists',
        'Data Analysts',
        'Quality Controllers',
      ],
      'Supportive Nurturers': [
        'Strategic Planners',
        'Performance Managers',
        'Innovation Leaders',
      ],
      'Clarifying Mentors': [
        'Creative Innovators',
        'Relationship Builders',
        'Change Agents',
      ],
    };
    return (
      partners[strength] || [
        'Complementary Skill Sets',
        'Different Perspectives',
        'Diverse Backgrounds',
      ]
    );
  }

  private getEnhancedStrengthWarning(strength: string): string {
    const warnings: { [key: string]: string } = {
      'Visionary Pioneers': 'Mengabaikan detail implementasi',
      'Insightful Truth-Seekers': 'Dianggap terlalu kritis/negatif',
      'Inspiring Connectors': 'Over-promise untuk menyenangkan orang',
      'Supportive Nurturers': 'Kesulitan membuat keputusan tegas',
      'Clarifying Mentors': 'Kaku pada metode yang sudah bekerja',
    };
    return warnings[strength] || 'Overextending capabilities';
  }

  private getEnhancedStrengthDevelopment(strength: string): string {
    const development: { [key: string]: string } = {
      'Visionary Pioneers':
        'Partnership dengan eksekutor detail dan risk assessment',
      'Insightful Truth-Seekers':
        'Teknik feedforward dan creative problem-solving',
      'Inspiring Connectors': 'Conflict resolution dan boundary setting',
      'Supportive Nurturers': 'Tough love skills dan self-care routines',
      'Clarifying Mentors':
        'Microlearning principles dan innovation methodologies',
    };
    return development[strength] || 'Continuous learning and adaptation';
  }

  private getEnhancedStrengthAction(strength: string): string {
    const actions: { [key: string]: string } = {
      'Visionary Pioneers': 'Gunakan framework MVP dan stage-gate evaluation',
      'Insightful Truth-Seekers': 'Sampaikan kritik dengan solusi alternatif',
      'Inspiring Connectors': 'Kombinasikan cerita dengan data pendukung',
      'Supportive Nurturers': 'Gunakan metrics untuk mengukur perkembangan',
      'Clarifying Mentors': 'Kolaborasi dengan Connectors untuk engagement',
    };
    return actions[strength] || 'Seek feedback and iterate regularly';
  }

  // ===== HEART ANALYSIS METHODS =====

  /**
   * Get heart description
   */
  getHeartDescription(): string {
    const heartReport = this.processedReport?.heart_analysis;
    return (
      heartReport?.primary_passion?.description ||
      this.getEnhancedHeartDescription(this.getPrimaryHeart())
    );
  }

  /**
   * Get heart hidden strength
   */
  getHeartHiddenStrength(): string {
    const heartReport = this.processedReport?.heart_analysis;
    return (
      heartReport?.primary_passion?.hidden_strengths ||
      this.getEnhancedHeartHiddenStrength(this.getPrimaryHeart())
    );
  }

  /**
   * Get heart values
   */
  getHeartValues(): string[] {
    const heartReport = this.processedReport?.heart_analysis;
    return (
      heartReport?.primary_passion?.core_values ||
      this.getEnhancedHeartValues(this.getPrimaryHeart())
    );
  }

  /**
   * Get heart love language
   */
  getHeartLoveLanguage(): string[] {
    const heartReport = this.processedReport?.heart_analysis;
    return (
      heartReport?.primary_passion?.love_language ||
      this.getEnhancedHeartLoveLanguage(this.getPrimaryHeart())
    );
  }

  /**
   * Get heart creative ideas
   */
  getHeartCreativeIdeas(): string[] {
    const heartReport = this.processedReport?.heart_analysis;
    return (
      heartReport?.creative_ideas ||
      this.getEnhancedHeartCreativeIdeas(this.getPrimaryHeart())
    );
  }

  /**
   * Get heart warning
   */
  getHeartWarning(): string {
    const heartReport = this.processedReport?.heart_analysis;
    return (
      heartReport?.warning_signs ||
      this.getEnhancedHeartWarning(this.getPrimaryHeart())
    );
  }

  // ===== ENHANCED HEART GUIDANCE METHODS =====

  private getEnhancedHeartDescription(heart: string): string {
    const descriptions: { [key: string]: string } = {
      'Art & Entertainment': 'Memandang dunia sebagai palet emosi yang hidup',
      Business: 'Melihat masalah sebagai model bisnis yang belum lahir',
      'Communication / Media':
        'Memandang keheningan sebagai ruang kosong untuk diisi makna',
      Family: 'Melihat rumah sebagai museum hidup yang terus berevolusi',
      Religion: 'Memandang yang sakral dalam yang sehari-hari',
      Education: 'Memandang ketidaktahuan sebagai lahan subur',
      Government: 'Memandang kebijakan sebagai arsitektur sosial',
    };
    return descriptions[heart] || 'Passion yang unik dan personal';
  }

  private getEnhancedHeartHiddenStrength(heart: string): string {
    const strengths: { [key: string]: string } = {
      'Art & Entertainment':
        'Terapi kejut estetika - menggunakan keindahan tak terduga untuk memutus spiral pikiran negatif',
      Business:
        'Virus kemakmuran - sistem yang menyebarkan kesejahteraan melalui interaksi alami',
      'Communication / Media':
        'Menyulam realitas - menyambung fragmen informasi menjadi narasi kohesif',
      Family:
        'Regenerasi akar - memulihkan hubungan yang rusak melalui ingatan kolektif',
      Religion:
        'Penglihatan lapis - memahami realitas fisik dan metafisik secara simultan',
      Education:
        'Penyambung titik tak terlihat - menemukan pola dalam pengetahuan yang tampak acak',
      Government:
        'Mendengar detak jantung kolektif - merasakan denyut nadi masyarakat',
    };
    return (
      strengths[heart] || 'Kemampuan unik untuk menciptakan dampak positif'
    );
  }

  private getEnhancedHeartValues(heart: string): string[] {
    const values: { [key: string]: string[] } = {
      'Art & Entertainment': [
        'Keindahan',
        'Ekspresi kreatif',
        'Transformasi emosional',
      ],
      Business: ['Efisiensi', 'Inovasi', 'Dampak terukur'],
      'Communication / Media': ['Transparansi', 'Koneksi', 'Pengaruh positif'],
      Family: ['Kebersamaan', 'Warisan', 'Pertumbuhan bersama'],
      Religion: ['Spiritualitas', 'Makna hidup', 'Transendensi'],
      Education: ['Pengetahuan', 'Pertumbuhan', 'Pemberdayaan'],
      Government: ['Keadilan', 'Pelayanan', 'Perubahan sistemik'],
    };
    return values[heart] || ['Autentisitas', 'Pertumbuhan', 'Kontribusi'];
  }

  private getEnhancedHeartLoveLanguage(heart: string): string[] {
    const loveLanguages: { [key: string]: string[] } = {
      'Art & Entertainment': [
        'Waktu tanpa batas untuk kreativitas',
        'Apresiasi tanpa syarat',
        'Kebebasan bereksperimen',
      ],
      Business: [
        'Bukti nyata dampak',
        'Percobaan terkendali',
        'Jaringan tak terduga',
      ],
      'Communication / Media': [
        'Perhatian tanpa batas',
        'Medium eksperimental',
        'Panggung tak terduga',
      ],
      Family: [
        'Waktu berkualitas tanpa syarat',
        'Warisan hidup',
        'Ruangan aman',
      ],
      Religion: ['Keheningan aktif', 'Simbol hidup', 'Pertanyaan suci'],
      Education: [
        'Pertanyaan bagus',
        'Laboratorium hidup',
        'Pembelajaran resiprokal',
      ],
      Government: [
        'Akses transparan',
        'Percobaan mikro',
        'Dialog tanpa topeng',
      ],
    };
    return (
      loveLanguages[heart] || ['Pengakuan', 'Dukungan', 'Kebebasan berekspresi']
    );
  }

  private getEnhancedHeartCreativeIdeas(heart: string): string[] {
    const ideas: { [key: string]: string[] } = {
      'Art & Entertainment': [
        'Proyek Seni Urban Guerilla',
        'Pertunjukan Realitas Campuran',
        'Kuliner Emosional',
      ],
      Business: ['Perpustakaan Alat', 'Pasar Mimpi', 'Bisnis Regeneratif'],
      'Communication / Media': [
        'Surat Masa Depan',
        'Podcast dalam Gelap',
        'Media Tanpa Kata',
      ],
      Family: [
        'Pertukaran Keluarga',
        'Kapsul Waktu Emosional',
        'Ritual Kesalahan',
      ],
      Religion: [
        'Wisata Spiritual Lintas Iman',
        'Teknologi Kontemplatif',
        'Kelas Keraguan Sistematis',
      ],
      Education: [
        'Sekolah Keterbalikan',
        'Pembelajaran Sensori Tunggal',
        'Museum Kegagalan',
      ],
      Government: [
        'Demokrasi Sensori',
        'Pemerintahan Bayangan Anak',
        'Kebijakan Fiksi Ilmiah',
      ],
    };
    return (
      ideas[heart] || [
        'Proyek Inovatif',
        'Eksperimen Kreatif',
        'Kolaborasi Lintas Bidang',
      ]
    );
  }

  private getEnhancedHeartWarning(heart: string): string {
    const warnings: { [key: string]: string } = {
      'Art & Entertainment':
        'Keracunan Keindahan - kehilangan kemampuan melihat nilai dalam yang biasa-biasa saja',
      Business:
        'Logika Kalkulator - mengukur segala sesuatu termasuk yang tak seharusnya terukur',
      'Communication / Media':
        'Banjir Sinyal - ketidakmampuan memproses esensi di tengah kebisingan',
      Family:
        'Mumi Cinta - mengawetkan hubungan dalam bentuk kaku tanpa pertumbuhan',
      Religion:
        'Kandang Keyakinan - terpenjara dalam dogma tanpa ruang bernafas',
      Education:
        'Kutukan Kepastian - kehilangan keajaiban dalam proses mencari jawaban',
      Government:
        'Mesin Birokrasi - menjadi bagian dari sistem yang ingin diubah',
    };
    return warnings[heart] || 'Risiko kehilangan keseimbangan dalam passion';
  }

  // ===== ABILITIES ANALYSIS METHODS =====

  /**
   * Get ability categories with scores
   */
  getAbilityCategories(): any[] {
    if (!this.shapeResults?.abilities?.category_scores) {
      return [];
    }

    return Object.entries(this.shapeResults.abilities.category_scores).map(
      ([name, score]) => ({
        name,
        score: score as number,
      })
    );
  }

  /**
   * Get ability description
   */
  getAbilityDescription(abilityName: string): string {
    const descriptions: { [key: string]: string } = {
      'Cognitive Skills': 'Kemampuan berpikir analitis dan pemecahan masalah',
      'Social Skills': 'Kemampuan berinteraksi dan bekerja dengan orang lain',
      'Technical Skills': 'Kemampuan menggunakan teknologi dan tools',
      'Creative Skills': 'Kemampuan berpikir kreatif dan inovatif',
      'Leadership Skills': 'Kemampuan memimpin dan menginspirasi orang lain',
      'Communication Skills': 'Kemampuan berkomunikasi secara efektif',
      'Management Skills': 'Kemampuan mengelola proyek dan sumber daya',
      'Physical Skills': 'Kemampuan fisik dan koordinasi',
    };
    return descriptions[abilityName] || 'Kemampuan khusus dalam bidang ini';
  }

  /**
   * Get ability hidden strength
   */
  getAbilityHiddenStrength(): string {
    const topAbilities = this.getTopAbilities();
    if (!topAbilities || topAbilities.length === 0)
      return 'Kemampuan unik untuk menciptakan dampak';

    const abilityName = topAbilities[0]; // topAbilities is string[]

    const strengths: { [key: string]: string } = {
      'Cognitive Skills':
        'Meta-thinking - berpikir tentang cara berpikir untuk mengoptimalkan proses kognitif',
      'Social Skills':
        'Social sensing - merasakan dinamika kelompok dan menyesuaikan komunikasi',
      'Technical Skills':
        'Tech translation - menerjemahkan kebutuhan bisnis menjadi solusi teknis',
      'Creative Skills':
        'Creative synthesis - menggabungkan elemen tak terduga menjadi solusi inovatif',
      'Leadership Skills':
        'Adaptive leadership - menyesuaikan gaya kepemimpinan dengan situasi dan tim',
      'Communication Skills':
        'Message crafting - menyesuaikan pesan dengan audiens dan konteks',
    };
    return (
      strengths[abilityName] ||
      'Kemampuan unik untuk menciptakan dampak dalam bidang ini'
    );
  }

  /**
   * Get ability creative ideas
   */
  getAbilityCreativeIdeas(): string[] {
    const topAbilities = this.getTopAbilities();
    if (!topAbilities || topAbilities.length === 0)
      return ['Proyek Inovatif', 'Eksperimen Kreatif'];

    const abilityName = topAbilities[0]; // topAbilities is string[]

    const ideas: { [key: string]: string[] } = {
      'Cognitive Skills': [
        'Cognitive Bootcamp',
        'Pattern Recognition Games',
        'Logic Escape Room',
      ],
      'Social Skills': [
        'Social Experiment Lab',
        'Conflict Resolution Theater',
        'Empathy Mapping Workshop',
      ],
      'Technical Skills': [
        'Tech for Good Hackathon',
        'Digital Minimalism Challenge',
        'Legacy System Revival',
      ],
      'Creative Skills': [
        'Constraint-based Creativity',
        'Cross-pollination Workshop',
        'Failure Gallery',
      ],
      'Leadership Skills': [
        'Reverse Leadership',
        'Crisis Simulation',
        'Values-based Decision Making',
      ],
      'Communication Skills': [
        'Silent Communication Challenge',
        'Multi-modal Storytelling',
        'Difficult Conversation Practice',
      ],
    };
    return (
      ideas[abilityName] || [
        'Innovative Projects',
        'Skill Challenges',
        'Collaborative Experiments',
      ]
    );
  }

  // ===== PERSONALITY ANALYSIS METHODS =====

  /**
   * Get personality description
   */
  getPersonalityDescription(): string {
    const personalityType = this.getPersonalityType();
    const descriptions: { [key: string]: string } = {
      'Visionary Driver':
        'Memandang masa depan sebagai kanvas kosong untuk dilukis, mengubah ketidakpastian menjadi peluang strategis',
      'Analytical Stabilizer':
        'Memandang data sebagai fondasi keputusan yang solid, mengubah chaos menjadi sistem yang terorganisir',
      'Collaborative Harmonizer':
        'Memandang perbedaan sebagai kekuatan komplementer, mengubah konflik menjadi kesempatan untuk saling memahami',
      'Independent Innovator':
        'Memandang konvensi sebagai starting point untuk improvement, mengubah keterbatasan menjadi catalyst untuk kreativitas',
      'Precision Analyst':
        'Fokus pada detail dan akurasi, memastikan kualitas tinggi dalam setiap output',
      'Dynamic Connector':
        'Energik dalam membangun hubungan dan menciptakan sinergi antar orang',
      'Steady Supporter':
        'Memberikan dukungan konsisten dan stabilitas dalam tim',
    };
    return (
      descriptions[personalityType] ||
      'Kombinasi unik dari berbagai aspek kepribadian'
    );
  }

  /**
   * Get personality communication style
   */
  getPersonalityCommunicationStyle(): string {
    const personalityType = this.getPersonalityType();
    const styles: { [key: string]: string } = {
      'Visionary Driver':
        'Inspiratif, berorientasi hasil, fokus pada gambaran besar',
      'Analytical Stabilizer': 'Faktual, metodis, berorientasi pada akurasi',
      'Collaborative Harmonizer':
        'Inklusif, supportive, berorientasi pada konsensus',
      'Independent Innovator':
        'Eksploratif, non-konformis, berorientasi pada solusi unik',
      'Precision Analyst': 'Detail-oriented, systematic, evidence-based',
      'Dynamic Connector': 'Energetic, relationship-focused, engaging',
      'Steady Supporter': 'Calm, consistent, supportive',
    };
    return styles[personalityType] || 'Adaptive communication style';
  }

  /**
   * Get personality communication preference
   */
  getPersonalityCommunicationPreference(): string {
    const personalityType = this.getPersonalityType();
    const preferences: { [key: string]: string } = {
      'Visionary Driver':
        'Diskusi strategis, brainstorming visi, planning session',
      'Analytical Stabilizer':
        'Laporan tertulis, analisis mendalam, diskusi berbasis data',
      'Collaborative Harmonizer':
        'Diskusi kelompok, one-on-one check-ins, team building',
      'Independent Innovator':
        'Brainstorming bebas, working alone, prototype sessions',
      'Precision Analyst':
        'Detailed documentation, structured meetings, data review',
      'Dynamic Connector':
        'Interactive discussions, networking events, collaborative sessions',
      'Steady Supporter':
        'Regular check-ins, supportive conversations, team meetings',
    };
    return preferences[personalityType] || 'Flexible communication approaches';
  }

  /**
   * Get personality team strengths
   */
  getPersonalityTeamStrengths(): string[] {
    const personalityType = this.getPersonalityType();
    const strengths: { [key: string]: string[] } = {
      'Visionary Driver': [
        'Catalyst untuk perubahan dan inovasi',
        'Motivator yang menggerakkan tim menuju tujuan ambisius',
        'Strategic thinker yang melihat peluang jangka panjang',
      ],
      'Analytical Stabilizer': [
        'Quality controller yang memastikan standar tinggi',
        'Risk assessor yang mengidentifikasi potensi masalah',
        'Process optimizer yang meningkatkan efisiensi sistem',
      ],
      'Collaborative Harmonizer': [
        'Mediator yang menyelesaikan konflik dengan diplomasi',
        'Culture keeper yang mempertahankan nilai-nilai tim',
        'Talent developer yang membantu orang lain berkembang',
      ],
      'Independent Innovator': [
        'Innovation catalyst yang menghasilkan ide-ide breakthrough',
        'Problem solver yang menemukan solusi non-obvious',
        'Change agent yang tidak takut menantang status quo',
      ],
      'Precision Analyst': [
        'Detail management',
        'Quality assurance',
        'Process improvement',
      ],
      'Dynamic Connector': [
        'Team building',
        'Communication facilitation',
        'Energy boosting',
      ],
      'Steady Supporter': [
        'Team stability',
        'Consistent support',
        'Conflict mediation',
      ],
    };
    return (
      strengths[personalityType] || [
        'Team collaboration',
        'Problem solving',
        'Communication',
      ]
    );
  }

  /**
   * Get personality development areas
   */
  getPersonalityDevelopmentAreas(): string[] {
    const personalityType = this.getPersonalityType();
    const areas: { [key: string]: string[] } = {
      'Visionary Driver': [
        'Patience dengan proses detail dan implementasi bertahap',
        'Listening skills untuk mendengar perspektif yang berbeda',
        'Delegation untuk menghindari burnout',
      ],
      'Analytical Stabilizer': [
        'Flexibility dalam menghadapi perubahan mendadak',
        'Intuitive decision making ketika data tidak lengkap',
        'Emotional intelligence dalam interaksi tim',
      ],
      'Collaborative Harmonizer': [
        'Assertiveness dalam menyampaikan pendapat yang unpopular',
        'Decision making yang tegas meski tidak semua setuju',
        'Boundary setting untuk menghindari people pleasing',
      ],
      'Independent Innovator': [
        'Collaboration skills untuk bekerja dalam struktur tim',
        'Communication untuk menjelaskan ide kompleks',
        'Follow-through untuk menyelesaikan proyek hingga tuntas',
      ],
      'Precision Analyst': [
        'Flexibility with ambiguity',
        'Speed in decision making',
        'Big picture thinking',
      ],
      'Dynamic Connector': [
        'Deep focus skills',
        'Individual work capability',
        'Structured planning',
      ],
      'Steady Supporter': [
        'Assertiveness training',
        'Change adaptability',
        'Leadership confidence',
      ],
    };
    return (
      areas[personalityType] || [
        'Continuous learning',
        'Skill diversification',
        'Leadership development',
      ]
    );
  }

  // ===== EXPERIENCE ANALYSIS METHODS =====

  /**
   * Get experience pattern
   */
  getExperiencePattern(): string {
    const experienceReport = this.processedReport?.experience_analysis;
    return (
      experienceReport?.experience_pattern?.pattern ||
      this.getEnhancedExperiencePattern()
    );
  }

  /**
   * Get experience description
   */
  getExperienceDescription(): string {
    const experienceReport = this.processedReport?.experience_analysis;
    return (
      experienceReport?.experience_pattern?.description ||
      this.getEnhancedExperienceDescription()
    );
  }

  /**
   * Get key experiences
   */
  getKeyExperiences(): any[] {
    const experienceReport = this.processedReport?.experience_analysis;
    return (
      experienceReport?.key_experiences || this.getEnhancedKeyExperiences()
    );
  }

  /**
   * Get learning pattern
   */
  getLearningPattern(): string {
    const experienceReport = this.processedReport?.experience_analysis;
    return (
      experienceReport?.learning_pattern || this.getEnhancedLearningPattern()
    );
  }

  /**
   * Get skill evolution
   */
  getSkillEvolution(): string[] {
    const experienceReport = this.processedReport?.experience_analysis;
    return (
      experienceReport?.skill_evolution || this.getEnhancedSkillEvolution()
    );
  }

  // ===== ENHANCED EXPERIENCE GUIDANCE METHODS =====

  private getEnhancedExperiencePattern(): string {
    // Based on combination of strength and heart
    const strength = this.getPrimaryStrength();
    const heart = this.getPrimaryHeart();

    if (strength.includes('Visionary') && heart === 'Business') {
      return 'Serial Entrepreneur Pattern';
    } else if (strength.includes('Supportive') && heart === 'Education') {
      return 'Lifelong Educator Pattern';
    } else if (
      strength.includes('Inspiring') &&
      heart === 'Communication / Media'
    ) {
      return 'Thought Leader Pattern';
    } else {
      return 'Multi-faceted Professional Pattern';
    }
  }

  private getEnhancedExperienceDescription(): string {
    const pattern = this.getExperiencePattern();
    const descriptions: { [key: string]: string } = {
      'Serial Entrepreneur Pattern':
        'Pola pengalaman yang menunjukkan kemampuan memulai dan mengembangkan berbagai inisiatif bisnis',
      'Lifelong Educator Pattern':
        'Pola pengalaman yang menunjukkan dedikasi konsisten dalam mengembangkan orang lain',
      'Thought Leader Pattern':
        'Pola pengalaman yang menunjukkan kemampuan mempengaruhi pemikiran dan tren industri',
      'Multi-faceted Professional Pattern':
        'Pola pengalaman yang menunjukkan adaptabilitas dan keragaman kemampuan',
    };
    return descriptions[pattern] || 'Pola pengalaman yang unik dan beragam';
  }

  private getEnhancedKeyExperiences(): any[] {
    const strength = this.getPrimaryStrength();
    const heart = this.getPrimaryHeart();

    return [
      {
        area: 'Leadership Experience',
        description: `Pengalaman memimpin tim atau proyek yang menggunakan kekuatan ${strength}`,
      },
      {
        area: 'Passion Project',
        description: `Proyek atau inisiatif yang berkaitan dengan passion di bidang ${heart}`,
      },
      {
        area: 'Learning Journey',
        description:
          'Pengalaman belajar yang membentuk perspektif dan kemampuan saat ini',
      },
      {
        area: 'Challenge Overcome',
        description:
          'Tantangan besar yang berhasil diatasi dan memberikan pembelajaran berharga',
      },
    ];
  }

  private getEnhancedLearningPattern(): string {
    const personalityType = this.getPersonalityType();
    const patterns: { [key: string]: string } = {
      'Visionary Driver':
        'Learn by doing - belajar melalui eksperimen dan implementasi langsung',
      'Analytical Stabilizer':
        'Learn by research - belajar melalui analisis mendalam dan studi kasus',
      'Collaborative Harmonizer':
        'Learn by discussion - belajar melalui dialog dan pertukaran perspektif',
      'Independent Innovator':
        'Learn by exploration - belajar melalui eksplorasi mandiri dan trial-error',
      'Precision Analyst':
        'Learn by structure - belajar melalui pendekatan sistematis dan terorganisir',
      'Dynamic Connector':
        'Learn by networking - belajar melalui interaksi dan kolaborasi dengan orang lain',
      'Steady Supporter':
        'Learn by observation - belajar melalui pengamatan dan refleksi',
    };
    return (
      patterns[personalityType] ||
      'Learn by adaptation - belajar melalui penyesuaian dengan situasi'
    );
  }

  private getEnhancedSkillEvolution(): string[] {
    const strength = this.getPrimaryStrength();
    const evolutions: { [key: string]: string[] } = {
      'Visionary Pioneers': [
        'Dari ide individual menjadi visi kolektif',
        'Dari eksperimen kecil menjadi transformasi sistemik',
        'Dari risk-taker menjadi calculated risk strategist',
      ],
      'Insightful Truth-Seekers': [
        'Dari kritik destruktif menjadi feedback konstruktif',
        'Dari perfectionist menjadi excellence facilitator',
        'Dari individual analyst menjadi team quality guardian',
      ],
      'Inspiring Connectors': [
        'Dari entertainer menjadi meaningful communicator',
        'Dari people pleaser menjadi authentic influencer',
        'Dari surface networking menjadi deep relationship building',
      ],
      'Supportive Nurturers': [
        'Dari helper menjadi empowerment catalyst',
        'Dari conflict avoider menjadi harmony creator',
        'Dari individual care menjadi system care designer',
      ],
      'Clarifying Mentors': [
        'Dari knowledge keeper menjadi wisdom facilitator',
        'Dari rigid teacher menjadi adaptive learning designer',
        'Dari individual mentoring menjadi scalable knowledge system',
      ],
    };
    return (
      evolutions[strength] || [
        'Dari individual contributor menjadi team enabler',
        'Dari task executor menjadi strategic thinker',
        'Dari skill user menjadi skill developer',
      ]
    );
  }

  // ============================================================================
  // NEW METHODS FOR CHART AND GUIDANCE INTEGRATION
  // ============================================================================

  /**
   * Get SHAPE overview data for radar chart
   */
  getShapeOverviewData(): ShapeOverviewData {
    if (!this.shapeResults) {
      return {
        strength: { name: 'Loading...', score: 0 },
        heart: { name: 'Loading...', score: 0 },
        abilities: { name: 'Loading...', score: 0 },
        personality: { name: 'Loading...', score: 0 },
        experience: { name: 'Loading...', score: 0 },
      };
    }

    return {
      strength: {
        name: this.shapeResults.strength?.primary || 'Unknown',
        score: this.shapeResults.strength?.score || 0,
      },
      heart: {
        name: this.shapeResults.heart?.primary || 'Unknown',
        score: this.shapeResults.heart?.score || 0,
      },
      abilities: {
        name: this.shapeResults.abilities?.primary || 'Unknown',
        score: this.shapeResults.abilities?.score || 0,
      },
      personality: {
        name: this.shapeResults.personality?.type || 'Unknown',
        score: this.shapeResults.personality?.score || 0,
      },
      experience: {
        name: this.shapeResults.experience?.pattern || 'Unknown',
        score: this.shapeResults.experience?.score || 0,
      },
    };
  }

  /**
   * Get primary abilities for overview
   */
  getPrimaryAbilities(): string {
    return this.shapeResults?.abilities?.primary || 'Loading...';
  }

  /**
   * Get primary personality for overview
   */
  getPrimaryPersonality(): string {
    return this.shapeResults?.personality?.type || 'Loading...';
  }

  /**
   * Get primary experience for overview
   */
  getPrimaryExperience(): string {
    return this.shapeResults?.experience?.pattern || 'Loading...';
  }

  /**
   * Get strength chart data for bar chart
   */
  getStrengthChartData(): AreaDetailData {
    if (!this.shapeResults?.strength?.category_scores) {
      return {
        categories: [{ name: 'Loading...', score: 0, level: 'Rendah' }],
        primaryCategory: 'Loading...',
        area: 'strength',
      };
    }

    const categories = Object.entries(
      this.shapeResults.strength.category_scores
    ).map(([name, score]) => ({
      name,
      score: Number(score),
      level: this.getScoreLevel(Number(score)),
    }));

    return {
      categories,
      primaryCategory: this.shapeResults.strength.primary || 'Unknown',
      area: 'strength',
    };
  }

  /**
   * Get score level based on score value
   */
  private getScoreLevel(score: number): 'Tinggi' | 'Moderat' | 'Rendah' {
    if (score >= 36) return 'Tinggi';
    if (score >= 21) return 'Moderat';
    return 'Rendah';
  }

  /**
   * Get strength natural talents from guidance
   */
  getStrengthNaturalTalents(): string[] {
    const guidance = this.guidanceContent['strength'];
    return guidance?.strengths || this.getStrengthTalents();
  }

  /**
   * Get strength team roles from guidance
   */
  getStrengthTeamRoles(): { title: string; description: string }[] {
    const guidance = this.guidanceContent['strength'];
    if (guidance?.team_roles) {
      return Object.entries(guidance.team_roles).map(
        ([title, description]) => ({
          title,
          description: String(description),
        })
      );
    }
    return [
      {
        title: 'Team Contributor',
        description: 'Memberikan kontribusi sesuai kekuatan utama',
      },
      {
        title: 'Skill Specialist',
        description: 'Mengembangkan keahlian di bidang tertentu',
      },
    ];
  }

  /**
   * Get strength development tips from guidance
   */
  getStrengthDevelopmentTips(): string[] {
    const guidance = this.guidanceContent['strength'];
    return (
      guidance?.development_tips || [
        'Fokus pada pengembangan kekuatan utama',
        'Cari mentor yang sesuai dengan bidang kekuatan',
        'Praktikkan keterampilan secara konsisten',
      ]
    );
  }

  /**
   * Get strength communication phrases from guidance
   */
  getStrengthCommunicationPhrases(): string[] {
    const guidance = this.guidanceContent['strength'];
    if (guidance?.communication_style?.bahasa) {
      return guidance.communication_style.bahasa;
    }
    return ['Mari kita coba', 'Bagaimana jika...', 'Saya yakin bisa'];
  }

  /**
   * Get strength communication pattern from guidance
   */
  getStrengthCommunicationPattern(): string {
    const guidance = this.guidanceContent['strength'];
    return (
      guidance?.communication_style?.pola ||
      'Komunikasi yang fokus pada solusi dan hasil'
    );
  }

  /**
   * Get strength weaknesses from guidance
   */
  getStrengthWeaknesses(): string[] {
    const guidance = this.guidanceContent['strength'];
    return (
      guidance?.weaknesses || [
        'Mungkin terlalu fokus pada satu area',
        'Perlu mengembangkan area lain untuk keseimbangan',
      ]
    );
  }

  /**
   * Load guidance content for current results
   */
  private loadGuidanceContent(): void {
    if (!this.shapeResults) return;

    this.guidanceService.getShapeGuidance(this.shapeResults).subscribe({
      next: (guidance) => {
        this.guidanceContent = guidance;
      },
      error: (error) => {
        console.error('Error loading guidance content:', error);
      },
    });
  }
}
