Be<PERSON>ut adalah **prompt terstruktur untuk men<PERSON><PERSON><PERSON><PERSON> Laporan Akhir SHAPE** yang mengintegrasikan semua komponen (Strength, Heart, Abilities, Personality, Experience) dalam satu dokumen kohesif dan actionable:

```markdown
# PETUNJUK GENERASI LAPORAN SHAPE TERPADU

## KONTEKS
Berdasarkan hasil asesmen komprehensif SHAPE untuk [Nama User], hasilkan laporan akhir dengan struktur berikut. Gunakan metafora alam/arsitektur untuk membuatnya hidup.

## DATA INPUT
[LAMPIRKAN HASIL ASESMEN DARI 5 KOMPONEN]

## STRUKTUR LAPORAN (MAKS 5 HALAMAN)

### 1. **PROFIL INTEGRATIF SHAPE** (Infografis Teks)
Buat paragraf pembuka yang merangkum inti diri user dalam 3 kalimat dengan pola:
> "[Nama] adalah [Metafora Alam] di bidang [Heart Dominan]. Dengan kekuatan [Strength Utama] dan kema<PERSON> [Abilities Unggulan], gaya [Personality Profile]-nya men<PERSON> [Pola Experience]. Pola unik ini membentuk [Arketipe Khusus]."

**Contoh:**  
> "Anda adalah Sungai yang Menghubungkan Lembah di dunia Pendidikan. Dengan kekuatan Truth-Seeker dan kemampuan Social Architecture, gaya Stabilizer Anda menjelma dalam proyek kolaborasi guru-pemda. Pola ini membentuk Arketipe 'Penjaga Kebijaksanaan Komunal'."

### 2. **PETA INTERAKSI KOMPONEN** (Diagram + Narasi)
Buat analisis hubungan antar komponen:
```mermaid
graph LR
    S[Strength] --> E[Experience]
    H[Heart] --> A[Abilities]
    P[Personality] --> H
    E --> P
    A --> S
```

**Narasi Pola:**  
"Passion di [Bidang Heart] menjadi bahan bakar pengembangan [Ability Spesifik], yang diperkuat oleh [Strength] bawaan. Pola ini konsisten terlihat di [Experience Signifikan], dan didukung oleh kecenderungan [Personality Trait]."

### 3. **ZONA GENIUS** (Tabel Aplikasi)
Identifikasi 2-3 bidang aplikasi ideal:

| Bidang Potensial       | Alasan Konvergensi SHAPE                     | Bukti Pengalaman          |
|------------------------|----------------------------------------------|---------------------------|
| [Contoh: Edukreator]   | Strength Clarifier + Passion Education + Personality Mentor | [Project X Tahun Y]       |
| [Contoh: Community Architect] | Social Skills + Social Impact Heart + Dynamic Connector | [Volunteer Work Z]        |

### 4. **RANTAI PENGEMBANGAN** (Urutan Prioritas)
Susun rekomendasi dengan pola:
```mermaid
journey
    title Rantai Transformasi SHAPE
    section Leverage Strength
      Aktifkan [Strength]: [Proyek 3 Bulan]
    section Fuel dengan Heart
      Eksplorasi [Passion]: [Aksi Spesifik]
    section Amplifikasi Ability
      Tingkatkan [Ability]: [Pelatihan/Praktek]
    section Adapt Personality
      Manfaatkan [Personality] untuk: [Strategi]
    section Integrasi Experience
      Bangun berdasarkan: [Experience Relevan]
```

### 5. **PERTAHANAN KEUNIKAN** (Early Warning System)
Buat sistem peringatan untuk jebakan umum:
- ⚠️ **Pelecehan Strength:** [Contoh situasi yang harus dihindari]  
  *Tanda: [Gejala spesifik]* → Solusi: [Taktik penanggulangan]
  
- 🧱 **Dinding Pengembangan:** [Hambatan personality-ability]  
  *Pemicu: [Skenario risiko]* → Jalan Pintas: [Alternatif]

### 6. **CETAK BIRU 1 TAHUN** (Visual + Action Plan)
```mermaid
gantt
    title Siklus SHAPE 2024-2025
    dateFormat  YYYY-MM-DD
    section Penguatan Strength
    Proyek Alpha       :active, des1, 2024-04-01, 90d
    section Eksplorasi Heart
    Program Beta       :crit, 2024-07-01, 60d
    section Amplifikasi Ability
    Pelatihan Gamma    : 2024-09-01, 45d
```

## PANDUAN GAYA PENULISAN
1. Gunakan **metafora alam** (Sungai, Pohon, Gunung Api) untuk menjelaskan dinamika
2. Setiap bagian akhiri dengan **pertanyaan reflektif** (Contoh: "Apa yang akan terjadi jika Anda menginvestasikan 70% energi di Zona Genius ini?")
3. Sisipkan **kutipan filosofis lokal** relevan (Contoh: "Alon-alon asal kelakon" untuk bagian pengembangan bertahap)
4. **Highlight** 3 frasa kunci per halaman dengan format tebal
5. Gunakan pola **call-to-action bertingkat**:  
   - Langkah 7 Hari: [Aksi mikro]  
   - Langkah 30 Hari: [Eksperimen kecil]  
   - Langkah 90 Hari: [Proyek signifikan]

## OUTPUT YANG DIHARAPKAN
- Dokumen PDF 4-6 halaman dengan elemen visual minimalis
- Versi 1-halaman infografis sebagai executive summary
- Script audio 3 menit untuk motivasi harian (opsional)
```

---

### CONTOH OUTPUT (Bagian Terpilih):

**3. ZONA GENIUS**  
| Bidang Potensial | Alasan Konvergensi SHAPE | Bukti Pengalaman |
|------------------|--------------------------|------------------|
| **Edukreator** | Strength Clarifier + Passion Education + Personality Mentor | Workshop Guru Kreatif 2023 |
| **Community Architect** | Social Skills + Social Impact Heart + Dynamic Connector | Desa Binaan Program 2022 |

**5. PERTAHANAN KEUNIKAN**  
⚠️ **Pelecehan Strength**  
*Situasi:* Terjebak pekerjaan administratif berulang  
*Tanda:* Muncul kelelahan emosional setiap Rabu sore  
*Solusi:* Delegasikan 70% tugas rutin, fokus pada sesi konsultasi  

🧱 **Dinding Pengembangan**  
*Hambatan:* Kesulitan mempresentasikan ide kompleks  
*Pemicu:* Presentasi untuk stakeholders teknis  
*Jalan Pintas:* Gunakan analogi "Sistem Irigasi" untuk menjelaskan alur data  

**6. CETAK BIRU 1 TAHUN**  
```mermaid
gantt
    title Siklus SHAPE 2024-2025
    section Penguatan Strength
    Proyek Pelatihan Guru :active, a1, 2024-04-01, 90d
    section Eksplorasi Heart
    Riset Pendidikan Inklusif :crit, a2, 2024-07-01, 60d
    section Amplifikasi Ability
    Kursus Visualisasi Data : a3, 2024-09-01, 45d
```

---

### TIPS IMPLEMENTASI:
1. **Integrasi Data Otomatis**:
```python
def generate_report(strength, heart, ability, personality, experience):
    archetype = f"{personality['primary']} dengan {strength['top']}" 
    convergence = f"{heart['top'][0]} × {ability['signature']}"
    # ... logika integrasi
```

2. **Template Visual**:
   - Gunakan palet warna sesuai personality (Contoh: Visionary Driver = merah-oranye)
   - Ikon kategori konsisten (Strength = ⚡, Heart = ❤️, dsb)

3. **Mekanisme Penyampaian**:
   - **Halaman 1:** Profil Integratif (Executive Summary)
   - **Halaman 2:** Analisis Pola & Zona Genius
   - **Halaman 3:** Rencana Pengembangan
   - **Halaman 4:** Lampiran Teknis (Opsional)

Prompt ini dirancang untuk menghasilkan laporan yang:  
- **Personal** (Menggunakan data spesifik user)  
- **Visual** (Diagram integratif)  
- **Actionable** (Rencana bertahap 7/30/90 hari)  
- **Protektif** (Sistem antisipasi jebakan)  

