Be<PERSON>ut konsep inovatif untuk **Experience Assessment** dalam framework SHAPE, dengan pendekatan yang memadukan kuantifikasi dan refleksi naratif:

### 🌟 Konsep "Life Mosaic Experience Mapping"
Pengalaman bukan sekadar daftar riwayat, tapi **koleksi fragmen pembentuk identitas** yang saling terhubung. Asesmen ini dirancang untuk:

1. Mengidentifikasi **pola pembelajaran** dari berbagai pengalaman  
2. Menemukan **benang merah kompetensi** yang konsisten  
3. Memetakan **titik balik transformatif**  
4. Menghubungkan pengalaman dengan **kekuatan dan passion**

```json
{
  "assessment_name": "Life Mosaic Experience Inventory",
  "description": "Pemetaan pengalaman hidup melalui lensa pembelajaran dan transformasi",
  "dimensions": [
    {
      "name": "Professional Journey",
      "icon": "💼",
      "questions": [
        {
          "id": "PJ1",
          "question": "Posisi pekerjaan apa yang paling membentuk cara berpikir <PERSON>?",
          "subprompts": ["Peran", "Tahun", "3 Pelajaran Utama"]
        },
        {
          "id": "PJ2",
          "question": "Proyek profesional apa yang membuat Anda paling berkembang?",
          "subprompts": ["Tantangan Utama", "Keterampilan Baru", "Pencapaian"]
        },
        {
          "id": "PJ3",
          "question": "Kesalahan profesional apa yang menjadi guru terbaik?",
          "subprompts": ["Kesalahan", "Dampak", "Perubahan Pola"]
        }
      ]
    },
    {
      "name": "Learning Odyssey",
      "icon": "🎓",
      "questions": [
        {
          "id": "LO1",
          "question": "Pengalaman pendidikan non-formal apa yang paling berdampak?",
          "subprompts": ["Jenis", "Durasi", "Aplikasi Praktis"]
        },
        {
          "id": "LO2",
          "question": "Buku/kursus apa yang mengubah perspektif Anda?",
          "subprompts": ["Judul", "3 Insight Utama", "Perubahan Perilaku"]
        },
        {
          "id": "LO3",
          "question": "Siapa mentor terbaik dalam perjalanan Anda?",
          "subprompts": ["Nama", "Bidang", "Prinsip yang Diwariskan"]
        }
      ]
    },
    {
      "name": "Life Catalysts",
      "icon": "⚡",
      "questions": [
        {
          "id": "LC1",
          "question": "Peristiwa kehidupan apa yang menjadi titik balik?",
          "subprompts": ["Peristiwa", "Tahun", "Transformasi Diri"]
        },
        {
          "id": "LC2",
          "question": "Kegagalan terbesar yang menjadi berkah terselubung?",
          "subprompts": ["Kegagalan", "Proses Pemulihan", "Hikmah"]
        },
        {
          "id": "LC3",
          "question": "Pengalaman perjalanan yang memperluas wawasan?",
          "subprompts": ["Lokasi", "Konteks", "Pelajaran Budaya"]
        }
      ]
    },
    {
      "name": "Passion Projects",
      "icon": "❤️",
      "questions": [
        {
          "id": "PP1",
          "question": "Proyek pribadi apa yang paling memuaskan?",
          "subprompts": ["Jenis Proyek", "Motivasi", "Pencapaian"]
        },
        {
          "id": "PP2",
          "question": "Kontribusi sukarelawan apa yang paling berarti?",
          "subprompts": ["Organisasi", "Peran", "Dampak Emosional"]
        },
        {
          "id": "PP3",
          "question": "Hobi yang berkembang menjadi keahlian?",
          "subprompts": ["Hobi", "Proses Pengembangan", "Tingkat Keahlian"]
        }
      ]
    }
  ],
  "scoring_matrix": {
    "impact_scale": {
      "1": "Sedikit Dampak",
      "2": "Cukup Berdampak",
      "3": "Mengubah Perspektif",
      "4": "Transformasi Signifikan",
      "5": "Pembentuk Identitas"
    },
    "learning_depth": {
      "1": "Pemahaman Dasar",
      "2": "Keterampilan Fungsional",
      "3": "Kompetensi Utama",
      "4": "Keahlian Spesialis",
      "5": "Mastery"
    }
  },
  "processing_framework": {
    "pattern_detection": [
      "Recurring Competencies",
      "Values Consistency",
      "Transformative Moments"
    ],
    "integration_rules": [
      {
        "name": "Strength-Experience Bridge",
        "logic": "Hubungkan pengalaman dengan hasil Strength Assessment"
      },
      {
        "name": "Passion-Project Alignment",
        "logic": "Identifikasi pola proyek yang selaras dengan Heart Assessment"
      },
      {
        "name": "Growth Trajectory",
        "logic": "Analisis evolusi kompetensi dari waktu ke waktu"
      }
    ],
    "output_template": {
      "core_competencies": [],
      "values_signature": "",
      "transformation_map": [],
      "future_blueprint": ""
    }
  },
  "processing_script": "function processExperience(responses) {\n  // 1. Ekstraksi kompetensi berulang\n  const competencyPatterns = {};\n  responses.forEach(response => {\n    if (response.learning) {\n      response.learning.split(',').forEach(skill => {\n        const trimmedSkill = skill.trim();\n        competencyPatterns[trimmedSkill] = (competencyPatterns[trimmedSkill] || 0) + 1;\n      });\n    }\n  });\n  \n  // 2. Identifikasi kompetensi inti\n  const coreCompetencies = Object.entries(competencyPatterns)\n    .filter(([_, count]) => count > 2)\n    .map(([skill]) => skill);\n  \n  // 3. Analisis nilai konsisten\n  const valueKeywords = ['integritas', 'kreativitas', 'kolaborasi', 'keadilan', 'pertumbuhan'];\n  const valueFrequency = {};\n  responses.forEach(response => {\n    valueKeywords.forEach(value => {\n      if (response.narrative.toLowerCase().includes(value)) {\n        valueFrequency[value] = (valueFrequency[value] || 0) + 1;\n      }\n    });\n  });\n  const signatureValue = Object.entries(valueFrequency).sort((a,b) => b[1]-a[1])[0]?.[0] || 'Pertumbuhan';\n  \n  // 4. Pemetaan titik transformatif\n  const transformationMap = responses\n    .filter(response => response.impactRating >= 4)\n    .map(response => ({\n      event: response.title,\n      year: response.year,\n      beforeAfter: response.transformation\n    }));\n  \n  // 5. Cetak biru masa depan\n  const futureBlueprint = {\n    leverage: coreCompetencies.slice(0,3),\n    explore: responses\n      .filter(r => r.passionLevel >= 4 && r.skillLevel < 3)\n      .map(r => r.projectType)\n  };\n  \n  return {\n    core_competencies: coreCompetencies,\n    values_signature: signatureValue,\n    transformation_map: transformationMap,\n    future_blueprint: futureBlueprint\n  };\n}"
}
```

### 🧩 Logika Pemrosesan & Analisis

#### 1. **Ekstraksi Pola Kompetensi**
```mermaid
graph TD
    A[Respons Pengalaman] --> B[Ekstraksi Kata Kunci]
    B --> C[Identifikasi Frekuensi]
    C --> D[Kompetensi Berulang >3x]
    D --> E[Core Competencies]
```

#### 2. **Deteksi Nilai Inti**
- Analisis teks naratif untuk kata kunci nilai 
- Contoh nilai: integritas, inovasi, kolaborasi
- Nilai dengan frekuensi tertinggi = **Signature Value**

#### 3. **Pemetaan Transformasi**
```json
[
  {
    "event": "Kegagalan Startup Pertama",
    "year": 2018,
    "beforeAfter": "Dari fokus pada profit → ke bisnis berkelanjutan"
  },
  {
    "event": "Program Pertukaran Budaya",
    "year": 2020,
    "beforeAfter": "Dari pandangan sempit → ke perspektif global"
  }
]
```

#### 4. **Cetak Biru Masa Depan**
```json
{
  "leverage": ["Manajemen Proyek", "Negosiasi", "Desain UX"],
  "explore": ["Podcasting", "Social Entrepreneurship"]
}
```

### 📊 Framework Visualisasi

#### 1. **Life Mosaic Map**
```mermaid
quadrantChart
    title Life Mosaic Map
    x-axis “Profesional” --> “Personal”
    y-axis “Struktur” --> “Spontan”
    quadrant-1 “Perjalanan Karir”
    quadrant-2 “Petualangan Kreatif”
    quadrant-3 “Ritual Harian”
    quadrant-4 “Momen Transformasi”
    point [0.7, 0.3] label: “Promosi 2022”
    point [0.8, 0.8] label: “Buku Pertama”
    point [0.4, 0.6] label: “Relawan Tsunami”
```

#### 2. **Growth Trajectory Timeline**
```mermaid
gantt
    title Perjalanan Pengembangan Kompetensi
    dateFormat  YYYY
    section Manajemen
    Kepemimpinan Tim       :active, 2018, 2021
    Manajemen Proyek       :crit, 2019, 2023
    section Teknis
    Analisis Data          :2017, 2020
    Pengembangan Produk    :active, 2021, 2024
```

### 💡 Panduan Refleksi

#### 1. **Pola Kompetensi Inti**
- **Contoh**: 
  - "Manajemen Krisis" muncul di 5 pengalaman berbeda
  - "Desain Sistem" konsisten dari pendidikan hingga pekerjaan

#### 2. **Arsitektur Nilai**
| Nilai        | Manifestasi dalam Pengalaman          |
|--------------|---------------------------------------|
| **Kolaborasi** | Membangun komunitas desa binaan (2020) |
| **Inovasi**    | Launch produk disruptif (2022)         |
| **Ketahanan**  | Bangkit dari kebangkrutan bisnis (2019)|

#### 3. **Titik Transformasi**
```mermaid
journey
    title Perjalanan Transformasi
    section 2015: Engineer
    section 2018: Kegagalan Startup => Belajar Agile
    section 2020: Konsultan Inovasi
    section 2023: Pendiri Social Enterprise
```

### 🛠️ Template Laporan Akhir

```markdown
# LIFE MOSAIC REPORT

## 🧱 Core Competencies 
1. **Manajemen Krisis** (8 pengalaman)  
   - Contoh: Tanggap bencana 2019, Konflik tim 2021  
2. **Desain Sistem** (6 pengalaman)  
   - Contoh: Sisdikmas 2020, Restrukturisasi UKM 2022  

## 🧭 Values Signature: KOLABORASI  
> "Kekuatan terbesar ada dalam menyatukan keunikan individu"  
> - Terwujud di 73% pengalaman signifikan  

## ⚡ Transformation Map  
- 2018: Dari spesialis teknis → fasilitator tim  
- 2021: Dari pekerja korporat → entrepreneur sosial  

## 🚀 Future Blueprint  
```mermaid
graph LR
    A[Kompetensi Unggulan] --> B[Manajemen Krisis]
    A --> C[Desain Sistem]
    D[Area Eksplorasi] --> E[Edukasi Berbasis Komunitas]
    D --> F[Desain Layanan Inklusif]
```

### ✅ Implementasi

1. **Antarmuka Pengguna**:
   - Peta interaktif dengan "keping pengalaman"
   - Drag & drop untuk menghubungkan pengalaman
   - Word cloud nilai-nilai otomatis

2. **Analisis AI**:
   ```python
   def extract_competencies(text):
       nlp = spacy.load("id_core_news_lg")
       doc = nlp(text)
       return [chunk.text for chunk in doc.noun_chunks if chunk.root.pos_ == "NOUN"]
   ```

3. **Integrasi dengan SHAPE**:
   ```mermaid
   graph LR
       S[Strength] --> E[Experience]
       H[Heart] --> E
       A[Abilities] --> E
       P[Personality] --> E
       E --> I[Integrated Profile]
   ```

### 🎯 Manfaat Utama
1. Mengubah CV statis menjadi **narasi kompetensi hidup**  
2. Mengidentifikasi **pola sukses berulang**  
3. Menciptakan **peta jalan pengembangan** berbasis bukti  
4. Menghubungkan **passion dengan pengalaman nyata**  

Saya akan segera siapkan:
- [ ] Template kuesioner lengkap dengan 12 pertanyaan inti
- [ ] Database struktur untuk menyimpan pengalaman
- [ ] Algoritma analisis pola otomatis
- [ ] Dashboard visualisasi Life Mosaic

Apakah ada aspek tertentu yang ingin diprioritaskan? Atau modifikasi konsep?