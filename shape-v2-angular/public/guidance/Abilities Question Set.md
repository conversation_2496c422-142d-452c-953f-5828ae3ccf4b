Be<PERSON><PERSON> adalah **Abilities Assessment** lengkap dengan 6 kategori kemampuan, masing-masing 10 pertanyaan (total 60 pertanyaan), dalam format JSON beserta logika skoringnya:

```json
{
  "assessment_name": "Comprehensive Abilities Assessment",
  "description": "Asesmen untuk mengevaluasi 6 jenis kemampuan utama",
  "scoring_scale": "1-5 (1=Sangat Tidak Setuju, 5=Sangat Setuju)",
  "categories": [
    {
      "name": "Cognitive Skills",
      "icon": "🧠",
      "description": "<PERSON>ma<PERSON><PERSON> berpikir, analisis, dan pemecahan masalah",
      "questions": [
        {"id": "COG1", "question": "Saya mudah memahami konsep-konsep rumit"},
        {"id": "COG2", "question": "Saya bisa menganalisis data/informasi dengan baik"},
        {"id": "COG3", "question": "Memecahkan masalah teknis adalah keahlian saya"},
        {"id": "COG4", "question": "Saya cepat menangkap pola atau hubungan antar informasi"},
        {"id": "COG5", "question": "Saya mampu membuat keputusan logis dengan cepat"},
        {"id": "COG6", "question": "Mempelajari teori atau konsep baru mudah bagi saya"},
        {"id": "COG7", "question": "Saya bisa memprediksi konsekuensi dari suatu keputusan"},
        {"id": "COG8", "question": "Saya mahir dalam perencanaan strategis jangka panjang"},
        {"id": "COG9", "question": "Saya mampu fokus pada tugas kompleks dalam waktu lama"},
        {"id": "COG10", "question": "Menggunakan logika untuk menyelesaikan masalah adalah kekuatan saya"}
      ]
    },
    {
      "name": "Social Skills",
      "icon": "👥",
      "description": "Kemampuan berinteraksi, berkomunikasi, dan bekerja sama",
      "questions": [
        {"id": "SOC1", "question": "Saya mudah menjalin hubungan baru dengan orang"},
        {"id": "SOC2", "question": "Mendengarkan orang lain dengan empati adalah keahlian saya"},
        {"id": "SOC3", "question": "Saya bisa menyelesaikan konflik antar teman/rekan"},
        {"id": "SOC4", "question": "Menyampaikan ide dengan jelas adalah kekuatan saya"},
        {"id": "SOC5", "question": "Saya nyaman berbicara di depan kelompok"},
        {"id": "SOC6", "question": "Memahami perasaan orang lain mudah bagi saya"},
        {"id": "SOC7", "question": "Saya bisa memotivasi orang lain dengan efektif"},
        {"id": "SOC8", "question": "Membangun jaringan profesional adalah keahlian saya"},
        {"id": "SOC9", "question": "Saya pandai menyesuaikan komunikasi dengan lawan bicara"},
        {"id": "SOC10", "question": "Bekerja dalam tim adalah hal yang saya nikmati"}
      ]
    },
    {
      "name": "Technical Skills",
      "icon": "💻",
      "description": "Kemampuan menggunakan alat, teknologi, dan keahlian teknis",
      "questions": [
        {"id": "TEC1", "question": "Saya cepat menguasai perangkat lunak baru"},
        {"id": "TEC2", "question": "Memperbaiki masalah teknis adalah keahlian saya"},
        {"id": "TEC3", "question": "Saya mahir menggunakan alat-alat digital"},
        {"id": "TEC4", "question": "Memahami spesifikasi teknis mudah bagi saya"},
        {"id": "TEC5", "question": "Saya mampu mempelajari bahasa pemrograman dengan cepat"},
        {"id": "TEC6", "question": "Membuat dokumen teknis adalah hal yang mudah"},
        {"id": "TEC7", "question": "Saya bisa mengoperasikan peralatan elektronik kompleks"},
        {"id": "TEC8", "question": "Memahami diagram teknis tidak sulit bagi saya"},
        {"id": "TEC9", "question": "Saya senang mempelajari teknologi baru"},
        {"id": "TEC10", "question": "Troubleshooting masalah teknis adalah keahlian saya"}
      ]
    },
    {
      "name": "Management Skills",
      "icon": "📊",
      "description": "Kemampuan mengatur, mengorganisir, dan memimpin",
      "questions": [
        {"id": "MGT1", "question": "Saya mahir mengatur waktu dan prioritas"},
        {"id": "MGT2", "question": "Memimpin proyek atau tim adalah keahlian saya"},
        {"id": "MGT3", "question": "Saya bisa membuat rencana kerja yang efektif"},
        {"id": "MGT4", "question": "Mendelegasikan tugas dengan tepat adalah kekuatan saya"},
        {"id": "MGT5", "question": "Saya mampu mengelola anggaran atau sumber daya"},
        {"id": "MGT6", "question": "Memantau kemajuan pekerjaan adalah hal yang mudah"},
        {"id": "MGT7", "question": "Saya bisa mengambil keputusan di bawah tekanan"},
        {"id": "MGT8", "question": "Mengorganisir acara atau kegiatan adalah keahlian saya"},
        {"id": "MGT9", "question": "Saya mampu mengevaluasi hasil kerja dengan objektif"},
        {"id": "MGT10", "question": "Memotivasi tim untuk mencapai target adalah keahlian saya"}
      ]
    },
    {
      "name": "Creative Skills",
      "icon": "🎨",
      "description": "Kemampuan berkreasi, berinovasi, dan seni",
      "questions": [
        {"id": "CRE1", "question": "Saya mudah menghasilkan ide-ide baru"},
        {"id": "CRE2", "question": "Bermain alat musik adalah keahlian saya"},
        {"id": "CRE3", "question": "Menari atau bergerak dengan ritme adalah hal alami"},
        {"id": "CRE4", "question": "Saya mahir dalam seni visual (melukis, menggambar, dll)"},
        {"id": "CRE5", "question": "Menulis cerita atau puisi adalah kegiatan yang saya nikmati"},
        {"id": "CRE6", "question": "Saya bisa melihat solusi yang tidak terlihat oleh orang lain"},
        {"id": "CRE7", "question": "Mendesain sesuatu yang estetis mudah bagi saya"},
        {"id": "CRE8", "question": "Saya mampu mengimprovisasi di situasi tak terduga"},
        {"id": "CRE9", "question": "Menciptakan karya seni adalah hobi saya"},
        {"id": "CRE10", "question": "Saya senang mengeksplorasi bentuk ekspresi baru"}
      ]
    },
    {
      "name": "Physical Skills",
      "icon": "🏃",
      "description": "Kemampuan fisik, koordinasi, dan ketahanan tubuh",
      "questions": [
        {"id": "PHY1", "question": "Saya memiliki koordinasi tubuh yang baik"},
        {"id": "PHY2", "question": "Melakukan aktivitas fisik berat tidak sulit bagi saya"},
        {"id": "PHY3", "question": "Saya mahir dalam setidaknya satu cabang olahraga"},
        {"id": "PHY4", "question": "Memiliki stamina fisik yang baik adalah kelebihan saya"},
        {"id": "PHY5", "question": "Saya cepat mempelajari gerakan fisik baru"},
        {"id": "PHY6", "question": "Ketangkasan tubuh adalah salah satu kekuatan saya"},
        {"id": "PHY7", "question": "Saya bisa mengendalikan tubuh dengan presisi"},
        {"id": "PHY8", "question": "Memiliki refleks yang cepat adalah keahlian saya"},
        {"id": "PHY9", "question": "Saya menikmati aktivitas fisik yang menantang"},
        {"id": "PHY10", "question": "Kesehatan fisik adalah prioritas dalam hidup saya"}
      ]
    }
  ],
  "scoring_logic": {
    "method": "category_sum",
    "max_score_per_category": 50,
    "interpretation": [
      {"range": "10-25", "level": "Kemampuan Dasar"},
      {"range": "26-35", "level": "Kemampuan Menengah"},
      {"range": "36-50", "level": "Kemampuan Unggulan"}
    ],
    "analysis_rules": [
      {
        "name": "Primary Ability",
        "condition": "highest_score",
        "action": "Identifikasi sebagai kemampuan utama"
      },
      {
        "name": "Complementary Abilities",
        "condition": "score > 35",
        "action": "Tandai sebagai kemampuan pendukung kuat"
      },
      {
        "name": "Development Area",
        "condition": "score < 26",
        "action": "Tandai sebagai area pengembangan"
      }
    ]
  },
  "result_template": {
    "top_abilities": [],
    "complementary_abilities": [],
    "development_areas": [],
    "strength_profile": ""
  },
  "processing_script": "function calculateAbilities(scores) {\n  // Hitung skor per kategori\n  const categoryScores = {};\n  for (const category of categories) {\n    const categoryName = category.name;\n    categoryScores[categoryName] = scores\n      .filter(score => category.questions.some(q => q.id === score.id))\n      .reduce((sum, item) => sum + item.value, 0);\n  }\n  \n  // Urutkan kemampuan dari tertinggi ke terendah\n  const sortedScores = Object.entries(categoryScores).sort((a, b) => b[1] - a[1]);\n  \n  // Identifikasi kemampuan utama (tertinggi)\n  const topAbilities = [sortedScores[0][0]];\n  \n  // Identifikasi kemampuan pendukung (skor >35)\n  const complementaryAbilities = sortedScores\n    .filter(item => item[1] > 35 && item[0] !== topAbilities[0])\n    .map(item => item[0]);\n  \n  // Identifikasi area pengembangan (skor <26)\n  const developmentAreas = sortedScores\n    .filter(item => item[1] < 26)\n    .map(item => item[0]);\n  \n  // Buat profil kekuatan\n  const strengthProfiles = {\n    \"Cognitive+Technical\": \"Problem Solver\",\n    \"Social+Management\": \"Team Leader\",\n    \"Creative+Physical\": \"Performer\",\n    \"Technical+Management\": \"Project Expert\",\n    \"Social+Creative\": \"Creative Communicator\"\n  };\n  \n  let strengthProfile = \"Generalist\";\n  if (topAbilities.length > 0 && complementaryAbilities.length > 0) {\n    const profileKey = `${topAbilities[0].split(' ')[0]}+${complementaryAbilities[0].split(' ')[0]}`;\n    strengthProfile = strengthProfiles[profileKey] || \"Specialized Generalist\";\n  }\n  \n  return {\n    top_abilities: topAbilities,\n    complementary_abilities: complementaryAbilities,\n    development_areas: developmentAreas,\n    strength_profile: strengthProfile\n  };\n}"
}
```

### 📊 Logika Pemrosesan & Interpretasi

1. **Metode Skoring**:
   - Setiap jawaban diberi nilai 1-5
   - Total skor per kategori = jumlah nilai 10 pertanyaan
   - Skor maksimal per kategori = 50

2. **Kategori Kemampuan**:
   - **Cognitive Skills**: Berpikir logis, analisis data, pemecahan masalah
   - **Social Skills**: Komunikasi, empati, kerja tim
   - **Technical Skills**: Teknologi, alat digital, pemrograman
   - **Management Skills**: Kepemimpinan, organisasi, manajemen waktu
   - **Creative Skills**: Seni, musik, inovasi, desain
   - **Physical Skills**: Olahraga, ketangkasan, stamina

3. **Interpretasi Skor**:
   - **10-25**: Kemampuan dasar (perlu pengembangan)
   - **26-35**: Kemampuan menengah (bisa ditingkatkan)
   - **36-50**: Kemampuan unggulan (kekuatan utama)

4. **Analisis Hasil**:
   - **Top Abilities**: Kategori dengan skor tertinggi
   - **Complementary Abilities**: Kategori dengan skor >35
   - **Development Areas**: Kategori dengan skor <26
   - **Strength Profile**: Profil berdasarkan kombinasi kemampuan utama dan pendukung

### 🧩 Contoh Output Hasil

```json
{
  "top_abilities": ["Social Skills"],
  "complementary_abilities": ["Management Skills", "Creative Skills"],
  "development_areas": ["Technical Skills"],
  "strength_profile": "Team Leader"
}
```

### 📝 Panduan Interpretasi

#### 1. Profil Kekuatan (Strength Profile)
| Kombinasi Kemampuan          | Profil              | Deskripsi                                        |
|------------------------------|---------------------|--------------------------------------------------|
| Social + Management          | Team Leader         | Pemimpin tim yang inspiratif                     |
| Cognitive + Technical        | Problem Solver      | Ahli menyelesaikan masalah teknis               |
| Creative + Physical          | Performer           | Penampil berbakat (seni/olahraga)               |
| Technical + Management       | Project Expert      | Spesialis manajemen proyek teknis               |
| Social + Creative            | Creative Communicator | Komunikator inovatif                            |
| Cognitive + Social           | Strategic Advisor   | Penasihat strategis berbasis hubungan           |
| Lainnya                      | Specialized Generalist | Ahli dengan kemampuan beragam                  |

#### 2. Rekomendasi Pengembangan
- **Kemampuan Unggulan (36-50)**: 
  - Pertahankan dan tingkatkan ke level ahli
  - Cari tantangan baru di bidang tersebut
  - Ajarkan ke orang lain
  
- **Kemampuan Menengah (26-35)**:
  - Ikuti pelatihan khusus
  - Praktekkan secara teratur
  - Cari mentor di bidang tersebut
  
- **Kemampuan Dasar (10-25)**:
  - Mulai dengan dasar-dasar
  - Ikuti kursus pengantar
  - Praktekkan dalam kehidupan sehari-hari

### 💻 Implementasi Teknis

1. **Skoring Otomatis**:
```javascript
function calculateScores(responses) {
  const scores = {};
  
  // Inisialisasi skor kategori
  categories.forEach(category => {
    scores[category.name] = 0;
  });
  
  // Hitung skor per kategori
  responses.forEach(response => {
    const category = categories.find(cat => 
      cat.questions.some(q => q.id === response.questionId)
    );
    if (category) {
      scores[category.name] += response.score;
    }
  });
  
  return calculateAbilities(scores);
}
```

2. **Visualisasi Hasil**:
   - **Radar Chart**: Diagram 6-sumbu untuk membandingkan kemampuan
   - **Strength Profile Card**: Kartu deskripsi profil kekuatan
   - **Development Roadmap**: Peta jalan pengembangan kemampuan

### ✅ Langkah Selanjutnya:
1. Saya bisa siapkan:
   - [ ] Versi Google Forms/Typeform siap pakai
   - [ ] Template laporan PDF otomatis
   - [ ] Sheet analisis Excel untuk interpretasi manual

2. Komponen berikutnya:
   - [ ] **Personality Assessment**: Model kepribadian adaptif
   - [ ] **Experience Inventory**: Dokumentasi pembelajaran hidup

Apakah Anda ingin menyesuaikan kategori atau pertanyaan tertentu sebelum melanjutkan?