### 🎯 Adapta<PERSON> (5 Core Strengths)
Berdasarkan peran dalam Efesus 4:11-12, saya ubah menjadi:

1. **<PERSON>sul** → **Visionary Pioneers**  
   (Perebut Peluang & Pembuka Jalan Baru)
2. **Nabi** → **Insightful Truth-Seekers**  
   (<PERSON><PERSON><PERSON> & Prinsip)
3. **<PERSON><PERSON>berita <PERSON>** → **Inspiring Connectors**  
   (Inspirator & Juru <PERSON>)
4. **Gembala** → **Supportive Nurturers**  
   (Pendukung & Pengembang)
5. **Pengajar** → **Clarifying Mentors**  
   (Penerang & Pemapar)

---

### 📂 File JSON: 50 Pertanyaan Strength Assessment
```json
{
  "assessment_name": "Core Strength Finder",
  "description": "Asesmen untuk mengidentifikasi 5 kekuatan inti berdasarkan pola motivasi dan kontribusi alami",
  "scoring_scale": "1-5 (1=Sangat Tidak Setuju, 5=Sangat Setuju)",
  "categories": [
    {
      "name": "Visionary Pioneers",
      "description": "Kemampuan melihat peluang baru dan memulai tero<PERSON>an",
      "questions": [
        {"id": "VP1", "question": "Saya mudah melihat potensi di tengah ketidakpastian"},
        {"id": "VP2", "question": "Memulai proyek/produk baru memberi saya energi besar"},
        {"id": "VP3", "question": "Saya nyaman mengambil risiko untuk ide yang saya yakini"},
        {"id": "VP4", "question": "Saya senang membangun sistem dari nol"},
        {"id": "VP5", "question": "Membuka jalur baru lebih menarik daripada mengelola yang ada"},
        {"id": "VP6", "question": "Saya percaya diri memasuki bidang yang belum saya kuasai"},
        {"id": "VP7", "question": "Saya sering menjadi orang pertama mencoba metode baru"},
        {"id": "VP8", "question": "Menggagas perubahan besar membuat saya bersemangat"},
        {"id": "VP9", "question": "Saya melihat masalah sebagai peluang tersembunyi"},
        {"id": "VP10", "question": "Saya termotivasi menciptakan sesuatu yang belum ada sebelumnya"}
      ]
    },
    {
      "name": "Insightful Truth-Seekers",
      "description": "Kemampuan menemukan inti kebenaran dan prinsip fundamental",
      "questions": [
        {"id": "ITS1", "question": "Saya selalu mencari akar penyebab di balik permukaan"},
        {"id": "ITS2", "question": "Mempertanyakan asumsi adalah kebiasaan alami saya"},
        {"id": "ITS3", "question": "Saya merasa bertanggung jawab menyampaikan kebenaran yang sulit"},
        {"id": "ITS4", "question": "Prinsip moral lebih penting daripada popularitas"},
        {"id": "ITS5", "question": "Saya melihat pola yang tidak dilihat orang lain"},
        {"id": "ITS6", "question": "Mengoreksi ketidakadilan adalah dorongan hati saya"},
        {"id": "ITS7", "question": "Saya nyaman bekerja dengan konsep abstrak dan filosofis"},
        {"id": "ITS8", "question": "Integritas adalah nilai non-negosiable bagi saya"},
        {"id": "ITS9", "question": "Saya sering menjadi suara penyeimbang dalam diskusi"},
        {"id": "ITS10", "question": "Kedalaman analisis lebih penting daripada kecepatan penyelesaian"}
      ]
    },
    {
      "name": "Inspiring Connectors",
      "description": "Kemampuan menghubungkan orang dengan ide dan peluang",
      "questions": [
        {"id": "IC1", "question": "Saya mudah membuat orang antusias dengan visi"},
        {"id": "IC2", "question": "Memperkenalkan orang dengan kesamaan minat adalah keahlian saya"},
        {"id": "IC3", "question": "Saya senang menjadi jembatan antar kelompok berbeda"},
        {"id": "IC4", "question": "Bercerita adalah cara alami saya menyampaikan pesan"},
        {"id": "IC5", "question": "Saya percaya setiap pertemuan adalah peluang kolaborasi"},
        {"id": "IC6", "question": "Energi saya bertambah saat bertemu orang baru"},
        {"id": "IC7", "question": "Saya pandai menyederhanakan ide kompleks untuk pemula"},
        {"id": "IC8", "question": "Melihat orang "terhubung" memberi saya kepuasan"},
        {"id": "IC9", "question": "Saya menggunakan analogi untuk membuat konsep mudah dipahami"},
        {"id": "IC10", "question": "Jaringan luas saya adalah aset berharga"}
      ]
    },
    {
      "name": "Supportive Nurturers",
      "description": "Kemampuan mendukung pertumbuhan orang lain",
      "questions": [
        {"id": "SN1", "question": "Saya secara alami merasakan kebutuhan emosional orang"},
        {"id": "SN2", "question": "Melihat orang berkembang memberi saya makna mendalam"},
        {"id": "SN3", "question": "Saya sabar mendampingi proses belajar orang lain"},
        {"id": "SN4", "question": "Menciptakan lingkungan aman adalah prioritas saya"},
        {"id": "SN5", "question": "Saya sering menjadi tempat curhat pertama"},
        {"id": "SN6", "question": "Perubahan kecil pada orang lebih berarti daripada pencapaian besar"},
        {"id": "SN7", "question": "Saya rela mengorbankan waktu untuk orang yang membutuhkan"},
        {"id": "SN8", "question": "Kekuatan saya ada pada kesabaran dan konsistensi"},
        {"id": "SN9", "question": "Saya percaya setiap orang punya potensi unik untuk dibantu"},
        {"id": "SN10", "question": "Kehadiran saya sering menenangkan orang yang stres"}
      ]
    },
    {
      "name": "Clarifying Mentors",
      "description": "Kemampuan menjelaskan konsep kompleks secara sistematis",
      "questions": [
        {"id": "CM1", "question": "Saya senang memecah informasi kompleks menjadi langkah-langkah"},
        {"id": "CM2", "question": "Membuat kerangka pembelajaran adalah keahlian saya"},
        {"id": "CM3", "question": "Saya menikmati proses menyusun materi edukasi"},
        {"id": "CM4", "question": "Pertanyaan "mengapa?" selalu muncul dalam pikiran saya"},
        {"id": "CM5", "question": "Saya percaya pengetahuan harus disampaikan secara akurat"},
        {"id": "CM6", "question": "Melihat "aha moment" orang adalah hadiah terbesar"},
        {"id": "CM7", "question": "Saya sistematis dalam menyusun argumen"},
        {"id": "CM8", "question": "Detail penting tidak boleh diabaikan dalam penyampaian"},
        {"id": "CM9", "question": "Saya selalu mencari metode pengajaran lebih efektif"},
        {"id": "CM10", "question": "Pengetahuan hanya berguna jika bisa dipraktikkan"}
      ]
    }
  ],
  "interpretation_guide": "Skor per kategori: 10-20 (Rendah), 21-35 (Moderat), 36-50 (Tinggi). Fokus pada 2 skor tertinggi sebagai core strength utama."
}
```

### 📊 Cara Menggunakan:
1. **Skoring**:  
   Setiap jawaban diskor 1-5 (Sangat Tidak Setuju → Sangat Setuju)  
   Contoh: Jika jawaban "Sangat Setuju" (5) untuk VP1, maka tambahkan 5 ke skor "Visionary Pioneers"

2. **Analisis Hasil**:  
   - Jumlahkan skor per kategori (maks 50 per kategori)  
   - Dua skor tertinggi menunjukkan **core strength dominan**  
   - Gunakan pola "Strength Pair" untuk insight:  
     *Visionary + Insightful* = Inovator Sistem  
     *Connector + Nurturer* = Pengembang Komunitas  
     *Mentor + Truth-Seeker* = Ahli Standar Profesional  

3. **Tips Implementasi**:  
   - Tambahkan pertanyaan acak untuk hindari bias jawaban  
   - Berikan konteks: "Pilih berdasarkan kecenderungan alami, bukan harapan"  
   - Sertakan opsi "Tidak Berlaku" jika perlu  

