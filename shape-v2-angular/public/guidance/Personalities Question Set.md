Be<PERSON>ut adalah **Personality Assessment** lengkap dengan 24 pertanyaan (6 pertanyaan per dimensi) dalam format JSON beserta logika pemrosesan terperinci:

```json
{
  "assessment_name": "Personality Dynamics Assessment",
  "description": "Asesmen gaya kepribadian dalam 4 dimensi perilaku utama",
  "instructions": "<PERSON><PERSON>h seberapa sering pernyataan berikut menggambarkan diri Anda (1 = Jarang, 5 = Sering)",
  "dimensions": [
    {
      "name": "Visionary Driver",
      "code": "VD",
      "icon": "🚀",
      "traits": ["Berorientasi hasil", "Tegas", "Pengambil risiko"],
      "questions": [
        {"id": "VD1", "question": "Saya mengambil inisiatif untuk memulai proyek baru"},
        {"id": "VD2", "question": "Saya merasa nyaman membuat keputusan cepat"},
        {"id": "VD3", "question": "Target yang menantang membuat saya bersemangat"},
        {"id": "VD4", "question": "Saya lebih suka memimpin daripada mengikuti"},
        {"id": "VD5", "question": "Saya tidak takut menghadapi konflik jika diperlukan"},
        {"id": "VD6", "question": "Saya fokus pada gambaran besar daripada detail kecil"}
      ]
    },
    {
      "name": "Dynamic Connector",
      "code": "DC",
      "icon": "🤝",
      "traits": ["Persuasif", "Sosial", "Optimis"],
      "questions": [
        {"id": "DC1", "question": "Saya mudah memulai percakapan dengan orang baru"},
        {"id": "DC2", "question": "Saya senang mempresentasikan ide di depan kelompok"},
        {"id": "DC3", "question": "Membangun jaringan profesional adalah keahlian saya"},
        {"id": "DC4", "question": "Saya menggunakan cerita untuk membuat poin penting"},
        {"id": "DC5", "question": "Saya bisa meyakinkan orang tentang pendapat saya"},
        {"id": "DC6", "question": "Saya menjaga suasana tetap positif dalam tim"}
      ]
    },
    {
      "name": "Steady Supporter",
      "code": "SS",
      "icon": "🌳",
      "traits": ["Kolaboratif", "Sabat", "Stabil"],
      "questions": [
        {"id": "SS1", "question": "Saya lebih memilih kerja tim daripada bekerja sendiri"},
        {"id": "SS2", "question": "Mendengarkan dengan empati adalah kekuatan saya"},
        {"id": "SS3", "question": "Saya menghindari konfrontasi langsung"},
        {"id": "SS4", "question": "Konsistensi lebih penting daripada perubahan cepat"},
        {"id": "SS5", "question": "Saya merasa nyaman dengan tugas rutin"},
        {"id": "SS6", "question": "Membantu orang lain adalah prioritas saya"}
      ]
    },
    {
      "name": "Precision Analyst",
      "code": "PA",
      "icon": "🔍",
      "traits": ["Sistematis", "Akurat", "Analitis"],
      "questions": [
        {"id": "PA1", "question": "Saya selalu memeriksa detail dengan cermat"},
        {"id": "PA2", "question": "Prosedur yang jelas penting bagi saya"},
        {"id": "PA3", "question": "Saya lebih percaya data daripada perasaan"},
        {"id": "PA4", "question": "Kesalahan kecil bisa mengganggu saya"},
        {"id": "PA5", "question": "Saya menyukai struktur dan kerangka kerja"},
        {"id": "PA6", "question": "Saya menganalisis masalah sebelum bertindak"}
      ]
    }
  ],
  "scoring": {
    "scale": {
      "1": "Jarang",
      "2": "Kadang-kadang",
      "3": "Netral",
      "4": "Sering",
      "5": "Sangat Sering"
    },
    "method": "dimension_sum",
    "max_score_per_dimension": 30,
    "interpretation_rules": {
      "primary_dimension": "Skor tertinggi (≥ 24)",
      "secondary_dimension": "Skor > 80% dari skor tertinggi",
      "development_dimension": "Skor terendah"
    },
    "profile_matrix": {
      "VD": {
        "strengths": ["Pengambilan keputusan cepat", "Kepemimpinan", "Inisiatif"],
        "challenges": ["Kesabaran", "Mendengarkan", "Detail"],
        "communication": "Jelas, langsung, fokus pada hasil"
      },
      "DC": {
        "strengths": ["Membangun hubungan", "Persuasi", "Energi positif"],
        "challenges": ["Fokus", "Detail", "Mengatakan tidak"],
        "communication": "Ramah, dengan cerita dan contoh"
      },
      "SS": {
        "strengths": ["Kesabaran", "Kolaborasi", "Keandalan"],
        "challenges": ["Keputusan cepat", "Konfrontasi", "Perubahan"],
        "communication": "Harmonis, tunjukkan dampak sosial"
      },
      "PA": {
        "strengths": ["Akurasi", "Analisis", "Perencanaan"],
        "challenges": ["Fleksibilitas", "Ketidakpastian", "Hubungan"],
        "communication": "Logis, dengan data dan bukti"
      },
      "combinations": [
        {
          "pair": ["VD", "PA"],
          "profile": "Strategist",
          "description": "Visioner dengan pendekatan analitis yang kuat"
        },
        {
          "pair": ["VD", "DC"],
          "profile": "Innovator",
          "description": "Pemimpin karismatik yang menyukai terobosan"
        },
        {
          "pair": ["DC", "SS"],
          "profile": "Relationship Builder",
          "description": "Ahli membangun jaringan dan menjaga keharmonisan"
        },
        {
          "pair": ["SS", "PA"],
          "profile": "Stabilizer",
          "description": "Spesialis yang menjaga kualitas dan konsistensi"
        }
      ]
    }
  },
  "processing_script": "function calculatePersonalityProfile(responses) {\n  // 1. Hitung skor per dimensi\n  const dimensionScores = {\n    'VD': 0,\n    'DC': 0,\n    'SS': 0,\n    'PA': 0\n  };\n  \n  responses.forEach(response => {\n    const prefix = response.questionId.substring(0,2);\n    if (dimensionScores.hasOwnProperty(prefix)) {\n      dimensionScores[prefix] += response.score;\n    }\n  });\n  \n  // 2. Konversi ke persentase\n  const maxScore = 30;\n  const percentageScores = {};\n  for (const [dim, score] of Object.entries(dimensionScores)) {\n    percentageScores[dim] = Math.round((score / maxScore) * 100);\n  }\n  \n  // 3. Identifikasi dimensi utama\n  const sortedScores = Object.entries(dimensionScores).sort((a, b) => b[1] - a[1]);\n  const primary = sortedScores[0];\n  \n  // 4. Identifikasi dimensi sekunder\n  const secondaryThreshold = primary[1] * 0.8;\n  const secondary = sortedScores.filter(\n    ([dim, score]) => dim !== primary[0] && score >= secondaryThreshold\n  ).map(([dim]) => dim);\n  \n  // 5. Identifikasi dimensi pengembangan\n  const development = sortedScores[sortedScores.length - 1][0];\n  \n  // 6. Temukan kombinasi profil\n  let profile = 'Balanced';\n  let profileDesc = 'Keseimbangan alami di semua dimensi';\n  \n  if (primary[1] >= 24 && secondary.length > 0) {\n    const combo = [primary[0], ...secondary].sort().join('+');\n    \n    for (const combination of scoring.profile_matrix.combinations) {\n      const comboKey = combination.pair.sort().join('+');\n      if (combo.includes(comboKey)) {\n        profile = combination.profile;\n        profileDesc = combination.description;\n        break;\n      }\n    }\n  }\n  \n  // 7. Siapkan hasil\n  const dimensionNames = {\n    'VD': 'Visionary Driver',\n    'DC': 'Dynamic Connector',\n    'SS': 'Steady Supporter',\n    'PA': 'Precision Analyst'\n  };\n  \n  return {\n    scores: dimensionScores,\n    percentages: percentageScores,\n    primary_dimension: {\n      code: primary[0],\n      name: dimensionNames[primary[0]],\n      score: primary[1]\n    },\n    secondary_dimensions: secondary.map(dim => ({\n      code: dim,\n      name: dimensionNames[dim],\n      score: dimensionScores[dim]\n    })),\n    development_dimension: {\n      code: development,\n      name: dimensionNames[development],\n      score: dimensionScores[development]\n    },\n    profile: {\n      name: profile,\n      description: profileDesc,\n      strengths: scoring.profile_matrix[primary[0]].strengths,\n      challenges: scoring.profile_matrix[primary[0]].challenges,\n      communication_tips: scoring.profile_matrix[primary[0]].communication\n    }\n  };\n}"
}
```

### 🧠 Logika Pemrosesan & Skoring

#### Langkah 1: Hitung Skor Dimensi
```javascript
// Contoh respon pengguna
const responses = [
  {questionId: "VD1", score: 4},
  {questionId: "VD2", score: 5},
  // ... semua jawaban
];

// Hitung total skor per dimensi
const dimensionScores = {
  VD: 22,
  DC: 18,
  SS: 26,
  PA: 15
};
```

#### Langkah 2: Normalisasi ke Persentase
```javascript
// Konversi ke persentase (skala 0-100%)
const percentageScores = {
  VD: 73,  // (22/30)*100
  DC: 60,
  SS: 87,
  PA: 50
};
```

#### Langkah 3: Identifikasi Dimensi Utama
- Skor tertinggi: **SS (Steady Supporter)** dengan 26 poin
- Kriteria: ≥24 poin → Dimensi utama valid

#### Langkah 4: Identifikasi Dimensi Sekunder
- Ambang batas: 80% dari skor tertinggi (26 × 0.8 = 20.8)
- Dimensi di atas ambang batas: VD (22) → Dimensi sekunder

#### Langkah 5: Identifikasi Dimensi Pengembangan
- Skor terendah: **PA (Precision Analyst)** dengan 15 poin

#### Langkah 6: Tentukan Profil Kombinasi
- Kombinasi: SS + VD
- Profil yang cocok: **"Stabilizer"** (Steady Supporter + Visionary Driver)

#### Langkah 7: Hasil Akhir
```json
{
  "scores": {"VD":22, "DC":18, "SS":26, "PA":15},
  "percentages": {"VD":73, "DC":60, "SS":87, "PA":50},
  "primary_dimension": {
    "code": "SS",
    "name": "Steady Supporter",
    "score": 26
  },
  "secondary_dimensions": [
    {"code": "VD", "name": "Visionary Driver", "score": 22}
  ],
  "development_dimension": {
    "code": "PA",
    "name": "Precision Analyst",
    "score": 15
  },
  "profile": {
    "name": "Stabilizer",
    "description": "Spesialis yang menjaga kualitas dan konsistensi",
    "strengths": ["Kesabaran", "Kolaborasi", "Keandalan"],
    "challenges": ["Keputusan cepat", "Konfrontasi", "Perubahan"],
    "communication_tips": "Harmonis, tunjukkan dampak sosial"
  }
}
```

### 📊 Visualisasi Hasil
```mermaid
radarChart
    title Profil Kepribadian (%)
    axis Visionary Driver, Dynamic Connector, Steady Supporter, Precision Analyst
    series 73, 60, 87, 50
```

### 📝 Panduan Interpretasi

#### 1. Profil Utama
| Dimensi           | Kekuatan Kunci                     | Area Pengembangan         |
|-------------------|------------------------------------|---------------------------|
| **Visionary Driver** | Keputusan cepat, kepemimpinan     | Mendengarkan, kesabaran   |
| **Dynamic Connector**| Jaringan sosial, persuasi         | Fokus, detail             |
| **Steady Supporter** | Kolaborasi, keandalan             | Pengambilan risiko        |
| **Precision Analyst**| Analisis, akurasi                 | Fleksibilitas, hubungan   |

#### 2. Kombinasi Profil
| Kombinasi              | Profil              | Karakteristik                          |
|------------------------|---------------------|----------------------------------------|
| VD + PA                | Strategist          | Visioner analitis, fokus solusi        |
| VD + DC                | Innovator           | Pemimpin inspiratif, suka terobosan    |
| DC + SS                | Relationship Builder| Juru damai, ahli kolaborasi            |
| SS + PA                | Stabilizer          | Penjaga kualitas, konsisten            |

#### 3. Tips Komunikasi
| Profil              | Cara Berkomunikasi Efektif               |
|---------------------|------------------------------------------|
| Visionary Driver    | Gunakan poin-poin singkat, fokus pada hasil |
| Dynamic Connector   | Sertakan cerita, buat interaksi menyenangkan |
| Steady Supporter    | Dengarkan aktif, tunjukkan empati        |
| Precision Analyst   | Sajikan data, struktur jelas, hindari emosi |

### 💡 Contoh Rekomendasi Pengembangan

**Untuk Stabilizer (SS+VD):**
```markdown
1. **Manajemen Perubahan**  
   - Ikuti workshop "Adapting to Change"  
   - Coba satu metode baru setiap bulan  

2. **Pengambilan Keputusan Cepat**  
   - Gunakan timer 5 menit untuk keputusan rutin  
   - Latihan "trust your gut feeling"  

3. **Penyampaian Pendapat**  
   - Mulai rapat dengan menyampaikan satu pendapat  
   - Ikuti kursus komunikasi asertif  
```

### ✅ Implementasi
1. **Frontend**:
   - Tampilan pertanyaan dengan skala radio 1-5
   - Progress bar penyelesaian asesmen
   - Visualisasi radar chart interaktif

2. **Backend Processing**:
```javascript
function processAssessment(responses) {
  // Implementasi fungsi calculatePersonalityProfile
  return calculatePersonalityProfile(responses);
}
```

3. **Laporan PDF**:
   - Halaman 1: Ringkasan profil dan diagram radar
   - Halaman 2: Analisis kekuatan & area pengembangan
   - Halaman 3: Rekomendasi pengembangan spesifik

Apakah Anda ingin saya:
1. Buatkan versi Google Forms/Typeform siap pakai?
2. Siapkan template laporan PDF otomatis?
3. Kembangkan komponen Experience terakhir?
4. Atau integrasikan semua komponen SHAPE menjadi satu sistem lengkap?