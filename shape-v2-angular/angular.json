{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"shape-v2-angular": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"outputPath": "dist/shape-v2-angular", "customWebpackConfig": {"path": "./extra-webpack.config.js", "mergeStrategies": {"module.rules": "append"}}, "main": "src/main.ts", "index": "src/index.html", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/assets", {"glob": "**/*", "input": "public", "output": "/"}], "styles": ["src/styles.scss"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "configurations": {"production": {"buildTarget": "shape-v2-angular:build:production"}, "development": {"buildTarget": "shape-v2-angular:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/assets", {"glob": "**/*", "input": "public", "output": "/"}], "styles": ["src/styles.scss"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["angular-eslint"]}}