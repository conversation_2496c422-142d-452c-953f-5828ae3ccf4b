{"name": "shape-v2-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "./ng-wrapper.sh serve", "build": "./ng-wrapper.sh build", "watch": "./ng-wrapper.sh build --watch --configuration development", "test": "./ng-wrapper.sh test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/cdk": "^20.0.3", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/router": "^20.0.0", "@react-pdf/renderer": "^4.3.0", "chart.js": "^4.5.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "ng2-charts": "^8.0.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^20.0.0", "@angular/build": "^20.0.3", "@angular/cli": "^20.0.3", "@angular/compiler-cli": "^20.0.0", "@babel/preset-react": "^7.27.1", "@types/file-saver": "^2.0.7", "@types/jasmine": "~5.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.2"}}