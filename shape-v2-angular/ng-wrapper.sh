#!/bin/bash

# This script ensures that the correct Node.js version is used
# when running Angular CLI commands

# Check if nvm is installed
if [ -f "$HOME/.nvm/nvm.sh" ]; then
  source "$HOME/.nvm/nvm.sh"
  
  # Use the version specified in .nvmrc or fallback to v20.19.3
  if [ -f ".nvmrc" ]; then
    nvm use
  else
    nvm use v20.19.3
  fi
fi

# Run the ng command with all arguments passed to this script
./node_modules/.bin/ng "$@"
