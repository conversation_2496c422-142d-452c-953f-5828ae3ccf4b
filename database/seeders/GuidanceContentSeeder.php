<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GuidanceContent;

class GuidanceContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Clear existing data
        GuidanceContent::truncate();

        // Seed Strength profiles
        $this->seedStrengthProfiles();
        
        // Seed Heart profiles
        $this->seedHeartProfiles();
        
        // Seed Abilities profiles
        $this->seedAbilitiesProfiles();
        
        // Seed Personality profiles
        $this->seedPersonalityProfiles();
        
        // Seed Experience profiles
        $this->seedExperienceProfiles();
    }

    private function seedStrengthProfiles()
    {
        $strengthProfiles = [
            [
                'area' => 'strength',
                'profile_name' => 'Visionary Pioneers',
                'profile_code' => 'VP',
                'description' => 'Adaptasi dari "Rasul" - Pola pikir futuristik & berorientasi pada kemungkinan baru',
                'characteristics' => [
                    'Pola pikir futuristik & berorientasi pada kemungkinan baru',
                    'Berani mengambil risiko terukur',
                    'Tidak nyaman dengan rutinitas/status quo'
                ],
                'communication_style' => [
                    'bahasa' => ['Bagaimana jika...', 'Bayangkan ketika...', 'Potensi besar di sini!'],
                    'media' => ['Peta konsep', 'sketsa visi', 'presentasi inspiratif'],
                    'pola' => 'Lompatan kreatif, fokus pada gambaran besar (bukan detail)'
                ],
                'strengths' => [
                    'Membuka pasar/jalur baru',
                    'Mengenali peluang sebelum orang lain',
                    'Memobilisasi tim menuju perubahan'
                ],
                'weaknesses' => [
                    'Mengabaikan detail implementasi',
                    'Tidak sabar dengan proses bertahap',
                    'Risiko over-commitment pada banyak proyek'
                ],
                'team_roles' => [
                    'Inisiator Proyek' => 'Merancang terobosan baru',
                    'Strategic Planner' => 'Memetakan skenario 5-10 tahun ke depan',
                    'Change Agent' => 'Memimpin transformasi organisasi'
                ],
                'development_tips' => [
                    'Berpasangan dengan eksekutor detail',
                    'Gunakan framework MVP (Minimum Viable Product)',
                    'Latih keterampilan risk assessment'
                ],
                'superpower' => 'You naturally see possibilities where others see dead ends.',
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'strength',
                'profile_name' => 'Insightful Truth-Seekers',
                'profile_code' => 'ITS',
                'description' => 'Adaptasi dari "Nabi" - Kepekaan tinggi pada ketidaksesuaian/prinsip',
                'characteristics' => [
                    'Kepekaan tinggi pada ketidaksesuaian/prinsip',
                    'Dorongan kuat untuk integritas & keadilan',
                    'Analisis akar masalah yang tajam'
                ],
                'communication_style' => [
                    'bahasa' => ['Prinsip dasarnya...', 'Ini melanggar konsistensi...', 'Akar masalahnya di sini'],
                    'media' => ['Diagram sebab-akibat', 'data kualitatif'],
                    'pola' => 'Bertanya sampai ke dasar, menantang asumsi'
                ],
                'strengths' => [
                    'Mencegah tim dari keputusan gegabah',
                    'Membongkar bias tersembunyi',
                    'Penjaga standar etika/kualitas'
                ],
                'weaknesses' => [
                    'Dianggap terlalu kritis/negatif',
                    'Kesulitan berkompromi pada nilai inti',
                    'Terlalu fokus pada masalah daripada solusi'
                ],
                'team_roles' => [
                    'Ethical Auditor' => 'Memastikan keselarasan nilai',
                    'Quality Controller' => 'Menjaga standar ekselen',
                    'Research Analyst' => 'Mengungkap pola tersembunyi'
                ],
                'development_tips' => [
                    'Sampaikan kritik dengan solusi alternatif',
                    'Pelajari teknik feedforward',
                    'Alokasikan waktu untuk "creative problem-solving"'
                ],
                'superpower' => 'You naturally uncover hidden truths and maintain integrity.',
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($strengthProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedHeartProfiles()
    {
        $heartProfiles = [
            [
                'area' => 'heart',
                'profile_name' => 'Art & Entertainment',
                'profile_code' => 'AE',
                'description' => 'Memandang dunia sebagai palet emosi yang hidup',
                'characteristics' => [
                    'Memandang dunia sebagai palet emosi yang hidup',
                    'Mengubah rutinitas menjadi pertunjukan',
                    'Mengkomunikasikan yang tak terucap melalui metafora'
                ],
                'creative_ideas' => [
                    'Proyek Seni Urban Guerilla: Membuat instalasi seni tak terduga di ruang publik',
                    'Pertunjukan Realitas Campuran: Menggabungkan teater fisik dengan proyeksi AR',
                    'Kuliner Emosional: Menciptakan menu berdasarkan mood pengunjung'
                ],
                'hidden_strengths' => [
                    'Kemampuan menyembuhkan melalui "terapi kejut estetika" - menggunakan keindahan tak terduga untuk memutus spiral pikiran negatif'
                ],
                'love_language' => [
                    'Waktu Tanpa Batas: Ruang untuk proses kreatif tanpa deadline',
                    'Apresiasi Tanpa Syarat: Pengakuan atas ekspresi bukan hasil akhir',
                    'Kebebasan Bereksperimen: Akses ke berbagai medium tanpa judgment'
                ],
                'inspirational_figures' => [
                    'Puck (dari Midsummer Night\'s Dream): Ahli transformasi realitas',
                    'Baron Munchausen: Pencipta kisah fantastis dari kehidupan biasa',
                    'Scheherazade: Seni bercerita sebagai alat bertahan hidup'
                ],
                'warnings' => [
                    'Risiko "Keracunan Keindahan" - kehilangan kemampuan melihat nilai dalam yang biasa-biasa saja'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'heart',
                'profile_name' => 'Business',
                'profile_code' => 'BUS',
                'description' => 'Melihat masalah sebagai model bisnis yang belum lahir',
                'characteristics' => [
                    'Melihat masalah sebagai model bisnis yang belum lahir',
                    'Memandang hubungan manusia sebagai jaringan nilai',
                    'Mengubah sumber daya terbatas menjadi ekosistem berlimpah'
                ],
                'creative_ideas' => [
                    'Perpustakaan Alat: Sistem berbagi alat profesional dengan model "bayar sesuai hasil"',
                    'Pasar Mimpi: Platform jual-beli ide mentah sebelum eksekusi',
                    'Bisnis Regeneratif: Perusahaan yang didesain untuk mati setelah misi selesai'
                ],
                'hidden_strengths' => [
                    'Kemampuan menciptakan "virus kemakmuran" - sistem yang menyebarkan kesejahteraan melalui interaksi alami'
                ],
                'love_language' => [
                    'Bukti Nyata: Data dampak riil bukan janji',
                    'Percobaan Terkendali: Ruang untuk gagal kecil yang terukur',
                    'Jaringan Tak Terduga: Perkenalan dengan orang di luar bubble'
                ],
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($heartProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedAbilitiesProfiles()
    {
        // Implementation for abilities profiles
        // This will be expanded based on the Abilities.md content
    }

    private function seedPersonalityProfiles()
    {
        // Implementation for personality profiles
        // This will be expanded based on the Personality.md content
    }

    private function seedExperienceProfiles()
    {
        // Implementation for experience profiles
        // This will be expanded based on the Experience.md content
    }
}
