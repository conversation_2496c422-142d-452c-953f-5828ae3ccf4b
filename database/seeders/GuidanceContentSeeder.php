<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GuidanceContent;

class GuidanceContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Clear existing data
        GuidanceContent::truncate();

        // Seed Strength profiles
        $this->seedStrengthProfiles();

        // Seed Heart profiles
        $this->seedHeartProfiles();

        // Seed Abilities profiles
        $this->seedAbilitiesProfiles();

        // Seed Personality profiles
        $this->seedPersonalityProfiles();

        // Seed Experience profiles
        $this->seedExperienceProfiles();
    }

    private function seedStrengthProfiles()
    {
        $strengthProfiles = [
            [
                'area' => 'strength',
                'profile_name' => 'Visionary Pioneers',
                'profile_code' => 'VP',
                'description' => 'Adaptasi dari "Rasul" - Pola pikir futuristik & berorientasi pada kemungkinan baru',
                'characteristics' => [
                    'Pola pikir futuristik & berorientasi pada kemungkinan baru',
                    'Berani mengambil risiko terukur',
                    'Tidak nyaman dengan rutinitas/status quo'
                ],
                'communication_style' => [
                    'bahasa' => ['Bagaimana jika...', 'Bayangkan ketika...', 'Potensi besar di sini!'],
                    'media' => ['Peta konsep', 'sketsa visi', 'presentasi inspiratif'],
                    'pola' => 'Lompatan kreatif, fokus pada gambaran besar (bukan detail)'
                ],
                'strengths' => [
                    'Membuka pasar/jalur baru',
                    'Mengenali peluang sebelum orang lain',
                    'Memobilisasi tim menuju perubahan'
                ],
                'weaknesses' => [
                    'Mengabaikan detail implementasi',
                    'Tidak sabar dengan proses bertahap',
                    'Risiko over-commitment pada banyak proyek'
                ],
                'team_roles' => [
                    'Inisiator Proyek' => 'Merancang terobosan baru',
                    'Strategic Planner' => 'Memetakan skenario 5-10 tahun ke depan',
                    'Change Agent' => 'Memimpin transformasi organisasi'
                ],
                'development_tips' => [
                    'Berpasangan dengan eksekutor detail',
                    'Gunakan framework MVP (Minimum Viable Product)',
                    'Latih keterampilan risk assessment'
                ],
                'superpower' => 'You naturally see possibilities where others see dead ends.',
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'strength',
                'profile_name' => 'Insightful Truth-Seekers',
                'profile_code' => 'ITS',
                'description' => 'Adaptasi dari "Nabi" - Kepekaan tinggi pada ketidaksesuaian/prinsip',
                'characteristics' => [
                    'Kepekaan tinggi pada ketidaksesuaian/prinsip',
                    'Dorongan kuat untuk integritas & keadilan',
                    'Analisis akar masalah yang tajam'
                ],
                'communication_style' => [
                    'bahasa' => ['Prinsip dasarnya...', 'Ini melanggar konsistensi...', 'Akar masalahnya di sini'],
                    'media' => ['Diagram sebab-akibat', 'data kualitatif'],
                    'pola' => 'Bertanya sampai ke dasar, menantang asumsi'
                ],
                'strengths' => [
                    'Mencegah tim dari keputusan gegabah',
                    'Membongkar bias tersembunyi',
                    'Penjaga standar etika/kualitas'
                ],
                'weaknesses' => [
                    'Dianggap terlalu kritis/negatif',
                    'Kesulitan berkompromi pada nilai inti',
                    'Terlalu fokus pada masalah daripada solusi'
                ],
                'team_roles' => [
                    'Ethical Auditor' => 'Memastikan keselarasan nilai',
                    'Quality Controller' => 'Menjaga standar ekselen',
                    'Research Analyst' => 'Mengungkap pola tersembunyi'
                ],
                'development_tips' => [
                    'Sampaikan kritik dengan solusi alternatif',
                    'Pelajari teknik feedforward',
                    'Alokasikan waktu untuk "creative problem-solving"'
                ],
                'superpower' => 'You naturally uncover hidden truths and maintain integrity.',
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($strengthProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedHeartProfiles()
    {
        $heartProfiles = [
            [
                'area' => 'heart',
                'profile_name' => 'Art & Entertainment',
                'profile_code' => 'AE',
                'description' => 'Memandang dunia sebagai palet emosi yang hidup',
                'characteristics' => [
                    'Memandang dunia sebagai palet emosi yang hidup',
                    'Mengubah rutinitas menjadi pertunjukan',
                    'Mengkomunikasikan yang tak terucap melalui metafora'
                ],
                'creative_ideas' => [
                    'Proyek Seni Urban Guerilla: Membuat instalasi seni tak terduga di ruang publik',
                    'Pertunjukan Realitas Campuran: Menggabungkan teater fisik dengan proyeksi AR',
                    'Kuliner Emosional: Menciptakan menu berdasarkan mood pengunjung'
                ],
                'hidden_strengths' => [
                    'Kemampuan menyembuhkan melalui "terapi kejut estetika" - menggunakan keindahan tak terduga untuk memutus spiral pikiran negatif'
                ],
                'love_language' => [
                    'Waktu Tanpa Batas: Ruang untuk proses kreatif tanpa deadline',
                    'Apresiasi Tanpa Syarat: Pengakuan atas ekspresi bukan hasil akhir',
                    'Kebebasan Bereksperimen: Akses ke berbagai medium tanpa judgment'
                ],
                'inspirational_figures' => [
                    'Puck (dari Midsummer Night\'s Dream): Ahli transformasi realitas',
                    'Baron Munchausen: Pencipta kisah fantastis dari kehidupan biasa',
                    'Scheherazade: Seni bercerita sebagai alat bertahan hidup'
                ],
                'warnings' => [
                    'Risiko "Keracunan Keindahan" - kehilangan kemampuan melihat nilai dalam yang biasa-biasa saja'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'heart',
                'profile_name' => 'Business',
                'profile_code' => 'BUS',
                'description' => 'Melihat masalah sebagai model bisnis yang belum lahir',
                'characteristics' => [
                    'Melihat masalah sebagai model bisnis yang belum lahir',
                    'Memandang hubungan manusia sebagai jaringan nilai',
                    'Mengubah sumber daya terbatas menjadi ekosistem berlimpah'
                ],
                'creative_ideas' => [
                    'Perpustakaan Alat: Sistem berbagi alat profesional dengan model "bayar sesuai hasil"',
                    'Pasar Mimpi: Platform jual-beli ide mentah sebelum eksekusi',
                    'Bisnis Regeneratif: Perusahaan yang didesain untuk mati setelah misi selesai'
                ],
                'hidden_strengths' => [
                    'Kemampuan menciptakan "virus kemakmuran" - sistem yang menyebarkan kesejahteraan melalui interaksi alami'
                ],
                'love_language' => [
                    'Bukti Nyata: Data dampak riil bukan janji',
                    'Percobaan Terkendali: Ruang untuk gagal kecil yang terukur',
                    'Jaringan Tak Terduga: Perkenalan dengan orang di luar bubble'
                ],
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($heartProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedAbilitiesProfiles()
    {
        $abilitiesProfiles = [
            [
                'area' => 'abilities',
                'profile_name' => 'Cognitive Skills',
                'profile_code' => 'COG',
                'description' => 'Memandang kompleksitas sebagai puzzle yang menantang',
                'characteristics' => [
                    'Memandang kompleksitas sebagai puzzle yang menantang',
                    'Mengubah data mentah menjadi insight bermakna',
                    'Menggunakan logika sebagai kompas navigasi kehidupan'
                ],
                'creative_ideas' => [
                    'Cognitive Bootcamp: Latihan intensif pemecahan masalah lintas disiplin',
                    'Pattern Recognition Games: Kompetisi menemukan pola tersembunyi dalam data acak',
                    'Logic Escape Room: Ruang teka-teki yang hanya bisa dipecahkan dengan reasoning'
                ],
                'hidden_strengths' => [
                    'Kemampuan "meta-thinking" - berpikir tentang cara berpikir untuk mengoptimalkan proses kognitif'
                ],
                'love_language' => [
                    'Tantangan Intelektual: Masalah kompleks yang membutuhkan analisis mendalam',
                    'Data Berkualitas: Akses ke informasi akurat dan terstruktur',
                    'Waktu Refleksi: Ruang untuk memproses dan menganalisis tanpa tekanan'
                ],
                'inspirational_figures' => [
                    'Sherlock Holmes: Master deduksi dan analisis pola',
                    'Athena: Dewi kebijaksanaan dan strategi',
                    'Alan Turing: Pionir computational thinking'
                ],
                'warnings' => [
                    'Risiko "Analysis Paralysis" - terjebak dalam analisis tanpa mengambil tindakan'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'abilities',
                'profile_name' => 'Social Skills',
                'profile_code' => 'SOC',
                'description' => 'Memandang setiap interaksi sebagai peluang koneksi bermakna',
                'characteristics' => [
                    'Memandang setiap interaksi sebagai peluang koneksi bermakna',
                    'Mengubah konflik menjadi kolaborasi',
                    'Menggunakan empati sebagai jembatan antar perbedaan'
                ],
                'creative_ideas' => [
                    'Social Experiment Lab: Menguji teori psikologi sosial dalam kehidupan nyata',
                    'Conflict Resolution Theater: Drama interaktif untuk latihan mediasi',
                    'Empathy Mapping Workshop: Visualisasi perspektif berbagai stakeholder'
                ],
                'hidden_strengths' => [
                    'Kemampuan "social sensing" - merasakan dinamika kelompok dan menyesuaikan komunikasi'
                ],
                'love_language' => [
                    'Authentic Connection: Hubungan yang tulus tanpa agenda tersembunyi',
                    'Collaborative Environment: Ruang kerja yang mendorong kerjasama',
                    'Recognition of Impact: Pengakuan atas kontribusi dalam membangun hubungan'
                ],
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($abilitiesProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedPersonalityProfiles()
    {
        $personalityProfiles = [
            [
                'area' => 'personality',
                'profile_name' => 'Visionary Driver',
                'profile_code' => 'VD',
                'description' => 'Memandang masa depan sebagai kanvas kosong untuk dilukis',
                'characteristics' => [
                    'Memandang masa depan sebagai kanvas kosong untuk dilukis',
                    'Mengubah ketidakpastian menjadi peluang strategis',
                    'Menggunakan urgency sebagai bahan bakar inovasi'
                ],
                'communication_style' => [
                    'bahasa' => ['Mari kita ubah permainan', 'Bayangkan jika...', 'Kita bisa lebih baik'],
                    'gaya' => 'Inspiratif, berorientasi hasil, fokus pada gambaran besar',
                    'preferensi' => 'Diskusi strategis, brainstorming visi, planning session'
                ],
                'strengths' => [
                    'Catalyst untuk perubahan dan inovasi',
                    'Motivator yang menggerakkan tim menuju tujuan ambisius',
                    'Strategic thinker yang melihat peluang jangka panjang'
                ],
                'weaknesses' => [
                    'Patience dengan proses detail dan implementasi bertahap',
                    'Listening skills untuk mendengar perspektif yang berbeda',
                    'Delegation untuk menghindari burnout'
                ],
                'inspirational_figures' => [
                    'Elon Musk: Visionary yang mengubah industri',
                    'Steve Jobs: Driver yang obsessed dengan excellence',
                    'Oprah Winfrey: Visionary yang memberdayakan orang lain'
                ],
                'warnings' => [
                    'Risiko "Vision Fatigue" - terlalu banyak visi tanpa eksekusi yang konsisten'
                ],
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($personalityProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedExperienceProfiles()
    {
        $experienceProfiles = [
            [
                'area' => 'experience',
                'profile_name' => 'Leadership Journey',
                'profile_code' => 'LJ',
                'description' => 'Perjalanan pengembangan kemampuan memimpin dan menginspirasi',
                'characteristics' => [
                    'Pengalaman memimpin tim atau proyek',
                    'Kemampuan mengambil keputusan dalam situasi sulit',
                    'Pengalaman mengelola konflik dan membangun konsensus'
                ],
                'strengths' => [
                    'Kemampuan memotivasi dan menginspirasi orang lain',
                    'Pengalaman dalam pengambilan keputusan strategis',
                    'Kemampuan membangun dan mempertahankan tim yang solid'
                ],
                'development_tips' => [
                    'Terus kembangkan emotional intelligence',
                    'Pelajari berbagai gaya kepemimpinan',
                    'Bangun network dengan leader lain untuk sharing pengalaman'
                ],
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($experienceProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }
}
