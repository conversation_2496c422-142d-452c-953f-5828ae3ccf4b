<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GuidanceContent;

class GuidanceContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Clear existing data
        GuidanceContent::truncate();

        // Seed Strength profiles
        $this->seedStrengthProfiles();

        // Seed Heart profiles
        $this->seedHeartProfiles();

        // Seed Abilities profiles
        $this->seedAbilitiesProfiles();

        // Seed Personality profiles
        $this->seedPersonalityProfiles();

        // Seed Experience profiles
        $this->seedExperienceProfiles();
    }

    private function seedStrengthProfiles()
    {
        $strengthProfiles = [
            [
                'area' => 'strength',
                'profile_name' => 'Visionary Pioneers',
                'profile_code' => 'VP',
                'description' => 'Adaptasi dari "Rasul" - Pola pikir futuristik & berorientasi pada kemungkinan baru',
                'characteristics' => [
                    'Pola pikir futuristik & berorientasi pada kemungkinan baru',
                    'Berani mengambil risiko terukur',
                    'Tidak nyaman dengan rutinitas/status quo'
                ],
                'communication_style' => [
                    'bahasa' => ['Bagaimana jika...', 'Bayangkan ketika...', 'Potensi besar di sini!'],
                    'media' => ['Peta konsep', 'sketsa visi', 'presentasi inspiratif'],
                    'pola' => 'Lompatan kreatif, fokus pada gambaran besar (bukan detail)'
                ],
                'strengths' => [
                    'Membuka pasar/jalur baru',
                    'Mengenali peluang sebelum orang lain',
                    'Memobilisasi tim menuju perubahan'
                ],
                'weaknesses' => [
                    'Mengabaikan detail implementasi',
                    'Tidak sabar dengan proses bertahap',
                    'Risiko over-commitment pada banyak proyek'
                ],
                'team_roles' => [
                    'Inisiator Proyek' => 'Merancang terobosan baru',
                    'Strategic Planner' => 'Memetakan skenario 5-10 tahun ke depan',
                    'Change Agent' => 'Memimpin transformasi organisasi'
                ],
                'development_tips' => [
                    'Berpasangan dengan eksekutor detail',
                    'Gunakan framework MVP (Minimum Viable Product)',
                    'Latih keterampilan risk assessment'
                ],
                'superpower' => 'You naturally see possibilities where others see dead ends.',
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'strength',
                'profile_name' => 'Insightful Truth-Seekers',
                'profile_code' => 'ITS',
                'description' => 'Adaptasi dari "Nabi" - Kepekaan tinggi pada ketidaksesuaian/prinsip',
                'characteristics' => [
                    'Kepekaan tinggi pada ketidaksesuaian/prinsip',
                    'Dorongan kuat untuk integritas & keadilan',
                    'Analisis akar masalah yang tajam'
                ],
                'communication_style' => [
                    'bahasa' => ['Prinsip dasarnya...', 'Ini melanggar konsistensi...', 'Akar masalahnya di sini'],
                    'media' => ['Diagram sebab-akibat', 'data kualitatif'],
                    'pola' => 'Bertanya sampai ke dasar, menantang asumsi'
                ],
                'strengths' => [
                    'Mencegah tim dari keputusan gegabah',
                    'Membongkar bias tersembunyi',
                    'Penjaga standar etika/kualitas'
                ],
                'weaknesses' => [
                    'Dianggap terlalu kritis/negatif',
                    'Kesulitan berkompromi pada nilai inti',
                    'Terlalu fokus pada masalah daripada solusi'
                ],
                'team_roles' => [
                    'Ethical Auditor' => 'Memastikan keselarasan nilai',
                    'Quality Controller' => 'Menjaga standar ekselen',
                    'Research Analyst' => 'Mengungkap pola tersembunyi'
                ],
                'development_tips' => [
                    'Sampaikan kritik dengan solusi alternatif',
                    'Pelajari teknik feedforward',
                    'Alokasikan waktu untuk "creative problem-solving"'
                ],
                'superpower' => 'You naturally uncover hidden truths and maintain integrity.',
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'strength',
                'profile_name' => 'Community Connectors',
                'profile_code' => 'CC',
                'description' => 'Membangun jembatan antar komunitas dan menciptakan jaringan yang kuat',
                'characteristics' => [
                    'Kemampuan alami membangun hubungan',
                    'Melihat potensi kolaborasi di mana orang lain melihat perbedaan',
                    'Energi dari interaksi sosial yang bermakna'
                ],
                'communication_style' => [
                    'bahasa' => ['Kita semua ingin...', 'Ceritamu mengingatkan saya pada...', 'Mari kolaborasi!'],
                    'media' => ['Storytelling', 'testimoni', 'platform jaringan'],
                    'pola' => 'Membangun hubungan sebelum transaksi'
                ],
                'strengths' => [
                    'Membentuk aliansi strategis',
                    'Meningkatkan engagement stakeholder',
                    'Menerjemahkan ide kompleks ke bahasa awam'
                ],
                'weaknesses' => [
                    'Over-promise untuk menyenangkan orang',
                    'Menghindari konflik yang diperlukan',
                    'Kelelahan sosial jika tak ada recovery time'
                ],
                'team_roles' => [
                    'Network Builder' => 'Membangun ekosistem kemitraan',
                    'Community Manager' => 'Mengelola hubungan stakeholder',
                    'Cultural Bridge' => 'Menghubungkan kelompok yang berbeda'
                ],
                'development_tips' => [
                    'Pelajari teknik boundary setting',
                    'Kembangkan keterampilan conflict resolution',
                    'Jadwalkan waktu untuk recharge energy'
                ],
                'superpower' => 'You naturally build bridges between different communities.',
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'strength',
                'profile_name' => 'Supportive Nurturers',
                'profile_code' => 'SN',
                'description' => 'Menciptakan lingkungan yang mendukung pertumbuhan dan perkembangan orang lain',
                'characteristics' => [
                    'Kepekaan tinggi terhadap kebutuhan orang lain',
                    'Kemampuan menciptakan rasa aman psikologis',
                    'Fokus pada pengembangan potensi individu'
                ],
                'communication_style' => [
                    'bahasa' => ['Apa yang kamu butuhkan?', 'Perkembanganmu luar biasa!', 'Saya percaya padamu'],
                    'media' => ['Sesi one-on-one', 'forum support group'],
                    'pola' => 'Mendengarkan aktif sebelum menasihati'
                ],
                'strengths' => [
                    'Menciptakan lingkungan psikologis aman',
                    'Mempertahankan talenta kunci',
                    'Mendeteksi masalah dini melalui observasi'
                ],
                'weaknesses' => [
                    'Kesulitan membuat keputusan tegas',
                    'Mengabaikan kebutuhan diri sendiri',
                    'Toleransi pada underperformance'
                ],
                'team_roles' => [
                    'People Developer' => 'Mengembangkan potensi tim',
                    'Wellness Advocate' => 'Menjaga kesehatan mental tim',
                    'Conflict Mediator' => 'Menyelesaikan konflik interpersonal'
                ],
                'development_tips' => [
                    'Pelajari assertiveness training',
                    'Kembangkan keterampilan performance management',
                    'Praktikkan self-care routine'
                ],
                'superpower' => 'You naturally create environments where people thrive.',
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'strength',
                'profile_name' => 'Knowledge Mentors',
                'profile_code' => 'KM',
                'description' => 'Mengubah kompleksitas menjadi pembelajaran yang mudah dipahami',
                'characteristics' => [
                    'Kemampuan menyederhanakan konsep kompleks',
                    'Passion untuk berbagi pengetahuan',
                    'Kesabaran dalam proses pembelajaran'
                ],
                'communication_style' => [
                    'bahasa' => ['Langkah pertama adalah...', 'Analoginya seperti...', 'Inti konsepnya...'],
                    'media' => ['Modul pembelajaran', 'diagram alur', 'checklist'],
                    'pola' => 'Struktur hierarkis dari dasar ke kompleks'
                ],
                'strengths' => [
                    'Mempercepat onboarding',
                    'Mencegah kesalahan berulang',
                    'Menstandarisasi keunggulan'
                ],
                'weaknesses' => [
                    'Kaku pada metode yang sudah bekerja',
                    'Frustrasi pada ketidakpedulian belajar',
                    'Over-engineering sistem sederhana'
                ],
                'team_roles' => [
                    'Knowledge Architect' => 'Membangun sistem pengetahuan',
                    'Process Optimizer' => 'Menyederhanakan kompleksitas',
                    'Learning Facilitator' => 'Mengakselerasi kurva belajar'
                ],
                'development_tips' => [
                    'Adopsi prinsip microlearning',
                    'Kembangkan alat asesmen pemahaman',
                    'Kolaborasi dengan Connectors untuk engagement'
                ],
                'superpower' => 'You naturally transform complexity into clarity.',
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($strengthProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedHeartProfiles()
    {
        $heartProfiles = [
            [
                'area' => 'heart',
                'profile_name' => 'Art & Entertainment',
                'profile_code' => 'AE',
                'description' => 'Memandang dunia sebagai palet emosi yang hidup',
                'characteristics' => [
                    'Memandang dunia sebagai palet emosi yang hidup',
                    'Mengubah rutinitas menjadi pertunjukan',
                    'Mengkomunikasikan yang tak terucap melalui metafora'
                ],
                'creative_ideas' => [
                    'Proyek Seni Urban Guerilla: Membuat instalasi seni tak terduga di ruang publik',
                    'Pertunjukan Realitas Campuran: Menggabungkan teater fisik dengan proyeksi AR',
                    'Kuliner Emosional: Menciptakan menu berdasarkan mood pengunjung'
                ],
                'hidden_strengths' => [
                    'Kemampuan menyembuhkan melalui "terapi kejut estetika" - menggunakan keindahan tak terduga untuk memutus spiral pikiran negatif'
                ],
                'love_language' => [
                    'Waktu Tanpa Batas: Ruang untuk proses kreatif tanpa deadline',
                    'Apresiasi Tanpa Syarat: Pengakuan atas ekspresi bukan hasil akhir',
                    'Kebebasan Bereksperimen: Akses ke berbagai medium tanpa judgment'
                ],
                'inspirational_figures' => [
                    'Puck (dari Midsummer Night\'s Dream): Ahli transformasi realitas',
                    'Baron Munchausen: Pencipta kisah fantastis dari kehidupan biasa',
                    'Scheherazade: Seni bercerita sebagai alat bertahan hidup'
                ],
                'warnings' => [
                    'Risiko "Keracunan Keindahan" - kehilangan kemampuan melihat nilai dalam yang biasa-biasa saja'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'heart',
                'profile_name' => 'Business',
                'profile_code' => 'BUS',
                'description' => 'Melihat masalah sebagai model bisnis yang belum lahir',
                'characteristics' => [
                    'Melihat masalah sebagai model bisnis yang belum lahir',
                    'Memandang hubungan manusia sebagai jaringan nilai',
                    'Mengubah sumber daya terbatas menjadi ekosistem berlimpah'
                ],
                'creative_ideas' => [
                    'Perpustakaan Alat: Sistem berbagi alat profesional dengan model "bayar sesuai hasil"',
                    'Pasar Mimpi: Platform jual-beli ide mentah sebelum eksekusi',
                    'Bisnis Regeneratif: Perusahaan yang didesain untuk mati setelah misi selesai'
                ],
                'hidden_strengths' => [
                    'Kemampuan menciptakan "virus kemakmuran" - sistem yang menyebarkan kesejahteraan melalui interaksi alami'
                ],
                'love_language' => [
                    'Bukti Nyata: Data dampak riil bukan janji',
                    'Percobaan Terkendali: Ruang untuk gagal kecil yang terukur',
                    'Jaringan Tak Terduga: Perkenalan dengan orang di luar bubble'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'heart',
                'profile_name' => 'Government',
                'profile_code' => 'GOV',
                'description' => 'Memandang kebijakan sebagai alat untuk menciptakan perubahan sistemik',
                'characteristics' => [
                    'Visi untuk keadilan sosial dan kesetaraan',
                    'Kemampuan navigasi sistem birokrasi',
                    'Fokus pada dampak jangka panjang untuk masyarakat'
                ],
                'creative_ideas' => [
                    'Citizen Lab: Platform eksperimen kebijakan dengan partisipasi langsung warga',
                    'Policy Sandbox: Area uji coba regulasi baru sebelum implementasi nasional',
                    'Transparent Governance: Sistem real-time tracking implementasi kebijakan'
                ],
                'hidden_strengths' => [
                    'Kemampuan membangun konsensus di antara kelompok yang bertentangan'
                ],
                'love_language' => [
                    'Data Dampak: Bukti nyata perubahan positif di masyarakat',
                    'Partisipasi Aktif: Keterlibatan langsung warga dalam proses kebijakan',
                    'Transparansi: Akses terbuka terhadap informasi dan proses pengambilan keputusan'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'heart',
                'profile_name' => 'Education',
                'profile_code' => 'EDU',
                'description' => 'Memandang pembelajaran sebagai kunci transformasi individu dan masyarakat',
                'characteristics' => [
                    'Passion untuk pengembangan potensi manusia',
                    'Kemampuan menyesuaikan metode dengan gaya belajar',
                    'Visi jangka panjang untuk dampak pendidikan'
                ],
                'creative_ideas' => [
                    'Learning Ecosystem: Jaringan pembelajaran yang menghubungkan formal dan informal',
                    'Skill Exchange Platform: Sistem barter keterampilan antar individu',
                    'Real-world Classroom: Pembelajaran berbasis proyek nyata di komunitas'
                ],
                'hidden_strengths' => [
                    'Kemampuan melihat potensi tersembunyi dalam setiap individu'
                ],
                'love_language' => [
                    'Growth Evidence: Bukti perkembangan dan pencapaian peserta didik',
                    'Innovation Freedom: Ruang untuk bereksperimen dengan metode pembelajaran',
                    'Community Impact: Kontribusi nyata terhadap pengembangan masyarakat'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'heart',
                'profile_name' => 'Healthcare',
                'profile_code' => 'HLT',
                'description' => 'Memandang kesehatan sebagai hak fundamental dan tanggung jawab bersama',
                'characteristics' => [
                    'Empati mendalam terhadap penderitaan manusia',
                    'Fokus pada pencegahan dan wellness holistik',
                    'Kemampuan membuat keputusan di bawah tekanan'
                ],
                'creative_ideas' => [
                    'Community Health Hub: Pusat kesehatan terintegrasi dengan pendekatan holistik',
                    'Preventive Care Network: Sistem monitoring kesehatan proaktif berbasis komunitas',
                    'Healing Environment Design: Ruang yang dirancang untuk mendukung proses penyembuhan'
                ],
                'hidden_strengths' => [
                    'Kemampuan memberikan harapan dan kekuatan dalam situasi sulit'
                ],
                'love_language' => [
                    'Patient Recovery: Bukti nyata perbaikan kondisi pasien',
                    'Holistic Approach: Pendekatan menyeluruh terhadap kesehatan',
                    'Prevention Focus: Upaya mencegah penyakit sebelum terjadi'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'heart',
                'profile_name' => 'Technology',
                'profile_code' => 'TECH',
                'description' => 'Memandang teknologi sebagai alat untuk memecahkan masalah kemanusiaan',
                'characteristics' => [
                    'Visi untuk teknologi yang berpusat pada manusia',
                    'Kemampuan melihat potensi teknologi untuk kebaikan',
                    'Fokus pada aksesibilitas dan inklusivitas'
                ],
                'creative_ideas' => [
                    'Tech for Good Lab: Inkubator untuk solusi teknologi masalah sosial',
                    'Digital Inclusion Platform: Sistem untuk mengurangi kesenjangan digital',
                    'Ethical AI Framework: Kerangka kerja untuk pengembangan AI yang bertanggung jawab'
                ],
                'hidden_strengths' => [
                    'Kemampuan menerjemahkan kebutuhan manusia menjadi solusi teknologi'
                ],
                'love_language' => [
                    'User Impact: Bukti teknologi membantu kehidupan nyata',
                    'Innovation Purpose: Teknologi yang memiliki misi sosial jelas',
                    'Accessibility Focus: Solusi yang dapat diakses oleh semua kalangan'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'heart',
                'profile_name' => 'Family',
                'profile_code' => 'FAM',
                'description' => 'Memandang keluarga sebagai fondasi kekuatan dan pertumbuhan',
                'characteristics' => [
                    'Komitmen mendalam terhadap kesejahteraan keluarga',
                    'Kemampuan menciptakan lingkungan yang mendukung',
                    'Fokus pada nilai-nilai dan tradisi yang bermakna'
                ],
                'creative_ideas' => [
                    'Family Learning Journey: Program pengembangan bersama seluruh anggota keluarga',
                    'Intergenerational Wisdom Exchange: Platform berbagi pengalaman antar generasi',
                    'Family Mission Project: Proyek bersama yang mencerminkan nilai keluarga'
                ],
                'hidden_strengths' => [
                    'Kemampuan menciptakan stabilitas emosional dalam ketidakpastian'
                ],
                'love_language' => [
                    'Quality Time: Waktu berkualitas bersama tanpa gangguan',
                    'Shared Values: Keselarasan dalam prinsip dan tujuan hidup',
                    'Legacy Building: Membangun warisan yang bermakna untuk generasi berikutnya'
                ],
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($heartProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedAbilitiesProfiles()
    {
        $abilitiesProfiles = [
            [
                'area' => 'abilities',
                'profile_name' => 'Cognitive Skills',
                'profile_code' => 'COG',
                'description' => 'Memandang kompleksitas sebagai puzzle yang menantang',
                'characteristics' => [
                    'Memandang kompleksitas sebagai puzzle yang menantang',
                    'Mengubah data mentah menjadi insight bermakna',
                    'Menggunakan logika sebagai kompas navigasi kehidupan'
                ],
                'creative_ideas' => [
                    'Cognitive Bootcamp: Latihan intensif pemecahan masalah lintas disiplin',
                    'Pattern Recognition Games: Kompetisi menemukan pola tersembunyi dalam data acak',
                    'Logic Escape Room: Ruang teka-teki yang hanya bisa dipecahkan dengan reasoning'
                ],
                'hidden_strengths' => [
                    'Kemampuan "meta-thinking" - berpikir tentang cara berpikir untuk mengoptimalkan proses kognitif'
                ],
                'love_language' => [
                    'Tantangan Intelektual: Masalah kompleks yang membutuhkan analisis mendalam',
                    'Data Berkualitas: Akses ke informasi akurat dan terstruktur',
                    'Waktu Refleksi: Ruang untuk memproses dan menganalisis tanpa tekanan'
                ],
                'inspirational_figures' => [
                    'Sherlock Holmes: Master deduksi dan analisis pola',
                    'Athena: Dewi kebijaksanaan dan strategi',
                    'Alan Turing: Pionir computational thinking'
                ],
                'warnings' => [
                    'Risiko "Analysis Paralysis" - terjebak dalam analisis tanpa mengambil tindakan'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'abilities',
                'profile_name' => 'Social Skills',
                'profile_code' => 'SOC',
                'description' => 'Memandang setiap interaksi sebagai peluang koneksi bermakna',
                'characteristics' => [
                    'Memandang setiap interaksi sebagai peluang koneksi bermakna',
                    'Mengubah konflik menjadi kolaborasi',
                    'Menggunakan empati sebagai jembatan antar perbedaan'
                ],
                'creative_ideas' => [
                    'Social Experiment Lab: Menguji teori psikologi sosial dalam kehidupan nyata',
                    'Conflict Resolution Theater: Drama interaktif untuk latihan mediasi',
                    'Empathy Mapping Workshop: Visualisasi perspektif berbagai stakeholder'
                ],
                'hidden_strengths' => [
                    'Kemampuan "social sensing" - merasakan dinamika kelompok dan menyesuaikan komunikasi'
                ],
                'love_language' => [
                    'Authentic Connection: Hubungan yang tulus tanpa agenda tersembunyi',
                    'Collaborative Environment: Ruang kerja yang mendorong kerjasama',
                    'Recognition of Impact: Pengakuan atas kontribusi dalam membangun hubungan'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'abilities',
                'profile_name' => 'Technical Skills',
                'profile_code' => 'TECH',
                'description' => 'Memandang teknologi sebagai alat untuk mewujudkan ide menjadi realitas',
                'characteristics' => [
                    'Kemampuan menguasai tools dan sistem kompleks',
                    'Pendekatan sistematis dalam pemecahan masalah',
                    'Passion untuk efisiensi dan optimisasi'
                ],
                'creative_ideas' => [
                    'Tech Innovation Lab: Eksperimen dengan teknologi emerging',
                    'Automation Challenge: Kompetisi menciptakan solusi otomatis',
                    'Digital Craftsmanship: Fokus pada kualitas dan keunggulan teknis'
                ],
                'hidden_strengths' => [
                    'Kemampuan "system thinking" - melihat bagaimana komponen teknis saling berinteraksi'
                ],
                'love_language' => [
                    'Technical Excellence: Standar tinggi dalam kualitas implementasi',
                    'Innovation Freedom: Ruang untuk bereksperimen dengan teknologi baru',
                    'Problem Solving: Tantangan teknis yang membutuhkan solusi kreatif'
                ],
                'inspirational_figures' => [
                    'Linus Torvalds: Creator of Linux, master of technical leadership',
                    'Ada Lovelace: Pioneer of computer programming',
                    'Tim Berners-Lee: Inventor of the World Wide Web'
                ],
                'warnings' => [
                    'Risiko "Technology Tunnel Vision" - fokus berlebihan pada aspek teknis tanpa mempertimbangkan user experience'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'abilities',
                'profile_name' => 'Creative Skills',
                'profile_code' => 'CRE',
                'description' => 'Memandang kreativitas sebagai kekuatan untuk menciptakan solusi inovatif',
                'characteristics' => [
                    'Kemampuan berpikir di luar kotak',
                    'Mengubah keterbatasan menjadi peluang kreatif',
                    'Menggunakan imajinasi untuk memecahkan masalah'
                ],
                'creative_ideas' => [
                    'Creative Problem Solving Workshop: Teknik brainstorming dan ideation',
                    'Innovation Sprint: Sesi intensif menghasilkan solusi kreatif',
                    'Design Thinking Lab: Pendekatan human-centered dalam inovasi'
                ],
                'hidden_strengths' => [
                    'Kemampuan "creative synthesis" - menggabungkan ide-ide yang tidak terkait menjadi solusi baru'
                ],
                'love_language' => [
                    'Creative Freedom: Ruang untuk berekspresi tanpa batasan',
                    'Inspiration Sources: Akses ke berbagai stimulus kreatif',
                    'Experimentation: Kesempatan untuk mencoba pendekatan baru'
                ],
                'inspirational_figures' => [
                    'Leonardo da Vinci: Master of creative innovation',
                    'Maya Angelou: Creative expression through words',
                    'Steve Jobs: Creative vision in product design'
                ],
                'warnings' => [
                    'Risiko "Creative Chaos" - terlalu banyak ide tanpa fokus eksekusi'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'abilities',
                'profile_name' => 'Leadership Skills',
                'profile_code' => 'LEAD',
                'description' => 'Memandang kepemimpinan sebagai tanggung jawab untuk memberdayakan orang lain',
                'characteristics' => [
                    'Kemampuan menginspirasi dan memotivasi tim',
                    'Visi jelas untuk masa depan yang lebih baik',
                    'Keberanian mengambil keputusan sulit'
                ],
                'creative_ideas' => [
                    'Leadership Development Program: Mentoring dan coaching untuk leader baru',
                    'Servant Leadership Practice: Fokus pada melayani dan memberdayakan tim',
                    'Adaptive Leadership Training: Kemampuan memimpin dalam perubahan'
                ],
                'hidden_strengths' => [
                    'Kemampuan "authentic influence" - memimpin melalui integritas dan contoh nyata'
                ],
                'love_language' => [
                    'Team Success: Melihat anggota tim berkembang dan berhasil',
                    'Meaningful Impact: Kontribusi nyata terhadap tujuan yang lebih besar',
                    'Trust Building: Hubungan yang dibangun atas dasar kepercayaan'
                ],
                'inspirational_figures' => [
                    'Nelson Mandela: Leadership through reconciliation',
                    'Mahatma Gandhi: Leading by example',
                    'Oprah Winfrey: Empowering others through leadership'
                ],
                'warnings' => [
                    'Risiko "Leadership Burnout" - mengabaikan kebutuhan diri sendiri demi tim'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'abilities',
                'profile_name' => 'Communication Skills',
                'profile_code' => 'COMM',
                'description' => 'Memandang komunikasi sebagai jembatan untuk membangun pemahaman',
                'characteristics' => [
                    'Kemampuan menyampaikan ide dengan jelas',
                    'Mendengarkan aktif dan empati',
                    'Adaptasi gaya komunikasi sesuai audiens'
                ],
                'creative_ideas' => [
                    'Storytelling Workshop: Teknik bercerita yang efektif',
                    'Cross-Cultural Communication: Komunikasi lintas budaya dan generasi',
                    'Digital Communication Mastery: Komunikasi efektif di era digital'
                ],
                'hidden_strengths' => [
                    'Kemampuan "communication bridge" - menghubungkan perspektif yang berbeda'
                ],
                'love_language' => [
                    'Clear Understanding: Komunikasi yang menghasilkan pemahaman mutual',
                    'Authentic Expression: Ruang untuk komunikasi yang tulus dan jujur',
                    'Active Listening: Lingkungan yang menghargai pendengaran aktif'
                ],
                'inspirational_figures' => [
                    'Martin Luther King Jr.: Master of inspirational communication',
                    'Maya Angelou: Powerful storytelling and expression',
                    'TED Speakers: Excellence in idea communication'
                ],
                'warnings' => [
                    'Risiko "Communication Overload" - terlalu banyak berbicara tanpa mendengarkan'
                ],
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($abilitiesProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedPersonalityProfiles()
    {
        $personalityProfiles = [
            [
                'area' => 'personalities',
                'profile_name' => 'Precision Analyst',
                'profile_code' => 'PA',
                'description' => 'Memandang detail sebagai kunci untuk keunggulan dan akurasi',
                'characteristics' => [
                    'Fokus tinggi pada akurasi dan presisi',
                    'Pendekatan sistematis dalam setiap tugas',
                    'Kemampuan analisis mendalam dan teliti'
                ],
                'communication_style' => [
                    'bahasa' => ['Data menunjukkan...', 'Mari kita analisis lebih detail', 'Perlu verifikasi lebih lanjut'],
                    'gaya' => 'Faktual, terstruktur, berbasis bukti',
                    'preferensi' => 'Laporan tertulis, presentasi data, diskusi analitis'
                ],
                'strengths' => [
                    'Menghasilkan analisis yang akurat dan dapat diandalkan',
                    'Mencegah kesalahan melalui perhatian pada detail',
                    'Memberikan fondasi data yang solid untuk pengambilan keputusan'
                ],
                'weaknesses' => [
                    'Cenderung perfeksionis yang dapat memperlambat progress',
                    'Kesulitan dengan ambiguitas dan ketidakpastian',
                    'Mungkin mengabaikan aspek emosional dalam analisis'
                ],
                'inspirational_figures' => [
                    'Marie Curie: Precision in scientific research',
                    'Warren Buffett: Analytical approach to investment',
                    'Katherine Johnson: Mathematical precision and accuracy'
                ],
                'warnings' => [
                    'Risiko "Analysis Paralysis" - terjebak dalam analisis tanpa mengambil tindakan'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'personalities',
                'profile_name' => 'Steady Supporter',
                'profile_code' => 'SS',
                'description' => 'Memandang stabilitas dan dukungan sebagai fondasi kesuksesan tim',
                'characteristics' => [
                    'Konsistensi dan keandalan dalam setiap situasi',
                    'Kemampuan memberikan dukungan emosional',
                    'Fokus pada harmoni dan kestabilan kelompok'
                ],
                'communication_style' => [
                    'bahasa' => ['Bagaimana saya bisa membantu?', 'Mari kita hadapi bersama', 'Kita akan melewati ini'],
                    'gaya' => 'Supportif, tenang, empati tinggi',
                    'preferensi' => 'Percakapan personal, diskusi kelompok kecil, komunikasi yang hangat'
                ],
                'strengths' => [
                    'Menciptakan lingkungan kerja yang stabil dan mendukung',
                    'Mediator yang efektif dalam konflik',
                    'Mempertahankan moral dan motivasi tim'
                ],
                'weaknesses' => [
                    'Kesulitan menghadapi perubahan yang cepat',
                    'Menghindari konfrontasi yang diperlukan',
                    'Mungkin mengabaikan kebutuhan diri sendiri'
                ],
                'inspirational_figures' => [
                    'Mother Teresa: Unwavering support for others',
                    'Fred Rogers: Steady, caring presence',
                    'Jimmy Carter: Consistent service and support'
                ],
                'warnings' => [
                    'Risiko "Support Burnout" - memberikan dukungan berlebihan tanpa self-care'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'personalities',
                'profile_name' => 'Influential Communicator',
                'profile_code' => 'IC',
                'description' => 'Memandang komunikasi sebagai kekuatan untuk menginspirasi dan mempengaruhi',
                'characteristics' => [
                    'Karisma natural dan kemampuan persuasi',
                    'Energi tinggi dan antusiasme yang menular',
                    'Kemampuan membangun hubungan dengan cepat'
                ],
                'communication_style' => [
                    'bahasa' => ['Bayangkan kemungkinannya!', 'Mari kita wujudkan bersama', 'Ini akan luar biasa!'],
                    'gaya' => 'Inspiratif, energik, persuasif',
                    'preferensi' => 'Presentasi publik, networking events, diskusi interaktif'
                ],
                'strengths' => [
                    'Memotivasi dan menginspirasi orang lain',
                    'Membangun jaringan dan hubungan yang kuat',
                    'Mengkomunikasikan visi dengan cara yang menarik'
                ],
                'weaknesses' => [
                    'Mungkin over-promise dalam antusiasme',
                    'Kesulitan dengan detail dan follow-through',
                    'Dapat mengabaikan feedback negatif'
                ],
                'inspirational_figures' => [
                    'Tony Robbins: Master of influential communication',
                    'Oprah Winfrey: Inspiring through authentic connection',
                    'Barack Obama: Powerful and inspiring speaker'
                ],
                'warnings' => [
                    'Risiko "Influence Overload" - menggunakan pengaruh tanpa mempertimbangkan dampak jangka panjang'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'personalities',
                'profile_name' => 'Decisive Leader',
                'profile_code' => 'DL',
                'description' => 'Memandang keputusan cepat dan tindakan tegas sebagai kunci kesuksesan',
                'characteristics' => [
                    'Kemampuan mengambil keputusan dengan cepat',
                    'Orientasi hasil dan pencapaian target',
                    'Keberanian mengambil risiko dan tanggung jawab'
                ],
                'communication_style' => [
                    'bahasa' => ['Mari kita putuskan sekarang', 'Apa langkah selanjutnya?', 'Kita perlu bertindak'],
                    'gaya' => 'Langsung, tegas, berorientasi aksi',
                    'preferensi' => 'Meeting singkat, komunikasi to-the-point, decision-making session'
                ],
                'strengths' => [
                    'Menggerakkan tim menuju pencapaian tujuan',
                    'Efektif dalam situasi krisis dan tekanan',
                    'Memberikan arah yang jelas dan tegas'
                ],
                'weaknesses' => [
                    'Mungkin mengabaikan input dari orang lain',
                    'Impatience dengan proses yang lambat',
                    'Risiko burnout karena pace yang tinggi'
                ],
                'inspirational_figures' => [
                    'Winston Churchill: Decisive leadership in crisis',
                    'Margaret Thatcher: Strong, decisive leadership',
                    'Jack Welch: Results-oriented business leadership'
                ],
                'warnings' => [
                    'Risiko "Decision Fatigue" - mengambil terlalu banyak keputusan tanpa delegasi'
                ],
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($personalityProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }

    private function seedExperienceProfiles()
    {
        $experienceProfiles = [
            [
                'area' => 'experience',
                'profile_name' => 'Leadership Journey',
                'profile_code' => 'LJ',
                'description' => 'Perjalanan pengembangan kemampuan memimpin dan menginspirasi',
                'characteristics' => [
                    'Pengalaman memimpin tim atau proyek',
                    'Kemampuan mengambil keputusan dalam situasi sulit',
                    'Pengalaman mengelola konflik dan membangun konsensus'
                ],
                'strengths' => [
                    'Kemampuan memotivasi dan menginspirasi orang lain',
                    'Pengalaman dalam pengambilan keputusan strategis',
                    'Kemampuan membangun dan mempertahankan tim yang solid'
                ],
                'development_tips' => [
                    'Terus kembangkan emotional intelligence',
                    'Pelajari berbagai gaya kepemimpinan',
                    'Bangun network dengan leader lain untuk sharing pengalaman'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'experience',
                'profile_name' => 'Life Catalyst',
                'profile_code' => 'LC',
                'description' => 'Pengalaman transformatif yang mengubah perspektif dan arah hidup',
                'characteristics' => [
                    'Pengalaman mengatasi tantangan besar dalam hidup',
                    'Kemampuan mengubah adversity menjadi opportunity',
                    'Wisdom yang diperoleh dari perjalanan hidup yang unik'
                ],
                'strengths' => [
                    'Resiliensi dan kemampuan adaptasi yang tinggi',
                    'Perspektif yang mendalam tentang kehidupan',
                    'Kemampuan menginspirasi orang lain melalui pengalaman'
                ],
                'development_tips' => [
                    'Refleksikan dan dokumentasikan pembelajaran dari pengalaman',
                    'Bagikan wisdom dengan menjadi mentor bagi orang lain',
                    'Terus terbuka untuk pembelajaran dan pertumbuhan'
                ],
                'inspirational_figures' => [
                    'Nelson Mandela: Transforming adversity into leadership',
                    'Maya Angelou: Finding strength through life challenges',
                    'Viktor Frankl: Creating meaning from difficult experiences'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'experience',
                'profile_name' => 'Learning Odyssey',
                'profile_code' => 'LO',
                'description' => 'Perjalanan pembelajaran berkelanjutan dan eksplorasi pengetahuan',
                'characteristics' => [
                    'Passion untuk pembelajaran seumur hidup',
                    'Pengalaman belajar di berbagai bidang dan konteks',
                    'Kemampuan mengintegrasikan pengetahuan lintas disiplin'
                ],
                'strengths' => [
                    'Adaptabilitas tinggi terhadap perubahan',
                    'Kemampuan melihat koneksi antar bidang yang berbeda',
                    'Curiosity dan open-mindedness yang kuat'
                ],
                'development_tips' => [
                    'Tetap curious dan selalu bertanya',
                    'Eksplorasi bidang-bidang baru secara teratur',
                    'Bangun habit refleksi untuk mengintegrasikan pembelajaran'
                ],
                'inspirational_figures' => [
                    'Leonardo da Vinci: Renaissance polymath and lifelong learner',
                    'Benjamin Franklin: Continuous learning and self-improvement',
                    'Marie Curie: Pioneering research and scientific discovery'
                ],
                'min_score' => 36,
                'max_score' => 50
            ],
            [
                'area' => 'experience',
                'profile_name' => 'Career Journey',
                'profile_code' => 'CJ',
                'description' => 'Perjalanan profesional yang membentuk expertise dan kontribusi',
                'characteristics' => [
                    'Pengalaman beragam dalam dunia profesional',
                    'Kemampuan membangun karir yang bermakna',
                    'Expertise yang dikembangkan melalui praktik dan pengalaman'
                ],
                'strengths' => [
                    'Professional expertise dan industry knowledge',
                    'Network profesional yang kuat',
                    'Kemampuan mentoring dan knowledge transfer'
                ],
                'development_tips' => [
                    'Terus kembangkan expertise di bidang yang dipilih',
                    'Bangun dan pelihara network profesional',
                    'Berbagi pengetahuan dengan generasi berikutnya'
                ],
                'inspirational_figures' => [
                    'Warren Buffett: Building expertise through long-term focus',
                    'Indra Nooyi: Strategic career development and leadership',
                    'Satya Nadella: Career transformation and innovation'
                ],
                'min_score' => 36,
                'max_score' => 50
            ]
        ];

        foreach ($experienceProfiles as $profile) {
            GuidanceContent::create($profile);
        }
    }
}
