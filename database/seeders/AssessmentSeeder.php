<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Assessment;
use App\Models\AssessmentCategory;
use App\Models\Question;

class AssessmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create Abilities Assessment
        $abilitiesAssessment = Assessment::create([
            'name' => 'Abilities Assessment',
            'slug' => 'abilities',
            'description' => 'Asesmen untuk mengevaluasi 6 jenis kemampuan utama',
            'icon' => '🧠',
            'type' => 'abilities',
            'scoring_scale' => json_encode([
                '1' => 'Sangat Tidak Setuju',
                '2' => 'Tidak Setuju',
                '3' => 'Netral',
                '4' => 'Setuju',
                '5' => 'Sangat Setuju'
            ]),
            'scoring_logic' => json_encode([
                'method' => 'category_sum',
                'max_score_per_category' => 50,
                'interpretation' => [
                    ['range' => '10-25', 'level' => 'Kemampuan Dasar'],
                    ['range' => '26-35', 'level' => 'Kemampuan <PERSON>'],
                    ['range' => '36-50', 'level' => 'Kemampuan Unggulan']
                ],
            ]),
            'active' => true
        ]);

        // Create Categories for Abilities Assessment
        $cognitiveCategory = AssessmentCategory::create([
            'assessment_id' => $abilitiesAssessment->id,
            'name' => 'Cognitive Skills',
            'description' => 'Kemampuan berpikir, analisis, dan pemecahan masalah',
            'icon' => '🧠',
            'order' => 1
        ]);

        $socialCategory = AssessmentCategory::create([
            'assessment_id' => $abilitiesAssessment->id,
            'name' => 'Social Skills',
            'description' => 'Kemampuan berinteraksi, berkomunikasi, dan bekerja sama',
            'icon' => '👥',
            'order' => 2
        ]);

        // Create Questions for Cognitive Skills Category
        $cognitiveQuestions = [
            ['question_id' => 'COG1', 'question_text' => 'Saya mudah memahami konsep-konsep rumit'],
            ['question_id' => 'COG2', 'question_text' => 'Saya bisa menganalisis data/informasi dengan baik'],
            ['question_id' => 'COG3', 'question_text' => 'Memecahkan masalah teknis adalah keahlian saya'],
            ['question_id' => 'COG4', 'question_text' => 'Saya cepat menangkap pola atau hubungan antar informasi'],
            ['question_id' => 'COG5', 'question_text' => 'Saya mampu membuat keputusan logis dengan cepat'],
            ['question_id' => 'COG6', 'question_text' => 'Mempelajari teori atau konsep baru mudah bagi saya'],
            ['question_id' => 'COG7', 'question_text' => 'Saya bisa memprediksi konsekuensi dari suatu keputusan'],
            ['question_id' => 'COG8', 'question_text' => 'Saya mahir dalam perencanaan strategis jangka panjang'],
            ['question_id' => 'COG9', 'question_text' => 'Saya mampu fokus pada tugas kompleks dalam waktu lama'],
            ['question_id' => 'COG10', 'question_text' => 'Menggunakan logika untuk menyelesaikan masalah adalah kekuatan saya']
        ];

        foreach ($cognitiveQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $abilitiesAssessment->id,
                'assessment_category_id' => $cognitiveCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🧠',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Social Skills Category
        $socialQuestions = [
            ['question_id' => 'SOC1', 'question_text' => 'Saya mudah menjalin hubungan baru dengan orang'],
            ['question_id' => 'SOC2', 'question_text' => 'Mendengarkan orang lain dengan empati adalah keahlian saya'],
            ['question_id' => 'SOC3', 'question_text' => 'Saya bisa menyelesaikan konflik antar teman/rekan'],
            ['question_id' => 'SOC4', 'question_text' => 'Menyampaikan ide dengan jelas adalah kekuatan saya'],
            ['question_id' => 'SOC5', 'question_text' => 'Saya nyaman berbicara di depan kelompok'],
            ['question_id' => 'SOC6', 'question_text' => 'Memahami perasaan orang lain mudah bagi saya'],
            ['question_id' => 'SOC7', 'question_text' => 'Saya bisa memotivasi orang lain dengan efektif'],
            ['question_id' => 'SOC8', 'question_text' => 'Membangun jaringan profesional adalah keahlian saya'],
            ['question_id' => 'SOC9', 'question_text' => 'Saya pandai menyesuaikan komunikasi dengan lawan bicara'],
            ['question_id' => 'SOC10', 'question_text' => 'Bekerja dalam tim adalah hal yang saya nikmati']
        ];

        foreach ($socialQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $abilitiesAssessment->id,
                'assessment_category_id' => $socialCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '👥',
                'order' => $index + 1
            ]);
        }

        // Create Technical Skills Category
        $technicalCategory = AssessmentCategory::create([
            'assessment_id' => $abilitiesAssessment->id,
            'name' => 'Technical Skills',
            'description' => 'Kemampuan menggunakan alat, teknologi, dan keahlian teknis',
            'icon' => '💻',
            'order' => 3
        ]);

        // Create Questions for Technical Skills Category
        $technicalQuestions = [
            ['question_id' => 'TEC1', 'question_text' => 'Saya cepat menguasai perangkat lunak baru'],
            ['question_id' => 'TEC2', 'question_text' => 'Memperbaiki masalah teknis adalah keahlian saya'],
            ['question_id' => 'TEC3', 'question_text' => 'Saya mahir menggunakan alat-alat digital'],
            ['question_id' => 'TEC4', 'question_text' => 'Memahami spesifikasi teknis mudah bagi saya'],
            ['question_id' => 'TEC5', 'question_text' => 'Saya mampu mempelajari bahasa pemrograman dengan cepat'],
            ['question_id' => 'TEC6', 'question_text' => 'Membuat dokumen teknis adalah hal yang mudah'],
            ['question_id' => 'TEC7', 'question_text' => 'Saya bisa mengoperasikan peralatan elektronik kompleks'],
            ['question_id' => 'TEC8', 'question_text' => 'Memahami diagram teknis tidak sulit bagi saya'],
            ['question_id' => 'TEC9', 'question_text' => 'Saya senang mempelajari teknologi baru'],
            ['question_id' => 'TEC10', 'question_text' => 'Troubleshooting masalah teknis adalah keahlian saya']
        ];

        foreach ($technicalQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $abilitiesAssessment->id,
                'assessment_category_id' => $technicalCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '💻',
                'order' => $index + 1
            ]);
        }

        // Create Management Skills Category
        $managementCategory = AssessmentCategory::create([
            'assessment_id' => $abilitiesAssessment->id,
            'name' => 'Management Skills',
            'description' => 'Kemampuan mengatur, mengorganisir, dan memimpin',
            'icon' => '📊',
            'order' => 4
        ]);

        // Create Questions for Management Skills Category
        $managementQuestions = [
            ['question_id' => 'MGT1', 'question_text' => 'Saya mahir mengatur waktu dan prioritas'],
            ['question_id' => 'MGT2', 'question_text' => 'Memimpin proyek atau tim adalah keahlian saya'],
            ['question_id' => 'MGT3', 'question_text' => 'Saya bisa membuat rencana kerja yang efektif'],
            ['question_id' => 'MGT4', 'question_text' => 'Mendelegasikan tugas dengan tepat adalah kekuatan saya'],
            ['question_id' => 'MGT5', 'question_text' => 'Saya mampu mengelola anggaran atau sumber daya'],
            ['question_id' => 'MGT6', 'question_text' => 'Memantau kemajuan pekerjaan adalah hal yang mudah'],
            ['question_id' => 'MGT7', 'question_text' => 'Saya bisa mengambil keputusan di bawah tekanan'],
            ['question_id' => 'MGT8', 'question_text' => 'Mengorganisir acara atau kegiatan adalah keahlian saya'],
            ['question_id' => 'MGT9', 'question_text' => 'Saya mampu mengevaluasi hasil kerja dengan objektif'],
            ['question_id' => 'MGT10', 'question_text' => 'Memotivasi tim untuk mencapai target adalah keahlian saya']
        ];

        foreach ($managementQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $abilitiesAssessment->id,
                'assessment_category_id' => $managementCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '📊',
                'order' => $index + 1
            ]);
        }

        // Create Creative Skills Category
        $creativeCategory = AssessmentCategory::create([
            'assessment_id' => $abilitiesAssessment->id,
            'name' => 'Creative Skills',
            'description' => 'Kemampuan berkreasi, berinovasi, dan seni',
            'icon' => '🎨',
            'order' => 5
        ]);

        // Create Questions for Creative Skills Category
        $creativeQuestions = [
            ['question_id' => 'CRE1', 'question_text' => 'Saya mudah menghasilkan ide-ide baru'],
            ['question_id' => 'CRE2', 'question_text' => 'Bermain alat musik adalah keahlian saya'],
            ['question_id' => 'CRE3', 'question_text' => 'Menari atau bergerak dengan ritme adalah hal alami'],
            ['question_id' => 'CRE4', 'question_text' => 'Saya mahir dalam seni visual (melukis, menggambar, dll)'],
            ['question_id' => 'CRE5', 'question_text' => 'Menulis cerita atau puisi adalah kegiatan yang saya nikmati'],
            ['question_id' => 'CRE6', 'question_text' => 'Saya bisa melihat solusi yang tidak terlihat oleh orang lain'],
            ['question_id' => 'CRE7', 'question_text' => 'Mendesain sesuatu yang estetis mudah bagi saya'],
            ['question_id' => 'CRE8', 'question_text' => 'Saya mampu mengimprovisasi di situasi tak terduga'],
            ['question_id' => 'CRE9', 'question_text' => 'Menciptakan karya seni adalah hobi saya'],
            ['question_id' => 'CRE10', 'question_text' => 'Saya senang mengeksplorasi bentuk ekspresi baru']
        ];

        foreach ($creativeQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $abilitiesAssessment->id,
                'assessment_category_id' => $creativeCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🎨',
                'order' => $index + 1
            ]);
        }

        // Create Physical Skills Category
        $physicalCategory = AssessmentCategory::create([
            'assessment_id' => $abilitiesAssessment->id,
            'name' => 'Physical Skills',
            'description' => 'Kemampuan fisik, koordinasi, dan ketahanan tubuh',
            'icon' => '🏃',
            'order' => 6
        ]);

        // Create Questions for Physical Skills Category
        $physicalQuestions = [
            ['question_id' => 'PHY1', 'question_text' => 'Saya memiliki koordinasi tubuh yang baik'],
            ['question_id' => 'PHY2', 'question_text' => 'Melakukan aktivitas fisik berat tidak sulit bagi saya'],
            ['question_id' => 'PHY3', 'question_text' => 'Saya mahir dalam setidaknya satu cabang olahraga'],
            ['question_id' => 'PHY4', 'question_text' => 'Memiliki stamina fisik yang baik adalah kelebihan saya'],
            ['question_id' => 'PHY5', 'question_text' => 'Saya cepat mempelajari gerakan fisik baru'],
            ['question_id' => 'PHY6', 'question_text' => 'Ketangkasan tubuh adalah salah satu kekuatan saya'],
            ['question_id' => 'PHY7', 'question_text' => 'Saya bisa mengendalikan tubuh dengan presisi'],
            ['question_id' => 'PHY8', 'question_text' => 'Memiliki refleks yang cepat adalah keahlian saya'],
            ['question_id' => 'PHY9', 'question_text' => 'Saya menikmati aktivitas fisik yang menantang'],
            ['question_id' => 'PHY10', 'question_text' => 'Kesehatan fisik adalah prioritas dalam hidup saya']
        ];

        foreach ($physicalQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $abilitiesAssessment->id,
                'assessment_category_id' => $physicalCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🏃',
                'order' => $index + 1
            ]);
        }

        // Create Heart Assessment
        $heartAssessment = Assessment::create([
            'name' => 'Heart Assessment',
            'slug' => 'heart',
            'description' => 'Asesmen untuk mengidentifikasi minat dan passion utama dalam 7 bidang kunci',
            'icon' => '❤️',
            'type' => 'heart',
            'scoring_scale' => json_encode([
                '1' => 'Tidak Relevan',
                '2' => 'Sedikit Relevan',
                '3' => 'Netral',
                '4' => 'Relevan',
                '5' => 'Sangat Relevan'
            ]),
            'scoring_logic' => json_encode([
                'method' => 'weighted_category_sum',
                'max_score_per_category' => 35,
                'interpretation' => [
                    ['range' => '7-17', 'level' => 'Minat Rendah'],
                    ['range' => '18-27', 'level' => 'Minat Moderat'],
                    ['range' => '28-35', 'level' => 'Passion Inti']
                ],
            ]),
            'active' => true
        ]);

        // Create Categories for Heart Assessment
        $artCategory = AssessmentCategory::create([
            'assessment_id' => $heartAssessment->id,
            'name' => 'Art & Entertainment',
            'description' => 'Minat pada seni, budaya, dan industri hiburan',
            'icon' => '🎨',
            'order' => 1
        ]);

        $businessCategory = AssessmentCategory::create([
            'assessment_id' => $heartAssessment->id,
            'name' => 'Business',
            'description' => 'Minat pada kewirausahaan, manajemen, dan pengembangan bisnis',
            'icon' => '💼',
            'order' => 2
        ]);

        // Create Questions for Art & Entertainment Category
        $artQuestions = [
            ['question_id' => 'AE1', 'question_text' => 'Saya menikmati menciptakan karya seni (lukis, musik, tari, dll)'],
            ['question_id' => 'AE2', 'question_text' => 'Menghadiri pameran seni atau konser memberi saya energi'],
            ['question_id' => 'AE3', 'question_text' => 'Saya mengikuti perkembangan industri film/musik'],
            ['question_id' => 'AE4', 'question_text' => 'Bereksperimen dengan bentuk ekspresi kreatif penting bagi saya'],
            ['question_id' => 'AE5', 'question_text' => 'Saya percaya seni dapat menyampaikan pesan sosial penting'],
            ['question_id' => 'AE6', 'question_text' => 'Mengapresiasi karya seni adalah bagian hidup saya'],
            ['question_id' => 'AE7', 'question_text' => 'Saya sering menggunakan metafora artistik dalam komunikasi']
        ];

        foreach ($artQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $heartAssessment->id,
                'assessment_category_id' => $artCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🎨',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Business Category
        $businessQuestions = [
            ['question_id' => 'BS1', 'question_text' => 'Saya tertarik pada strategi pengembangan bisnis'],
            ['question_id' => 'BS2', 'question_text' => 'Menganalisis model bisnis adalah kegiatan menarik'],
            ['question_id' => 'BS3', 'question_text' => 'Saya menikmati proses membangun jaringan profesional'],
            ['question_id' => 'BS4', 'question_text' => 'Inovasi produk/jasa memicu ide-ide saya'],
            ['question_id' => 'BS5', 'question_text' => 'Saya percaya bisnis dapat menjadi kekuatan perubahan sosial'],
            ['question_id' => 'BS6', 'question_text' => 'Kisah sukses pengusaha menginspirasi saya'],
            ['question_id' => 'BS7', 'question_text' => 'Saya senang memecahkan masalah operasional bisnis']
        ];

        foreach ($businessQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $heartAssessment->id,
                'assessment_category_id' => $businessCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '💼',
                'order' => $index + 1
            ]);
        }

        // Create Communication & Media Category
        $commsCategory = AssessmentCategory::create([
            'assessment_id' => $heartAssessment->id,
            'name' => 'Communication / Media',
            'description' => 'Minat pada jurnalistik, media, dan komunikasi publik',
            'icon' => '📡',
            'order' => 3
        ]);

        // Create Questions for Communication & Media Category
        $commsQuestions = [
            ['question_id' => 'CM1', 'question_text' => 'Saya senang memproduksi konten untuk publik'],
            ['question_id' => 'CM2', 'question_text' => 'Media sosial adalah alat powerful untuk komunikasi'],
            ['question_id' => 'CM3', 'question_text' => 'Menganalisis pesan media adalah kebiasaan saya'],
            ['question_id' => 'CM4', 'question_text' => 'Saya percaya informasi akurat adalah hak masyarakat'],
            ['question_id' => 'CM5', 'question_text' => 'Membuat kampanye komunikasi efektif menarik minat saya'],
            ['question_id' => 'CM6', 'question_text' => 'Saya tertarik pada perkembangan teknologi komunikasi'],
            ['question_id' => 'CM7', 'question_text' => 'Wawancara atau talk show adalah format favorit saya']
        ];

        foreach ($commsQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $heartAssessment->id,
                'assessment_category_id' => $commsCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '📡',
                'order' => $index + 1
            ]);
        }

        // Create Family Category
        $familyCategory = AssessmentCategory::create([
            'assessment_id' => $heartAssessment->id,
            'name' => 'Family',
            'description' => 'Minat pada penguatan keluarga dan hubungan personal',
            'icon' => '👨‍👩‍👧‍👦',
            'order' => 4
        ]);

        // Create Questions for Family Category
        $familyQuestions = [
            ['question_id' => 'FM1', 'question_text' => 'Kualitas waktu keluarga adalah prioritas saya'],
            ['question_id' => 'FM2', 'question_text' => 'Saya aktif dalam kegiatan penguatan hubungan keluarga'],
            ['question_id' => 'FM3', 'question_text' => 'Mewariskan nilai keluarga penting bagi saya'],
            ['question_id' => 'FM4', 'question_text' => 'Saya percaya keluarga adalah fondasi masyarakat sehat'],
            ['question_id' => 'FM5', 'question_text' => 'Menyelesaikan konflik keluarga adalah keterampilan berharga'],
            ['question_id' => 'FM6', 'question_text' => 'Saya tertarik pada dinamika hubungan antar generasi'],
            ['question_id' => 'FM7', 'question_text' => 'Mendidik anak-anak adalah peran yang saya hargai']
        ];

        foreach ($familyQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $heartAssessment->id,
                'assessment_category_id' => $familyCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '👨‍👩‍👧‍👦',
                'order' => $index + 1
            ]);
        }

        // Create Experience Assessment
        $experienceAssessment = Assessment::create([
            'name' => 'Experience Assessment',
            'slug' => 'experience',
            'description' => 'Pemetaan pengalaman hidup melalui lensa pembelajaran dan transformasi',
            'icon' => '🌟',
            'type' => 'experience',
            'active' => true
        ]);

        // Create Categories for Experience Assessment
        $professionalCategory = AssessmentCategory::create([
            'assessment_id' => $experienceAssessment->id,
            'name' => 'Professional Journey',
            'description' => 'Pengalaman dalam dunia kerja profesional',
            'icon' => '💼',
            'order' => 1
        ]);

        $learningCategory = AssessmentCategory::create([
            'assessment_id' => $experienceAssessment->id,
            'name' => 'Learning Odyssey',
            'description' => 'Pengalaman pembelajaran formal dan informal',
            'icon' => '🎓',
            'order' => 2
        ]);

        $lifeCatalystsCategory = AssessmentCategory::create([
            'assessment_id' => $experienceAssessment->id,
            'name' => 'Life Catalysts',
            'description' => 'Momen penting yang menjadi katalis perubahan dalam hidup',
            'icon' => '⚡',
            'order' => 3
        ]);

        // Create Questions for Professional Journey Category
        $professionalQuestions = [
            ['question_id' => 'PJ1', 'question_text' => 'Posisi pekerjaan apa yang paling membentuk cara berpikir Anda?', 'subprompts' => json_encode(['Peran', 'Tahun', '3 Pelajaran Utama'])],
            ['question_id' => 'PJ2', 'question_text' => 'Proyek profesional apa yang membuat Anda paling berkembang?', 'subprompts' => json_encode(['Tantangan Utama', 'Keterampilan Baru', 'Pencapaian'])],
            ['question_id' => 'PJ3', 'question_text' => 'Kesalahan profesional apa yang menjadi guru terbaik?', 'subprompts' => json_encode(['Kesalahan', 'Dampak', 'Perubahan Pola'])]
        ];

        foreach ($professionalQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $experienceAssessment->id,
                'assessment_category_id' => $professionalCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'narrative',
                'subprompts' => $questionData['subprompts'],
                'icon' => '💼',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Learning Odyssey Category
        $learningQuestions = [
            ['question_id' => 'LO1', 'question_text' => 'Pengalaman pendidikan non-formal apa yang paling berdampak?', 'subprompts' => json_encode(['Jenis', 'Durasi', 'Aplikasi Praktis'])],
            ['question_id' => 'LO2', 'question_text' => 'Buku/kursus apa yang mengubah perspektif Anda?', 'subprompts' => json_encode(['Judul', '3 Insight Utama', 'Perubahan Perilaku'])],
            ['question_id' => 'LO3', 'question_text' => 'Siapa mentor terbaik dalam perjalanan Anda?', 'subprompts' => json_encode(['Nama', 'Bidang', 'Prinsip yang Diwariskan'])]
        ];

        foreach ($learningQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $experienceAssessment->id,
                'assessment_category_id' => $learningCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'narrative',
                'subprompts' => $questionData['subprompts'],
                'icon' => '🎓',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Life Catalysts Category
        $lifeCatalystsQuestions = [
            ['question_id' => 'LC1', 'question_text' => 'Momen penting apa yang mengubah arah hidup Anda?', 'subprompts' => json_encode(['Peristiwa', 'Usia saat itu', 'Perubahan yang dihasilkan'])],
            ['question_id' => 'LC2', 'question_text' => 'Keputusan apa yang paling mempengaruhi hidup Anda?', 'subprompts' => json_encode(['Keputusan', 'Alasan', 'Hasil Jangka Panjang'])],
            ['question_id' => 'LC3', 'question_text' => 'Tantangan terbesar apa yang membentuk karakter Anda?', 'subprompts' => json_encode(['Tantangan', 'Cara Mengatasi', 'Pelajaran Hidup'])]
        ];

        foreach ($lifeCatalystsQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $experienceAssessment->id,
                'assessment_category_id' => $lifeCatalystsCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'narrative',
                'subprompts' => $questionData['subprompts'],
                'icon' => '⚡',
                'order' => $index + 1
            ]);
        }
        // Create Strength Assessment
        $strengthAssessment = Assessment::create([
            'name' => 'Strength Assessment',
            'slug' => 'strength',
            'description' => 'Asesmen untuk mengidentifikasi 5 kekuatan inti berdasarkan pola motivasi dan kontribusi alami',
            'icon' => '💪',
            'type' => 'strength',
            'scoring_scale' => json_encode([
                '1' => 'Sangat Tidak Setuju',
                '2' => 'Tidak Setuju',
                '3' => 'Netral',
                '4' => 'Setuju',
                '5' => 'Sangat Setuju'
            ]),
            'scoring_logic' => json_encode([
                'method' => 'category_sum',
                'max_score_per_category' => 50,
                'interpretation' => [
                    ['range' => '10-20', 'level' => 'Rendah'],
                    ['range' => '21-35', 'level' => 'Moderat'],
                    ['range' => '36-50', 'level' => 'Tinggi']
                ],
                'strength_pairs' => [
                    'Visionary Pioneers+Insightful Truth-Seekers' => 'Inovator Sistem',
                    'Inspiring Connectors+Supportive Nurturers' => 'Pengembang Komunitas',
                    'Clarifying Mentors+Insightful Truth-Seekers' => 'Ahli Standar Profesional',
                    'Visionary Pioneers+Inspiring Connectors' => 'Pembuka Peluang',
                    'Supportive Nurturers+Clarifying Mentors' => 'Pelatih Pengembangan'
                ],
                'interpretation_guide' => 'Fokus pada 2 skor tertinggi sebagai core strength utama.'
            ]),
            'active' => true
        ]);

        // Create Categories for Strength Assessment
        $visionaryCategory = AssessmentCategory::create([
            'assessment_id' => $strengthAssessment->id,
            'name' => 'Visionary Pioneers',
            'description' => 'Kemampuan melihat peluang baru dan memulai terobosan',
            'icon' => '🚀',
            'order' => 1
        ]);

        $insightfulCategory = AssessmentCategory::create([
            'assessment_id' => $strengthAssessment->id,
            'name' => 'Insightful Truth-Seekers',
            'description' => 'Kemampuan menemukan inti kebenaran dan prinsip fundamental',
            'icon' => '🔍',
            'order' => 2
        ]);

        $inspiringCategory = AssessmentCategory::create([
            'assessment_id' => $strengthAssessment->id,
            'name' => 'Inspiring Connectors',
            'description' => 'Kemampuan menghubungkan orang dengan ide dan peluang',
            'icon' => '🤝',
            'order' => 3
        ]);

        $supportiveCategory = AssessmentCategory::create([
            'assessment_id' => $strengthAssessment->id,
            'name' => 'Supportive Nurturers',
            'description' => 'Kemampuan mendukung dan mengembangkan potensi orang lain',
            'icon' => '💗',
            'order' => 4
        ]);

        $clarifyingCategory = AssessmentCategory::create([
            'assessment_id' => $strengthAssessment->id,
            'name' => 'Clarifying Mentors',
            'description' => 'Kemampuan menerangkan hal kompleks dan membuat orang memahami',
            'icon' => '�',
            'order' => 5
        ]);

        // Create Questions for Visionary Pioneers Category
        $visionaryQuestions = [
            ['question_id' => 'VP1', 'question_text' => 'Saya mudah melihat potensi di tengah ketidakpastian'],
            ['question_id' => 'VP2', 'question_text' => 'Memulai proyek/produk baru memberi saya energi besar'],
            ['question_id' => 'VP3', 'question_text' => 'Saya nyaman mengambil risiko untuk ide yang saya yakini'],
            ['question_id' => 'VP4', 'question_text' => 'Saya senang membangun sistem dari nol'],
            ['question_id' => 'VP5', 'question_text' => 'Membuka jalur baru lebih menarik daripada mengelola yang ada'],
            ['question_id' => 'VP6', 'question_text' => 'Saya percaya diri memasuki bidang yang belum saya kuasai'],
            ['question_id' => 'VP7', 'question_text' => 'Saya sering menjadi orang pertama mencoba metode baru'],
            ['question_id' => 'VP8', 'question_text' => 'Menggagas perubahan besar membuat saya bersemangat'],
            ['question_id' => 'VP9', 'question_text' => 'Saya melihat masalah sebagai peluang tersembunyi'],
            ['question_id' => 'VP10', 'question_text' => 'Saya termotivasi menciptakan sesuatu yang belum ada sebelumnya']
        ];

        foreach ($visionaryQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $strengthAssessment->id,
                'assessment_category_id' => $visionaryCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🚀',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Insightful Truth-Seekers Category
        $insightfulQuestions = [
            ['question_id' => 'ITS1', 'question_text' => 'Saya selalu mencari akar penyebab di balik permukaan'],
            ['question_id' => 'ITS2', 'question_text' => 'Mempertanyakan asumsi adalah kebiasaan alami saya'],
            ['question_id' => 'ITS3', 'question_text' => 'Saya merasa bertanggung jawab menyampaikan kebenaran yang sulit'],
            ['question_id' => 'ITS4', 'question_text' => 'Prinsip moral lebih penting daripada popularitas'],
            ['question_id' => 'ITS5', 'question_text' => 'Saya melihat pola yang tidak dilihat orang lain'],
            ['question_id' => 'ITS6', 'question_text' => 'Mengoreksi ketidakadilan adalah dorongan hati saya'],
            ['question_id' => 'ITS7', 'question_text' => 'Saya nyaman bekerja dengan konsep abstrak dan filosofis'],
            ['question_id' => 'ITS8', 'question_text' => 'Integritas adalah nilai non-negosiable bagi saya'],
            ['question_id' => 'ITS9', 'question_text' => 'Saya sering menjadi suara penyeimbang dalam diskusi'],
            ['question_id' => 'ITS10', 'question_text' => 'Kedalaman analisis lebih penting daripada kecepatan penyelesaian']
        ];

        foreach ($insightfulQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $strengthAssessment->id,
                'assessment_category_id' => $insightfulCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🔍',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Inspiring Connectors Category
        $inspiringQuestions = [
            ['question_id' => 'IC1', 'question_text' => 'Saya mudah membuat orang antusias dengan visi'],
            ['question_id' => 'IC2', 'question_text' => 'Memperkenalkan orang yang tepat satu sama lain adalah kekuatan saya'],
            ['question_id' => 'IC3', 'question_text' => 'Saya dapat menyampaikan ide kompleks dengan cara menarik'],
            ['question_id' => 'IC4', 'question_text' => 'Menemukan titik temu antar kelompok berbeda adalah keahlian saya'],
            ['question_id' => 'IC5', 'question_text' => 'Saya bisa membuat orang merasakan potensi diri mereka'],
            ['question_id' => 'IC6', 'question_text' => 'Menjembatani kesenjangan komunikasi adalah peran alami saya'],
            ['question_id' => 'IC7', 'question_text' => 'Saya efektif membangun momentum untuk perubahan positif'],
            ['question_id' => 'IC8', 'question_text' => 'Menghubungkan ide berbeda adalah sesuatu yang saya nikmati'],
            ['question_id' => 'IC9', 'question_text' => 'Saya dapat melihat potensi kolaborasi yang tidak terlihat orang lain'],
            ['question_id' => 'IC10', 'question_text' => 'Membangun jejaring lintas bidang adalah kekuatan saya']
        ];

        foreach ($inspiringQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $strengthAssessment->id,
                'assessment_category_id' => $inspiringCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🤝',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Supportive Nurturers Category
        $supportiveQuestions = [
            ['question_id' => 'SN1', 'question_text' => 'Saya senang membantu orang menemukan jalan mereka'],
            ['question_id' => 'SN2', 'question_text' => 'Mengidentifikasi kebutuhan emosional orang adalah kekuatan saya'],
            ['question_id' => 'SN3', 'question_text' => 'Saya berkomitmen pada pertumbuhan jangka panjang orang lain'],
            ['question_id' => 'SN4', 'question_text' => 'Membangun lingkungan yang aman secara emosional adalah prioritas saya'],
            ['question_id' => 'SN5', 'question_text' => 'Saya dapat melihat potensi tersembunyi dalam diri orang'],
            ['question_id' => 'SN6', 'question_text' => 'Menciptakan harmoni dalam kelompok adalah keahlian saya'],
            ['question_id' => 'SN7', 'question_text' => 'Saya mendengarkan lebih banyak daripada berbicara'],
            ['question_id' => 'SN8', 'question_text' => 'Mendampingi orang dalam kesulitan memberi saya makna'],
            ['question_id' => 'SN9', 'question_text' => 'Saya mengingat kebutuhan pribadi dan preferensi orang lain'],
            ['question_id' => 'SN10', 'question_text' => 'Merayakan keberhasilan orang lain membuat saya bahagia']
        ];

        foreach ($supportiveQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $strengthAssessment->id,
                'assessment_category_id' => $supportiveCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '💗',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Clarifying Mentors Category
        $clarifyingQuestions = [
            ['question_id' => 'CLM1', 'question_text' => 'Saya suka menyederhanakan konsep kompleks'],
            ['question_id' => 'CLM2', 'question_text' => 'Mengajarkan keterampilan baru memberi saya kepuasan'],
            ['question_id' => 'CLM3', 'question_text' => 'Saya dapat menyesuaikan penjelasan dengan pemahaman pendengar'],
            ['question_id' => 'CLM4', 'question_text' => 'Menyusun informasi secara terstruktur adalah keahlian saya'],
            ['question_id' => 'CLM5', 'question_text' => 'Saya sering menemukan analogi yang tepat untuk menjelaskan'],
            ['question_id' => 'CLM6', 'question_text' => 'Mendemonstrasikan langkah demi langkah adalah kekuatan saya'],
            ['question_id' => 'CLM7', 'question_text' => 'Saya sabar menghadapi pelajar yang lambat'],
            ['question_id' => 'CLM8', 'question_text' => 'Membuat panduan atau tutorial adalah sesuatu yang saya nikmati'],
            ['question_id' => 'CLM9', 'question_text' => 'Saya dapat melihat kesenjangan pemahaman dan mengisinya'],
            ['question_id' => 'CLM10', 'question_text' => 'Membuat koneksi antar konsep berbeda adalah keahlian saya']
        ];

        foreach ($clarifyingQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $strengthAssessment->id,
                'assessment_category_id' => $clarifyingCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '📚',
                'order' => $index + 1
            ]);
        }

        // Create Personalities Assessment
        $personalitiesAssessment = Assessment::create([
            'name' => 'Personalities Assessment',
            'slug' => 'personalities',
            'description' => 'Asesmen gaya kepribadian dalam 4 dimensi perilaku utama',
            'icon' => '😊',
            'type' => 'personalities',
            'scoring_scale' => json_encode([
                '1' => 'Jarang',
                '2' => 'Kadang-kadang',
                '3' => 'Terkadang',
                '4' => 'Sering',
                '5' => 'Sangat Sering'
            ]),
            'scoring_logic' => json_encode([
                'method' => 'dimension_comparison',
                'dimensions' => [
                    'VD' => 'Visionary Driver',
                    'DC' => 'Dynamic Connector',
                    'AP' => 'Analytical Processor',
                    'SS' => 'Supportive Stabilizer'
                ],
                'personality_profiles' => [
                    'VD-DC' => 'Inspirational Leader',
                    'VD-AP' => 'Strategic Innovator',
                    'VD-SS' => 'Decisive Implementer',
                    'DC-VD' => 'Charismatic Influencer',
                    'DC-AP' => 'Engaging Educator',
                    'DC-SS' => 'Team Harmonizer',
                    'AP-VD' => 'Visionary Analyst',
                    'AP-DC' => 'Articulate Specialist',
                    'AP-SS' => 'Methodical Consultant',
                    'SS-VD' => 'Reliable Executor',
                    'SS-DC' => 'Supportive Coordinator',
                    'SS-AP' => 'Detail-Oriented Guardian'
                ],
                'development_areas' => [
                    'Visionary Driver' => [
                        'Patient Listening',
                        'Detail Management',
                        'Empathetic Response'
                    ],
                    'Dynamic Connector' => [
                        'Focus Maintenance',
                        'Data-Based Decisions',
                        'Task Completion'
                    ],
                    'Analytical Processor' => [
                        'Quick Decision-Making',
                        'Social Engagement',
                        'Emotional Expression'
                    ],
                    'Supportive Stabilizer' => [
                        'Change Management',
                        'Quick Decision-Making',
                        'Assertiveness'
                    ]
                ],
                'instructions' => 'Pilih seberapa sering pernyataan berikut menggambarkan diri Anda'
            ]),
            'active' => true
        ]);

        // Create Categories for Personalities Assessment
        $visionaryDriverCategory = AssessmentCategory::create([
            'assessment_id' => $personalitiesAssessment->id,
            'name' => 'Visionary Driver',
            'description' => 'Berorientasi hasil, Tegas, Pengambil risiko',
            'icon' => '�',
            'order' => 1
        ]);

        $dynamicConnectorCategory = AssessmentCategory::create([
            'assessment_id' => $personalitiesAssessment->id,
            'name' => 'Dynamic Connector',
            'description' => 'Persuasif, Sosial, Optimis',
            'icon' => '🤝',
            'order' => 2
        ]);

        $steadySupporterCategory = AssessmentCategory::create([
            'assessment_id' => $personalitiesAssessment->id,
            'name' => 'Steady Supporter',
            'description' => 'Kolaboratif, Sabar, Stabil',
            'icon' => '🌳',
            'order' => 3
        ]);

        $precisionAnalystCategory = AssessmentCategory::create([
            'assessment_id' => $personalitiesAssessment->id,
            'name' => 'Precision Analyst',
            'description' => 'Sistematis, Akurat, Analitis',
            'icon' => '🔍',
            'order' => 4
        ]);

        // Create Questions for Visionary Driver Category
        $visionaryDriverQuestions = [
            ['question_id' => 'VD1', 'question_text' => 'Saya mengambil inisiatif untuk memulai proyek baru'],
            ['question_id' => 'VD2', 'question_text' => 'Saya merasa nyaman membuat keputusan cepat'],
            ['question_id' => 'VD3', 'question_text' => 'Target yang menantang membuat saya bersemangat'],
            ['question_id' => 'VD4', 'question_text' => 'Saya lebih suka memimpin daripada mengikuti'],
            ['question_id' => 'VD5', 'question_text' => 'Saya tidak takut menghadapi konflik jika diperlukan'],
            ['question_id' => 'VD6', 'question_text' => 'Saya fokus pada gambaran besar daripada detail kecil']
        ];

        foreach ($visionaryDriverQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $personalitiesAssessment->id,
                'assessment_category_id' => $visionaryDriverCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🚀',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Dynamic Connector Category
        $dynamicConnectorQuestions = [
            ['question_id' => 'DC1', 'question_text' => 'Saya mudah memulai percakapan dengan orang baru'],
            ['question_id' => 'DC2', 'question_text' => 'Saya senang mempresentasikan ide di depan kelompok'],
            ['question_id' => 'DC3', 'question_text' => 'Membangun jaringan profesional adalah keahlian saya'],
            ['question_id' => 'DC4', 'question_text' => 'Saya menggunakan cerita untuk membuat poin penting'],
            ['question_id' => 'DC5', 'question_text' => 'Saya bisa meyakinkan orang tentang pendapat saya'],
            ['question_id' => 'DC6', 'question_text' => 'Saya menjaga suasana tetap positif dalam tim']
        ];

        foreach ($dynamicConnectorQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $personalitiesAssessment->id,
                'assessment_category_id' => $dynamicConnectorCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🤝',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Steady Supporter Category
        $steadySupporterQuestions = [
            ['question_id' => 'SS1', 'question_text' => 'Saya lebih memilih kerja tim daripada bekerja sendiri'],
            ['question_id' => 'SS2', 'question_text' => 'Mendengarkan dengan empati adalah kekuatan saya'],
            ['question_id' => 'SS3', 'question_text' => 'Saya menghindari konfrontasi langsung'],
            ['question_id' => 'SS4', 'question_text' => 'Konsistensi lebih penting daripada perubahan cepat'],
            ['question_id' => 'SS5', 'question_text' => 'Saya merasa nyaman dengan tugas rutin'],
            ['question_id' => 'SS6', 'question_text' => 'Membantu orang lain adalah prioritas saya']
        ];

        foreach ($steadySupporterQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $personalitiesAssessment->id,
                'assessment_category_id' => $steadySupporterCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🌳',
                'order' => $index + 1
            ]);
        }

        // Create Questions for Precision Analyst Category
        $precisionAnalystQuestions = [
            ['question_id' => 'PA1', 'question_text' => 'Saya selalu memeriksa detail dengan cermat'],
            ['question_id' => 'PA2', 'question_text' => 'Prosedur yang jelas penting bagi saya'],
            ['question_id' => 'PA3', 'question_text' => 'Saya lebih percaya data daripada perasaan'],
            ['question_id' => 'PA4', 'question_text' => 'Kesalahan kecil bisa mengganggu saya'],
            ['question_id' => 'PA5', 'question_text' => 'Saya lebih memilih kualitas daripada kecepatan'],
            ['question_id' => 'PA6', 'question_text' => 'Saya menghargai ketepatan dalam komunikasi']
        ];

        foreach ($precisionAnalystQuestions as $index => $questionData) {
            Question::create([
                'assessment_id' => $personalitiesAssessment->id,
                'assessment_category_id' => $precisionAnalystCategory->id,
                'question_id' => $questionData['question_id'],
                'question_text' => $questionData['question_text'],
                'question_type' => 'rating',
                'icon' => '🔍',
                'order' => $index + 1
            ]);
        }
    }
}
