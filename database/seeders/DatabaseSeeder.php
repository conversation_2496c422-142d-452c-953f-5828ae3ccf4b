<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        \App\Models\User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);

        \App\Models\User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);

        $this->call([
            AssessmentSeeder::class
        ]);
    }
}
