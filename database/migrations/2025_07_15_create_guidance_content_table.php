<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('guidance_content', function (Blueprint $table) {
            $table->id();
            $table->string('area'); // 'strength', 'heart', 'abilities', 'personality', 'experience'
            $table->string('profile_name'); // e.g., 'Visionary Pioneers', 'Business', etc.
            $table->string('profile_code')->nullable(); // e.g., 'VP', 'BUS', etc.
            $table->text('description'); // Main description
            $table->json('characteristics')->nullable(); // Ciri utama
            $table->json('communication_style')->nullable(); // Gaya komunikasi
            $table->json('strengths')->nullable(); // Kekuatan
            $table->json('weaknesses')->nullable(); // <PERSON>lemahan
            $table->json('team_roles')->nullable(); // <PERSON>an dalam tim
            $table->json('development_tips')->nullable(); // Tips pengembangan
            $table->json('inspirational_figures')->nullable(); // Tokoh inspiratif
            $table->json('creative_ideas')->nullable(); // Ide kreatif
            $table->json('love_language')->nullable(); // Bahasa cinta
            $table->json('hidden_strengths')->nullable(); // Kekuatan tersembunyi
            $table->json('warnings')->nullable(); // Peringatan
            $table->json('combinations')->nullable(); // Kombinasi dengan profil lain
            $table->text('superpower')->nullable(); // Superpower description
            $table->integer('min_score')->default(0); // Minimum score for this profile
            $table->integer('max_score')->default(100); // Maximum score for this profile
            $table->boolean('active')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['area', 'profile_name']);
            $table->index(['area', 'active']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('guidance_content');
    }
};
