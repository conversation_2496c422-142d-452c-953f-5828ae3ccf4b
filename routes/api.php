<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\AssessmentController;
use App\Http\Controllers\API\QuestionController;
use App\Http\Controllers\API\AnswerController;
use App\Http\Controllers\API\ResultController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Assessment public routes (these could be protected in a real app)
Route::get('/assessments', [AssessmentController::class, 'index']);
Route::get('/assessments/{idOrSlug}', [AssessmentController::class, 'show']);
Route::get('/assessments/{assessmentId}/categories', [AssessmentController::class, 'getCategories']);
Route::get('/assessments/{assessmentId}/questions', [QuestionController::class, 'getQuestionsByAssessment']);
Route::get('/categories/{categoryId}/questions', [QuestionController::class, 'getQuestionsByCategory']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // User
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // Answers
    Route::post('/answers', [AnswerController::class, 'submitAnswer']);
    Route::post('/answers/bulk', [AnswerController::class, 'submitMultipleAnswers']);
    Route::get('/assessments/{assessmentId}/answers', [AnswerController::class, 'getUserAnswers']);

    // Results
    Route::post('/assessments/{assessmentId}/process', [ResultController::class, 'processResults']);
    Route::get('/assessments/{assessmentId}/results', [ResultController::class, 'getUserResults']);
    Route::get('/results', [ResultController::class, 'getAllUserResults']);

    // User Assessments
    Route::get('/user/assessments', [AssessmentController::class, 'getUserAssessments']);

    // Assessment Progress
    Route::post('/assessments/{assessmentId}/progress', [AssessmentController::class, 'saveProgress']);
    Route::get('/assessments/{assessmentId}/progress', [AssessmentController::class, 'getProgress']);

    // Token validation
    Route::get('/validate-token', [AuthController::class, 'validateToken']);

    // Admin routes (these would have additional middleware in a real app)
    Route::group(['prefix' => 'admin', 'middleware' => []], function () {
        // Assessments management
        Route::post('/assessments', [AssessmentController::class, 'store']);
        Route::put('/assessments/{id}', [AssessmentController::class, 'update']);
        Route::delete('/assessments/{id}', [AssessmentController::class, 'destroy']);

        // Questions management
        Route::post('/questions', [QuestionController::class, 'store']);
        Route::put('/questions/{id}', [QuestionController::class, 'update']);
        Route::delete('/questions/{id}', [QuestionController::class, 'destroy']);
        Route::post('/questions/bulk', [QuestionController::class, 'bulkCreate']);
    });
});
